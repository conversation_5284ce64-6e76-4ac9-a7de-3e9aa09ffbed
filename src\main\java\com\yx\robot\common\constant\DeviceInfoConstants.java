package com.yx.robot.common.constant;

/**
 * 设备信息相关常量
 * <AUTHOR>
 * @date 2021/04/22
 */
public interface DeviceInfoConstants {
    /**
     * 设备信息
     */
    String DEVICE_INFO = "device_info";

    /**
     * 音量大小
     */
    String VOLUME_SIZE = "volumeSize";

    /**
     * 设备保养时间(单位：年)
     */
    String DEVICE_MAINTENANCE_TIME = "deviceMaintenanceTime";

    /**
     * 设备保养日期
     */
    String DEVICE_MAINTENANCE_DATE = "deviceMaintenanceDate";

    /**
     * 脉冲灯保养时间（单位：小时）
     */
    String ULRAY_MAINTENANCE_TIME = "ulrayMaintenanceTime";

    /**
     * 脉冲灯使用时长(单位：秒)
     */
    String ULRAY_USAGE_TIME = "ulrayUsageTime";

    /**
     * 脉冲灯保养日期
     */
    String ULRAY_MAINTENANCE_DATE = "ulrayMaintenanceDate";

    /**
     * 设备类型：其他
     */
    int DEVICE_TYPE_OTHER = 0;

    /**
     * 其他设备类型：门禁
     */
    int DEVICE_OTHER_ENTRANCE_GUARD = 0;

    /**
     * 选择操作设备操作类型：类型
     */
    int DEVICE_OPERATE_SELECT_TYPE = 0;

    /**
     * 选择操作设备操作类型：控制方式
     */
    int DEVICE_OPERATE_SELECT_CONTROL_METHOD = 1;

    /**
     * 门禁：设置门禁
     */
    int ENTRANCE_GUARD_OPERATION_SET = 3;

    /**
     * 门禁：开门
     */
    int ENTRANCE_GUARD_OPERATION_OPEN = 1;

    /**
     * 门禁:关门
     */
    int ENTRANCE_GUARD_OPERATION_CLOSE = 2;

}
