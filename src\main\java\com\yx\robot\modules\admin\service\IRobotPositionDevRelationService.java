package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotPositionDevRelation;

/**
 * 设备和点位关联信息接口
 *
 * <AUTHOR>
 */
public interface IRobotPositionDevRelationService extends IService<RobotPositionDevRelation> {

    /**
     * 根据位置ID和设备类型获取设备ID
     *
     * @param positionId 位置ID
     * @param type       类型
     * @param subType    子类
     * @return 设备ID
     */
    String getDeviceIdByPositionAndDeviceType(String positionId, Integer type, Integer subType);

    /**
     * 添加关系
     *
     * @param devId      设备ID
     * @param positionId 位置ID
     * @return true:成功，false:失败
     */
    boolean addRelation(String devId, String positionId);

    /**
     * 删除关系，根据positionId
     *
     * @param positionId 位置ID
     * @return true：成功，false:失败
     */
    boolean deleteRelation(String positionId);
}