package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.dao.mapper.RobotSwitchRecordMapper;
import com.yx.robot.modules.admin.entity.RobotSwitchRecord;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.IRobotSwitchRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 机器人开关机记录接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class IRobotSwitchRecordServiceImpl extends ServiceImpl<RobotSwitchRecordMapper, RobotSwitchRecord> implements IRobotSwitchRecordService {

    private static int count = 0;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    /**
     * 处理机器人开关机记录
     *
     * @param action 动作事件
     * @param type   type
     */
    @Override
    public void handleRobotSwitchRecord(Integer action, Integer type) {
        if (count == 0) {
            RobotSwitchRecord robotSwitchRecord = new RobotSwitchRecord();
            robotSwitchRecord.setAction(action);
            robotSwitchRecord.setType(type);
            robotSwitchRecord.setStartTime(new Date());
            robotSwitchRecord.setSyncStatus("0");
            Float percentage = iRobotStatusService.getBatteryPercentage();
            if (ObjectUtil.isNotNull(percentage)) {
                robotSwitchRecord.setPercentage((double) percentage);
            } else {
                // 检测不到电量则默认为100的满电
                robotSwitchRecord.setPercentage(100.0);
            }

            this.save(robotSwitchRecord);
            count++;
        }
    }
}