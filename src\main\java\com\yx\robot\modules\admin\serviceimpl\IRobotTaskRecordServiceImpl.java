package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.constant.ControlStatusConstants;
import com.yx.robot.common.enums.DisinfectTaskType;
import com.yx.robot.common.enums.TaskType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotTaskMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotTaskRecordMapper;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.entity.RobotTaskRecord;
import com.yx.robot.modules.admin.service.IRobotTaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.util.Date;
import java.util.UUID;

import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 机器人任务记录接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotTaskRecordServiceImpl extends ServiceImpl<RobotTaskRecordMapper, RobotTaskRecord> implements IRobotTaskRecordService {

    @Autowired
    private RobotTaskRecordMapper robotTaskRecordMapper;

    @Autowired
    private RobotTaskMapper robotTaskMapper;

    /**
     * 添加任务记录
     *
     * @param robotPositionName 机器人位置名称
     * @param taskId            任务ID
     */
    @Override
    public void saveRobotTaskRecord(String robotPositionName, String taskId) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            RobotTaskRecord robotTaskRecord = new RobotTaskRecord();
            String uuId = UUID.randomUUID().toString();
            jedis.hset(ROBOT_SYS_INFO, LAST_TASK_RECORD_ID, robotTaskRecord.getId());
            robotTaskRecord.setUuId(uuId);
            robotTaskRecord.setStartTime(new Date());
            robotTaskRecord.setTimeConsume(0);
            robotTaskRecord.setEndTime(new Date());
            robotTaskRecord.setRobotLocation(robotPositionName);
            robotTaskRecord.setTaskId(taskId);
            RobotTask robotTask = robotTaskMapper.selectById(taskId);
            if (robotTask.getType().equals(TaskType.CHARGING.getType())) {
                jedis.hset(ROBOT_SYS_INFO, LAST_CHARGING_TASK_RECORD_ID, robotTaskRecord.getId());
            }
            if (robotTask.getType().equals(TaskType.DISINFECT.getType())
                    && DisinfectTaskType.LINE_PATROL_TASK.getType().equals(robotTask.getSubType())) {
                robotTaskRecord.setVideoPath(ControlStatusConstants.VIDEO_PATH);
            }
            robotTaskRecord.setStatus("0");
            robotTaskRecord.setDataStatus("0");
            robotTaskRecord.setSyncStatus("0");
            String o = jedis.hget(ROBOT_SYS_INFO, MILEAGE);
            robotTaskRecord.setMileage(Double.valueOf(StringUtils.isBlank(o) ? "0.0" : o));
            //点位删除导致任务记录无法显示（存在点位为空的记录）---BUG修改；
            if(StrUtil.isNotBlank(robotPositionName)){
                robotTaskRecordMapper.insert(robotTaskRecord);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 更新任务记录
     *
     * @param statusCode
     */
    @Override
    public void updateRobotTaskRecord(Integer statusCode) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String taskRecordId = jedis.hget(ROBOT_SYS_INFO, LAST_TASK_RECORD_ID);
            if (StrUtil.isEmpty(taskRecordId)) {
                return;
            }
            RobotTaskRecord one = robotTaskRecordMapper.selectById(taskRecordId);
            if (one == null) {
                return;
            }
            Date currentDate = new Date();
            one.setEndTime(currentDate);
            String o1 = jedis.hget(ROBOT_SYS_INFO, MILEAGE);
            double taskMileage = 0.0;
            if (StrUtil.isNotBlank(o1)) {
                taskMileage = Double.valueOf(o1) - one.getMileage();
            }
            one.setMileage((double) Math.round(taskMileage * 1000) / 1000);
            one.setTimeConsume((currentDate.getTime() - one.getStartTime().getTime()) / 1000);
            if (statusCode != null) {
                one.setStatus(statusCode.toString());
            }
            one.setDataStatus("1");
            robotTaskRecordMapper.updateById(one);
            jedis.hdel(ROBOT_SYS_INFO, LAST_TASK_RECORD_ID);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }
}