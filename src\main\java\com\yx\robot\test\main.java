package com.yx.robot.test;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.utils.HttpSendUtil;
import com.yx.robot.modules.admin.electrl.constant.ApiConstants;
import com.yx.robot.modules.admin.electrl.constant.ElevatorFactoryConfig;
import com.yx.robot.modules.admin.electrl.constant.ElevatorTopicConstants;
import com.yx.robot.modules.admin.electrl.enums.EncryptTypeEnum;
import com.yx.robot.modules.admin.electrl.msg.*;
import com.yx.robot.modules.admin.electrl.util.ElevatorUtil;
import okhttp3.Response;

import javax.websocket.Session;
import java.net.InetAddress;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class main {

    public static void main(String[] args) throws Exception{
//        String url = ApiConstants.BASE_URL + ApiConstants.DEVELOPER_LOGIN;
//        System.out.println(url);
//        Response response = null;
//        DeveloperLoginReq developerLoginReq = new DeveloperLoginReq();
//        try {
//            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq));
//            String sign = ElevatorUtil.getSDKV3Sign(JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq),Map.class), ElevatorFactoryConfig.APP_SECRET);
//            params.put("sign",sign);
//            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
//            System.out.println(encryptScript);
//            Map<String,String> formParams = new HashMap<>();
//            formParams.put("encryptScript",encryptScript);
//            formParams.put("appId",ElevatorFactoryConfig.APP_ID);
//            formParams.put("encryptType",EncryptTypeEnum.MODE_1.getValue().toString());
//            System.out.println(JSONObject.toJSONString(formParams));
//            response = HttpSendUtil.okhttpPost(url, formParams);
//            if(response != null && response.isSuccessful()) {
//                JSONObject result = JSONObject.parseObject(response.body().string());
//                if(result != null && result.get("encryptScript") != null){
//                    String encryptResultScript = result.get("encryptScript").toString();
//                    String nsout = ElevatorUtil.decryptStr(encryptResultScript,ElevatorFactoryConfig.APP_SECRET);
//                    DeveloperLoginRep developerLoginRep = JSONObject.parseObject(nsout, DeveloperLoginRep.class);
//                    System.out.println(JSONObject.toJSONString(developerLoginRep));
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            if(response != null) {
//                response.body().close();
//            }
//        }

//        String url1 = ApiConstants.BASE_URL + ApiConstants.GET_DEVICE_INFO;
//        Response response1 = null;
//        DeviceInfoReq deviceInfoReq = new DeviceInfoReq();
//
//        try {
//            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(deviceInfoReq));
//            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
//            Map<String,String> formParams = new HashMap<>();
//            formParams.put("encryptScript",encryptScript);
//            formParams.put("appId",ElevatorFactoryConfig.APP_ID);
//            formParams.put("encryptType",EncryptTypeEnum.MODE_1.getValue().toString());
//            String token = "nbq76XwOs9m9CTTw5g5sIEzEJB1Uc05l7xs04VA0eZ2HCUgN0EdUmGiXq+oUkihe";
//            formParams.put("token",token);
//            response1 = HttpSendUtil.okhttpPost(url1, formParams);
//            System.out.println(response1);
//            if(response1 != null && response1.isSuccessful()) {
//                JSONObject result = JSONObject.parseObject(response1.body().string());
//                if(result != null && result.get("encryptScript") != null){
//                    String encryptResultScript = result.get("encryptScript").toString();
//                    String nsout = ElevatorUtil.decryptStr(encryptResultScript,ElevatorFactoryConfig.APP_SECRET);
//                    CallNoninductiveElevatorRep callNoninductiveElevatorRep = JSONObject.parseObject(nsout, CallNoninductiveElevatorRep.class);
//                    System.out.println(JSONObject.toJSONString(callNoninductiveElevatorRep));
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//        } finally {
//            if(response1 != null) {
//                response1.body().close();
//            }
//        }

        String url3 = ApiConstants.BASE_URL + ApiConstants.CALL_ELEVATOR;
        Response response3 = null;
        CallElevatorReq callElevatorReq = new CallElevatorReq();
        callElevatorReq.setFromFloor("3");
        callElevatorReq.setToFloor("4");
        try {
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(callElevatorReq));
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String,String> formParams = new HashMap<>();
            formParams.put("encryptScript",encryptScript);
            formParams.put("appId",ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType",EncryptTypeEnum.MODE_1.getValue().toString());
            formParams.put("token","nbq76XwOs9m9CTTw5g5sIEzEJB1Uc05l7xs04VA0eZ2HCUgN0EdUmGiXq+oUkihe");
            response3 = HttpSendUtil.okhttpPost(url3, formParams);
            if(response3 != null && response3.isSuccessful()) {
                JSONObject result = JSONObject.parseObject(response3.body().string());
                if(result != null && result.get("encryptScript") != null){
                    String encryptResultScript = result.get("encryptScript").toString();
                    String nsout = ElevatorUtil.decryptStr(encryptResultScript,ElevatorFactoryConfig.APP_SECRET);
                    CallElevatorRep callElevatorRep = JSONObject.parseObject(nsout, CallElevatorRep.class);
                    System.out.println(JSONObject.toJSONString(callElevatorRep));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(response3 != null) {
                response3.body().close();
            }
        }

//        String url4 = ApiConstants.BASE_URL + ApiConstants.SEND_OPEN_DOOR;
//        SendOpenDoorReq sendOpenDoorReq = new SendOpenDoorReq();
//        sendOpenDoorReq.setDeviceUnique(ElevatorFactoryConfig.DEVICE_UNIQUE);
//        sendOpenDoorReq.setPosition("1");
//        sendOpenDoorReq.setEffectiveTime("10");
//        JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(sendOpenDoorReq));
//        String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
//        Map<String,String> formParams = new HashMap<>();
//        formParams.put("encryptScript",encryptScript);
//        formParams.put("appId",ElevatorFactoryConfig.APP_ID);
//        formParams.put("encryptType",EncryptTypeEnum.MODE_1.getValue().toString());
//        formParams.put("token","nbq76XwOs9m9CTTw5g5sIEzEJB1Uc05l7xs04VA0eZ2HCUgN0EdUmGiXq+oUkihe");
//        // 防止网络不好执行三次
//        for(int index = 0; index < 3; index++) {
//            Response response4 = HttpSendUtil.okhttpPost(url4, formParams);
//            if(response4 != null && response4.isSuccessful()) {
//                JSONObject result = JSONObject.parseObject(response4.body().string());
//                if(result != null && result.get("encryptScript") != null){
//                    String encryptResultScript = result.get("encryptScript").toString();
//                    String nsout = ElevatorUtil.decryptStr(encryptResultScript,ElevatorFactoryConfig.APP_SECRET);
//                    SendOpenDoorRep sendOpenDoorRep = JSONObject.parseObject(nsout, SendOpenDoorRep.class);
//                    System.out.println(sendOpenDoorRep);
//                }
//            }
//            Thread.sleep(1000);
//        }
    }
}
