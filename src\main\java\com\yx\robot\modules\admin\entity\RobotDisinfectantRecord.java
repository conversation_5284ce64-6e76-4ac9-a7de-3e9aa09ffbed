package com.yx.robot.modules.admin.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_disinfectant_record")
@ApiModel(value = "机器人消毒液变化记录")
public class RobotDisinfectantRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "操作类型（1 进水 -1 出水）")
    private Integer operationType;

    @ApiModelProperty(value = "开始消毒液的量（单位：百分比）")
    private BigDecimal beginQuantity;

    @ApiModelProperty(value = "消毒液开始变化时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date beginChangeTime;

    @ApiModelProperty(value = "结束消毒液的量（单位：百分比）")
    private BigDecimal endQuantity;

    @ApiModelProperty(value = "消毒液结束变化时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endChangeTime;

    @ApiModelProperty(value = "消毒液变化的量(单位：百分比)")
    private BigDecimal changeQuantity;

    @ApiModelProperty(value = "消毒液变化时间消耗(单位：秒)")
    private long consumeTime;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}