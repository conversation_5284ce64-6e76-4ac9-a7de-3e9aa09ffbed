package com.yx.robot.modules.admin.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.dao.mapper.RobotPositionDevRelationMapper;
import com.yx.robot.modules.admin.entity.RobotPositionDevRelation;
import com.yx.robot.modules.admin.service.IRobotPositionDevRelationService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * 设备和点位关联信息接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class IRobotPositionDevRelationServiceImpl extends ServiceImpl<RobotPositionDevRelationMapper, RobotPositionDevRelation> implements IRobotPositionDevRelationService {

    private RobotPositionDevRelationMapper robotPositionDevRelationMapper;

    /**
     * 根据位置ID和设备类型获取设备ID
     *
     * @param positionId 位置ID
     * @param type       类型
     * @param subType    子类
     * @return 设备ID
     */
    @Override
    public String getDeviceIdByPositionAndDeviceType(String positionId, Integer type, Integer subType) {
        Map<String, String> params = new HashMap<>(16);
        params.put("positionId", positionId);
        params.put("type", type.toString());
        params.put("subType", type.toString());
        return robotPositionDevRelationMapper.getDeviceIdByPositionAndDeviceType(params);
    }

    /**
     * 添加关系，添加前先三处
     *
     * @param devId      设备ID
     * @param positionId 位置ID
     * @return true：成功，false；失败
     */
    @Override
    public boolean addRelation(String devId, String positionId) {

        RobotPositionDevRelation robotPositionDevRelation = new RobotPositionDevRelation();
        robotPositionDevRelation.setDevId(devId);
        robotPositionDevRelation.setPositionId(positionId);
        deleteRelation(positionId);
        return robotPositionDevRelationMapper.insert(robotPositionDevRelation) > 0;
    }

    /**
     * 删除关系，根据positionId
     *
     * @param positionId 位置ID
     * @return true：成功，false:失败
     */
    @Override
    public boolean deleteRelation(String positionId) {
        QueryWrapper<RobotPositionDevRelation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("position_id", positionId);
        return robotPositionDevRelationMapper.delete(queryWrapper) > 0;
    }
}