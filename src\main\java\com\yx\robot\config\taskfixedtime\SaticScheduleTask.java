package com.yx.robot.config.taskfixedtime;

import cn.hutool.core.io.FileUtil;
import com.yx.robot.common.enums.RabbitMqMessageType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.scheduler.RabbitMQScheduler;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.serviceimpl.IRobotStatusServiceImpl;
import com.yx.robot.modules.base.utils.ApplicationContextScheduleUtil;
import com.yx.robot.modules.base.utils.LidarControlUtil;
import com.yx.robot.modules.base.utils.SendMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

import java.text.ParseException;

import static com.yx.robot.common.constant.ControlStatusConstants.CONTROL_SLEEP;
import static com.yx.robot.common.constant.ControlStatusConstants.FIXED_TIME_RESTART_ROBOT;
import static com.yx.robot.common.constant.RosWebConstants.FUNCTION_RADAR_SLEEP;
import static com.yx.robot.common.constant.RosWebConstants.ROBOT_FUNCTION;
import static com.yx.robot.modules.base.utils.LidarControlUtil.END_CHARING;

/**
 * <AUTHOR>
 * @date 2022/6/23
 * description：用于定时删除相机抓拍的图片
 */
@Configuration
@EnableScheduling
@Slf4j
public class SaticScheduleTask {
    /**
     * 添加定时任务：每天上午10点30分执行任务
     * 至于为什么不使用配置文件路径赋值，是因为赋值不上，所以直接写路径
     */
    @Scheduled(cron = "0 30 10 ? * *")
    private void deleteDeepImage() {
        log.warn("开始清空深度相机文件夹");
        //清空所有的深度照片
        boolean clean1 = FileUtil.clean("/home/<USER>/udrive_v1_1_1/ws/compressImage");
        boolean clean2 = FileUtil.clean("/home/<USER>/udrive_v1_1_1/ws/image");
        if (clean1 == true && clean2 == true) {
            log.warn("深度相机文件夹已经清空");
        } else {
            log.warn("深度相机文件夹清空失败");
        }
    }

    /**
     * 每月15日上午10:15发送一次保养信息:每月15日上午10:15发送一次
     */
    @Scheduled(cron = "0 15 10 15 * ?")
    private void sendProtectShortMessage() {
        SendMessageUtil sendMessageUtil = (SendMessageUtil) ApplicationContextScheduleUtil.getBean("SendMessageUtil");
        sendMessageUtil.sendShortMessage("需要保养了");
    }

    /**
     * 每个七天发送一次保养信息:每个星期三中午12点发送一次
     */
    @Scheduled(cron = "0 0 12 ? * WED")
    private void sendProtectMessage() {
        RabbitMQScheduler rabbitMQScheduler = (RabbitMQScheduler) ApplicationContextScheduleUtil.getBean("RabbitMQScheduler");
        rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.PROTECTING.getType());
    }

    /**
     * 从1月开始每隔两个月，15日上午10:15发送电池充电定期维护信息
     */
    @Scheduled(cron = "0 15 10 15 1/2 ?")
    private void sendBatteryMessage() {
        RabbitMQScheduler rabbitMQScheduler = (RabbitMQScheduler) ApplicationContextScheduleUtil.getBean("RabbitMQScheduler");
        rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.BATTERY_CHARING_REMIND.getType());
    }

    /**
     * 每隔30秒检测一次是否在充电，如果在充电就关闭雷达
     */
    @Scheduled(cron = "*/30 * * * * ?")
    private void stopLidar() throws InterruptedException, ParseException {
        String isOpen = RedisUtil.getHash(ROBOT_FUNCTION, FUNCTION_RADAR_SLEEP);
        if (!Boolean.TRUE.toString().equals(isOpen)) {
//            log.info("机器休眠功能未开启");
            return;
        } else {
//            log.info("机器休眠功能已开启");
        }
        IRobotStatusServiceImpl iRobotStatusService = (IRobotStatusServiceImpl) ApplicationContextScheduleUtil.getBean("IRobotStatusServiceImpl");
        LidarControlUtil lidarControlUtil = (LidarControlUtil) ApplicationContextScheduleUtil.getBean("LidarControlUtil");
        boolean charging1 = iRobotStatusService.isDock();
        if (charging1) {
            Boolean sleepChargeState = true;
            Boolean sleepUserChargeState = true;
            Integer sleepChargeCtrl = 0;
            String lidarStatue1 = lidarControlUtil.returnLiadrStatue();
            String depthStatue1 = lidarControlUtil.returnDepthStatue();
            //如果雷达已经关闭并且30分钟内有定时任务，则开启雷达
            log.info("60分钟内有定时任务1:" + lidarControlUtil.checkFixTimeTask(60));
            if (LidarControlUtil.S00000.equals(lidarStatue1) && LidarControlUtil.S00000.equals(depthStatue1)) {
                //如果雷达没关闭则休眠雷达
                log.info("雷达和深度开启状态");
                //间隔3分钟，判断是否仍处于充电桩
                while (sleepChargeState){
                    Thread.sleep(1000);
                    if(iRobotStatusService.isDock()){
                        sleepChargeCtrl++;
                    }else {
                        log.info("中途退出充电状态，重新定义休眠时间");
                        return;
                    }
                    if(sleepChargeCtrl.equals(180)){
                        sleepChargeState = false;
                    }
                }
                //如果此前有手动控制休眠的，再间隔10分钟再检查休眠；
                if (CONTROL_SLEEP) {
                    while (sleepUserChargeState){
                        Thread.sleep(1000);
                        if(iRobotStatusService.isDock()){
                            sleepChargeCtrl++;
                        }else {
                            log.info("存在用户手动控制情况下，中途退出充电状态，重新定义休眠时间");
                            CONTROL_SLEEP = false;
                            return;
                        }
                        if(sleepChargeCtrl.equals(780)){
                            sleepUserChargeState = false;
                        }
                    }
                    CONTROL_SLEEP = false;
                }
                boolean charging2 = iRobotStatusService.isDock();
                if (charging2) {
                    log.info("雷达和深度准备休眠");
                    String lidarStatue2 = lidarControlUtil.returnLiadrStatue();
                    String depthStatue2 = lidarControlUtil.returnDepthStatue();
                    //如果雷达没有关闭并且60分钟内没有定时任务，则关闭雷达
                    log.info("systemCheck2.lidar:" + lidarStatue2);
                    log.info("systemCheck2.depth_camera:" + depthStatue2);
                    log.info("60分钟内有定时任务2:" + lidarControlUtil.checkFixTimeTask(60));
                    if (LidarControlUtil.S00000.equals(lidarStatue2) && LidarControlUtil.S00000.equals(depthStatue2) && !lidarControlUtil.checkFixTimeTask(60)) {
                    //如果未脱离充电桩，则可以休眠
                        if (!END_CHARING) {
                            boolean b = lidarControlUtil.handleLidar(false);
                            if (b) {
                                log.warn("雷达和深度已休眠");
                            }
                        }
                    }
                }
            }
        }else {
            // 如果不在充电桩上进入了休眠，则开启
            String lidarStatue = lidarControlUtil.returnLiadrStatue();
            String depthStatue = lidarControlUtil.returnDepthStatue();
            if (LidarControlUtil.SLEEP.equals(lidarStatue) && LidarControlUtil.SLEEP.equals(depthStatue)) {
                log.info("机器未在充电情况下进入休眠后，解锁休眠！");
                lidarControlUtil.handleLidar(true);
            }
        }
    }
}
