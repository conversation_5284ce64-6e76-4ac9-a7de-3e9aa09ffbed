package com.yx.robot.modules.admin.dto;

import lombok.Data;

/**
 * 机器人设备数据dto
 * <AUTHOR>
 * @date
 */
@Data
public class YxRobotDeviceDataDto {

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 地图id
     */
    private String mapId;

    /**
     * 地图名称
     */
    private String mapName;

    /**
     * 地图数据
     */
    private String robotMapData;

    /**
     * 标点数据
     */
    private String robotPositionData;

    /**
     * 位置区域映射数据
     */
    private String robotLocationData;

    /**
     * 路径数据
     */
    private String robotRouteData;

    /**
     * 世界坐标数据
     */
    private String robotWorldPositionData;

    /**
     * 任务数据
     */
    private String robotTaskData;

    /**
     * 消毒数据
     */
    private String 	robotDisinfectData;

    /**
     * 区域数据
     */
    private String robotAreasData;

    /**
     * 标签数据
     */
    private String robotTagData;
}
