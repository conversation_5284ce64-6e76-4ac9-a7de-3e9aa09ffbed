package com.yx.robot.modules.admin.service;

/**
 * 电机控制服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/1 11:27
 */
public interface IRobotMotorService {

    /**
     * 设置电机是否休眠，且操作只能交替发送
     * <p>
     * 如果正在充电状态，且没有发送过休眠指令则发送休眠
     * 如果没有在在充电，且没有发送过唤醒指令则发送唤醒指令
     * <p>
     * 和ros建立通讯，将消息发送到ros
     * <p>
     * 电机充电成功时，设置电机休眠。
     * 电机未充电时，设置电机唤醒。
     * 电机脱离充电桩前，设置电机唤醒
     *
     * @param isNotSleep false:休眠，true:唤醒
     * @return true:设置成功， false:设置失败
     */
    boolean setMotorSleep(Boolean isNotSleep);

    /**
     * 电机操作
     *
     * @param operate 控制电机(true)/不控制(false)
     * @return true:操作成功,false:操作失败
     */
    boolean motorControl(boolean operate);

    /**
     * 获取急停开关状态
     *
     * @return true 被按下  false 没被按下
     */
    Boolean getEmergencyState();
}
