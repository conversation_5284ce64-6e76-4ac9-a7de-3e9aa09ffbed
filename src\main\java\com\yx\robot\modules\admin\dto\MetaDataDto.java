package com.yx.robot.modules.admin.dto;

import cn.hutool.core.util.ObjectUtil;
import com.yx.robot.modules.admin.message._MapMetadata;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/1 8:45
 */
@Data
public class MetaDataDto {

    private double resolution;
    private int height;
    private PositionDto position;

    public MetaDataDto() {

    }

    public MetaDataDto(_MapMetadata mapMetadata) {
        if (ObjectUtil.isNull(mapMetadata)) {
            return;
        }
        resolution = mapMetadata.resolution;
        height = mapMetadata.height;
        PositionDto positionDto = new PositionDto();
        positionDto.setX(mapMetadata.origin.position.x);
        positionDto.setY(mapMetadata.origin.position.y);
        position = positionDto;
    }

}
