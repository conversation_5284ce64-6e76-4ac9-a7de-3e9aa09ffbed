package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.ChargingCondition;
import com.yx.robot.common.enums.RabbitMqMessageType;
import com.yx.robot.common.enums.RobotSwitchType;
import com.yx.robot.common.enums.SceneType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.message._BatteryState;
import com.yx.robot.modules.admin.message._Bool;
import com.yx.robot.modules.admin.message._Uint8;
import com.yx.robot.modules.admin.service.IRobotChargingService;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.IRobotSwitchRecordService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.SysMonitor;
import com.yx.robot.modules.base.utils.LidarControlUtil;
import com.yx.robot.modules.base.utils.SendMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.yx.robot.common.constant.ControlStatusConstants.CREATE_MAP_STATUE;
import static com.yx.robot.common.constant.ControlStatusConstants.IS_NEED_GO_CHARGING_LOW_POWER;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.StatusConstants.*;

/**
 * 系统故障检测（低电量）
 *
 * <AUTHOR>
 * @date 2020-2-17
 */
@Component
@Slf4j
@Order(3)
public class RobotHealthScheduler implements CommandLineRunner {

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1, new BasicThreadFactory.Builder().namingPattern("scheduled-RobotHealth-%d").daemon(true).build());

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IRobotSwitchRecordService iRobotSwitchRecordService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private RabbitMQScheduler rabbitMQScheduler;

    @Autowired
    private SendMessageUtil sendMessageUtil;

    /**
     * 为了防止多次触发，每个方法只能执行一次
     */
    private static long count0 = 0;

    /**
     * 为了防止多次触发，每个方法只能执行一次
     */
    private static long count1 = 0;

    /**
     * 为了防止多次触发，每个方法只能执行一次
     */
    private static long count2 = 0;

    /**
     * 为了防止多次触发，每个方法只能执行一次
     */
    private static boolean flag3 = true;

    /**
     * 低电量短信发送控制条件
     */
    private static boolean lowBatteryMessageCtrl = true;

    /**
     * 关机语音发送控制条件
     */
    public static Boolean shoutdownCtrl = false;

    @Override
    public void run(String... args) {
        executorService.scheduleWithFixedDelay(this::handleRobotHealth, 0, 30, TimeUnit.SECONDS);
        executorService.scheduleWithFixedDelay(() -> {
            // 关机状态检测
            try {
                handleSystemChargeCheck();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, 0, 100, TimeUnit.MILLISECONDS);
        log.info("机器人系统健康检测中......");
    }

    private void handleRobotHealth() {
        // 处理低电量
        handleLowerBattery();
        // 系统监控
        handleSysMonitor();
    }

    /**
     * 关机状态检测
     */
    private void handleSystemChargeCheck() throws Exception {
        String systemChargeResult = redisTemplate.opsForValue().get(TOPIC + "::" + TopicConstants.SYSTEM_CHARGE);
        if (StrUtil.isNotEmpty(systemChargeResult)) {
            _Uint8 uint8 = JSON.parseObject(systemChargeResult, _Uint8.class);
            if (Objects.requireNonNull(uint8).data == 1) {
                iRobotSwitchRecordService.handleRobotSwitchRecord(0, RobotSwitchType.MANUAL.getType());
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, PRE_SHUTDOWN, "true");
                rosWebService.sendVoicePrompt(SceneType.SHUTDOWN_PROCESS, null);
                shoutdownCtrl = true;
                Thread.sleep(5000);
            }
        }
    }

    /**
     * 系统监控
     */
    private void handleSysMonitor() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            SysMonitor sysMonitor = new SysMonitor();
            jedis.set(MONITOR_INFO, JSONObject.toJSONString(sysMonitor));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 低电量逻辑处理
     */
    private void handleLowerBattery() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            _BatteryState batteryState = iRobotStatusService.getBatteryState();
            if (null != batteryState) {
                float batteryEletricRemain = batteryState.percentage;
                float voltage = batteryState.voltage;
                //电量达到80%执行恢复任务
                if (batteryEletricRemain >= BATTERY_POWER_THRESHOLD_CONTINUE_TASK.floatValue()) {
                    count0++;
                    if (count0 <= 10 && count0 > 0) {
                        rosWebService.recoveryTask();
                        count0 = 11;
                    }
                } else {
                    count0 = 0;
                }
                //当电量大于25%时重置短信发送条件
                if (batteryEletricRemain > BATTERY_POWER_CTRL.floatValue()) {
                    lowBatteryMessageCtrl = true;
                }
                //电量大于15并且小于20的时候根据任务空闲状态自动充电
                if (batteryEletricRemain <= BATTERY_POWER_THRESHOLD_WARNING.floatValue()
                        && batteryEletricRemain > BATTERY_POWER_THRESHOLD_FORCE_CHARGE.floatValue()) {
                    if (lowBatteryMessageCtrl) {
                        sendMessageUtil.sendShortMessage("电量低于20%");
                        lowBatteryMessageCtrl = false;
                    }
                    boolean idle = rosWebService.isIdle();
                    log.warn("电量大于15并且小于20的时候根据任务空闲状态自动充电count1:{},当前电量:{}", count1, batteryEletricRemain);
                    log.info("IS_NEED_GO_CHARGING_LOW_POWER:{}", IS_NEED_GO_CHARGING_LOW_POWER);
                    if (IS_NEED_GO_CHARGING_LOW_POWER && idle && !CREATE_MAP_STATUE) {
                        if (!iRobotStatusService.getPositionStatus()) {
                            log.warn("定位丢失，无法执行，自动充电操作");
                            return;
                        }
                        log.warn("符合空闲状态下返回充电条件！");
                        IS_NEED_GO_CHARGING_LOW_POWER = Boolean.FALSE;
                        // 防止开机自动去充电
                        Thread.sleep(1000 * 30);
                        // 尝试充电次数
                        int times = 5;
                        while (!iRobotStatusService.isDock() && times > 0) {
                            times--;
                            // 先停止任务
                            rosWebService.taskControl1(null, "2");
                            // 再取消任务
                            rosWebService.taskControl1(null, "4");
                            jedis.hset(ROBOT_SYS_INFO, CHARGING_CONDITION, ChargingCondition.AUTO_CHARGING_LOWER_BATTERY.getType().toString());
                            log.warn("15%-20%电量空闲状态下返回充电！！！");
                            rosWebService.gotoCharging();
                            ThreadUtil.sleep(1000 * 120);
                        }
                    }
                } else {
                    IS_NEED_GO_CHARGING_LOW_POWER = Boolean.TRUE;
                }
                //电量大于10并且小于15的时候强制自动充电
                //低电量触发
                boolean isLowestEletric = batteryEletricRemain <= BATTERY_POWER_THRESHOLD_FORCE_CHARGE.floatValue()
                        && batteryEletricRemain > BATTERY_POWER_THRESHOLD_CLOSE.floatValue();
                //低电压触发
                boolean isLowestVoltage = voltage <= BATTERY_POWER_MINIMUM_VOLTAGE.floatValue();
                if (isLowestEletric || isLowestVoltage) {
                    count2++;
                    if (isLowestEletric) {
                        log.warn("电量大于10并且小于15的时候强制自动充电count2:{},当前电量:{},充电状态：{}", count2, batteryEletricRemain, iRobotStatusService.isDock());
                    }
                    if (isLowestVoltage) {
                        log.warn("电压太低：{}，触发返回充电任务", voltage);
                    }
                    if (count2 <= 10 && count2 > 0 && !iRobotStatusService.isDock()) {
                        // 防止开机自动去充电
                        Thread.sleep(1000 * 30);
                        jedis.del(LAST_TO_DO_LIST);
                        List<String> allToDoList = jedis.lrange(TO_DO_LIST, 0, -1);
                        if (CollectionUtil.isNotEmpty(allToDoList) && Boolean.TRUE.toString()
                                .equalsIgnoreCase(RedisUtil.getHash(ROBOT_SYS_INFO, IS_CONTINUE_TASK))) {
                            jedis.lpush(LAST_TO_DO_LIST, allToDoList.toArray(new String[allToDoList.size()]));
                        }
                        // 先停止任务
                        rosWebService.taskControl1(null, "2");
                        // 再取消任务
                        rosWebService.taskControl1(null, "4");
                        jedis.hset(ROBOT_SYS_INFO, CHARGING_CONDITION, ChargingCondition.AUTO_CHARGING_LOWER_BATTERY.getType().toString());
                        rosWebService.gotoCharging();
                        count2 = 11;
                    }
                } else {
                    count2 = 0;
                }
                // 电量低于10的时候通知自动关机
                // 判断脉冲灯的一个状态（如果是脉冲引起的低电量问题，则不关机）
                boolean pulseStatus = false;
                String pulseStatusStr = jedis.get(TOPIC + "::" + TopicConstants.PULSE_STATUS);
                if (StrUtil.isNotBlank(pulseStatusStr)) {
                    _Bool bool = JSONObject.parseObject(pulseStatusStr, _Bool.class);
                    pulseStatus = bool.data;
                }
                if (batteryEletricRemain <= BATTERY_POWER_THRESHOLD_CLOSE.floatValue() && !pulseStatus) {
                    log.warn("电量低于10的时候通知自动关机,当前电量:{}", batteryEletricRemain);
                    // 电量低于百分之10的时候三分钟之后自动关机
                    Thread.sleep(1000 * 60 * 3);
                    boolean charging = iRobotStatusService.isDock();
                    if (!charging) {
                        if (flag3) {
                            iRobotSwitchRecordService.handleRobotSwitchRecord(0, RobotSwitchType.LOW_BATTERY.getType());
                            rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.LOW_BATTERY_SHOUT_DOWN.getType());
                            rosWebService.notifyRosShutdown();
                            flag3 = false;
                        }
                    }
                } else {
                    flag3 = true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }
}
