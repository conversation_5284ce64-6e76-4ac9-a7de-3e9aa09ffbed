package com.yx.robot.modules.admin.message;

import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/19 21:48
 */
@Data
@MessageType(string = "geometry_msgs/Twist")
public class _DirectionalControl {
    /**
     * 只有x生效；
     * 前后，x起作用，x>0 向前，x<0向后。 运行速度不要超过0.8
     */
    public Linear linear;

    /**
     * 控制机器人旋转数据，只有z参数有效；
     * z 左右，z<0 向右，z>0 向左. 旋转速度不要超过 1
     */
    public Angular angular;

    public void setValue(float x, float z) {
        linear = new Linear();
        linear.x = x;
        angular = new Angular();
        angular.z = z;
    }
}