package com.yx.robot.test.ctl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.message._NavControlReq;
import com.yx.robot.modules.admin.message._Pose;
import com.yx.robot.modules.admin.scheduler.RobotTaskScheduler;
import com.yx.robot.modules.admin.service.IRobotRouteService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import redis.clients.jedis.Jedis;

import java.util.List;

import static com.yx.robot.common.enums.TaskType.DISINFECT;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/26 14:27
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class NavTest {

    @Autowired
    private IRobotRouteService iRobotRouteService;

    @Autowired
    private RobotTaskScheduler robotTaskScheduler;

    @Test
    public void test() {
        System.out.println(moveLineStartPoint());
    }

    public Boolean moveLineStartPoint() {
        String routeId = "564237986711998464";
        String routeLine = iRobotRouteService.getById(routeId).getPositions();
        List<_Pose> poseList = JSONObject.parseArray(routeLine, _Pose.class);
        _Pose startPoint = poseList.get(0);
        Jedis jedis = RedisUtil.getJedis();
        RobotTask robotTask = new RobotTask();
        robotTask.setType(DISINFECT.getType());

        _NavControlReq req = new _NavControlReq();
        req.cmd = 1;
        req.pose_name = "路线起始点";
        req.nav_mode = 0;
        req.pose_info = startPoint;
        try {
            String position = JSON.toJSONString(req);
            System.out.println(position);
            int s1 = robotTaskScheduler.sendNavPrompt(jedis, position, robotTask, null);
            System.out.println(s1);
            return 0 == s1;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return false;
    }
}
