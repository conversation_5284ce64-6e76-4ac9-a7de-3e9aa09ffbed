package com.yx.robot.config.wesocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@ServerEndpoint("/yx/ws/ros/{sid}")
@Component
@Slf4j
@EnableScheduling
public class WebSocketRosServer {

    /**
     * 静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
     */
    private static int onlineCount = 0;
    /**
     * concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
     */
    private static CopyOnWriteArraySet<WebSocketRosServer> webSocketSet = new CopyOnWriteArraySet<WebSocketRosServer>();

    /**
     * 与某个客户端的连接会话，需要通过它来给客户端发送数据
     */
    private Session session;
    private static Map<String, Session> sessions = new ConcurrentHashMap<>();

    private static ArrayBlockingQueue<WsQueueMsg> msgQueue = new ArrayBlockingQueue<>(5000);

    private AtomicBoolean isDone = new AtomicBoolean(false);

    /**
     * 连接建立成功调用的方法
     *
     * @param session
     * @param sid
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("sid") String sid) {
        try {
            this.session = session;
            sessions.put(sid, session);
            //加入set中
            webSocketSet.add(this);
            //在线数加1
            addOnlineCount();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        //从set中删除
        webSocketSet.remove(this);
        //在线数减1
        subOnlineCount();
        sessions.remove(session);
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {

        log.info("接收到的消息:{}", message);
        //群发消息
        msgQueue.offer(new WsQueueMsg(sessions.keySet(), message));

//        for (WebSocketRosServer item : webSocketSet) {
//            try {
//                item.sendMessage(session, message);
//            } catch (IOException e) {
//                e.printStackTrace();
//            }
//        }
    }

    /**
     * 关闭连接
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误:{}", error.getMessage());
        log.error("session.getRequestURI:{}", session.getRequestURI());
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(Session sessionTemp, String message) throws IOException {
        if (null != sessionTemp) {
            synchronized (sessionTemp) {
                sessionTemp.getBasicRemote().sendText(message);
            }
        }
    }

    public void sendAllMessage(String message, String name) throws IOException {
//        for (String sid : sessions.keySet()) {
        msgQueue.offer(new WsQueueMsg(sessions.keySet(), message));
//            Session sessionTmp = sessions.get(sid);
//            synchronized (sessionTmp) {
//                if (sessionTmp.isOpen()) {
//                    try {
//                        isDone.getAndSet(true);
//                        sessionTmp.getBasicRemote().sendText(message);
//                        isDone.getAndSet(false);
//                    } catch (Exception e) {
//                        isDone.getAndSet(false);
//                        log.error(e.getMessage());
//                    }
//                }
//            }
//        }
    }

    public static synchronized void addOnlineCount() {
        WebSocketRosServer.onlineCount++;
        log.info("当前在线人数：" + WebSocketRosServer.onlineCount);
    }

    public static synchronized void subOnlineCount() {
        WebSocketRosServer.onlineCount--;
        log.info("当前在线人数：" + WebSocketRosServer.onlineCount);
    }

    public int getOnlineCount() {
        return onlineCount;
    }

    @Scheduled(fixedDelay = 2)
    public void sendHandlerServer() {
        try {
            WsQueueMsg msg = msgQueue.take();
//            log.info("send:{}", msg.getMsg());
            for (String sid : msg.getSids()) {
                try {
                    Session sessionTmp = sessions.get(sid);
                    if (sessionTmp.isOpen()) {
                        sessionTmp.getBasicRemote().sendText(msg.getMsg().toString());
                    }
                } catch (Exception e) {
                    Set<String> s = new HashSet<>();
                    s.add(sid);
                    msgQueue.offer(new WsQueueMsg(s, msg.getMsg()));
                    log.error(e.getMessage());
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }
}
