package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yx.robot.common.constant.ControlStatusConstants;
import com.yx.robot.common.constant.DeviceInfoConstants;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.hk.HkUtils;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.electrl.service.ElevatorTaskService;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.jike.dto.RobotWorkStatusDto;
import com.yx.robot.modules.admin.jike.enums.TaskStatusEnum;
import com.yx.robot.modules.admin.jike.service.JikePlatformService;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.JPushService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.base.utils.SendMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;

import static com.yx.robot.common.constant.ControlStatusConstants.*;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.*;
import static com.yx.robot.common.enums.NavType.*;
import static com.yx.robot.common.enums.TaskType.DISINFECT;

@Component
@Slf4j
@Order(2)
public class RobotTaskScheduler implements CommandLineRunner {

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1, new BasicThreadFactory.Builder().namingPattern("scheduled-RobotTask-%d").daemon(true).build());

    @Autowired
    private IRobotTaskService robotTaskService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private IRobotLocationService iRobotLocationService;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Autowired
    private IRobotTaskRecordService iRobotTaskRecordService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotRouteService iRobotRouteService;

    @Autowired
    private IRobotDisinfectTaskDetailService iRobotDisinfectTaskDetailService;

    @Autowired
    private IRobotChargingRecordService iRobotChargingRecordService;

    @Autowired
    private IRobotEntranceGuardService iRobotEntranceGuardService;

    @Autowired
    private ElevatorTaskService elevatorTaskService;

    @Autowired
    private JikePlatformService jikePlatformService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Autowired
    private RabbitMQScheduler rabbitMQScheduler;

    @Autowired
    private SendMessageUtil sendMessageUtil;

    @Autowired
    private IRobotAreasService iRobotAreasService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${platform.jike.enable}")
    private boolean jikeEnable;
    /**
     * 梯控模块是否可用
     */
    @Value("${platform.electrl.enable}")
    private boolean electrlEnable;

    @Autowired
    private JPushService jPushService;

    private static Timer timer1 = null;

    private static Timer timer2 = null;

    private static boolean flag1 = true;

    private static boolean flag2 = true;

    private static boolean flag3 = true;

    private static boolean flag4 = true;

    /**
     * 路径规划失败控制条件
     */
    public static boolean pathPlanningFailedMessage = true;

    @Override
    public void run(String... args) throws Exception {
        executorService.execute(() -> {
            while (true) {
                try {
                    normalTask();
                    routeTask();
                    Thread.sleep(1000);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        });
        log.info("机器人任务调度执行中......");
    }

    /**
     * 路径消毒
     */
    private void routeTask() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            Long routeToDoListSize = jedis.llen(ROUTE_TO_DO_LIST);
            if (!(routeToDoListSize > 0)) {
                ROBOT_LINE_EXEC_STATUS = 0;
                return;
            }
            List<String> all = jedis.lrange(ROUTE_TO_DO_LIST, 0, -1);
            Collections.reverse(all);
            String toDoContent = jedis.lindex(ROUTE_TO_DO_LIST, routeToDoListSize - 1);
            String[] toDoContentArr = toDoContent.split("#");
            // 任务id
            String taskId = toDoContentArr[0];
            String robotRouteId = toDoContentArr[1];
            RobotRoute robotRoute = iRobotRouteService.getById(robotRouteId);
            if (ObjectUtil.isNull(robotRoute)) {
                log.info("消毒路线不存在:{}，跳过", robotRoute);
                jedis.rpop(ROUTE_TO_DO_LIST);
                return;
            }
            String name = robotRoute.getLocationInfo().concat(robotRoute.getLocationCode());
            RobotTask robotTask = robotTaskService.getById(taskId);
            if (!robotTaskService.isExecutableTime(robotTask)) {
                rosWebService.taskControl1(taskId, TaskOperationType.STOP.getOperation().toString());
                rosWebService.taskControl1(taskId, TaskOperationType.CANCEL.getOperation().toString());
                log.warn("任务不可以执行，执行返回充电任务");
                if (!iRobotStatusService.isDock()) {
                    rosWebService.gotoCharging();
                }
                return;
            }
            //消毒任务（清空消毒任务信息）
            if (robotTask.getType().equals(DISINFECT.getType())) {
                preHandleDisinfect(jedis, robotTask, robotRouteId);
            }
            boolean b2 = checkTaskStatus(jedis);
            if (!b2) {
                if (IS_ROBOT_LINE_EXEC_STATUS_CANCEL == 0) {
                    log.info("删除消毒路劲");
                    jedis.rpop(ROUTE_TO_DO_LIST);
                }
                return;
            }
            // 充电和防撞条状态检测
            boolean b = checkStatus(jedis);
            if (!b) {
                return;
            }
            if (flag2) {
                //电机上锁
                iRobotMotorService.motorControl(true);
                if (null != robotTask) {
                    iRobotTaskRecordService.saveRobotTaskRecord(name, taskId);
                }
                flag2 = !flag2;
            }
            // 导航到起始点
            if (ROBOT_LINE_EXEC_STATUS == 0 && !moveLineStartPoint(robotRouteId)) {
                log.info("导肮到线路的起始点失败");
                return;
            }
            ROBOT_LINE_EXEC_STATUS = 1;
            while (true) {
                ThreadUtil.sleep(2000);
                Integer statusCode = rosWebService.getStatusCode();
                if (NAV_DOING_TYPE.getType().equals(statusCode)) {
                    continue;
                }
                if (statusCode.equals(NAV_FINISH_TYPE.getType()) || statusCode.equals(NAV_FINISH_NEARBY_TYPE.getType())) {
                    log.info("到达路线起点");
                } else {
                    log.info("导航失败:{}", statusCode);
                    ROBOT_LINE_EXEC_STATUS = 0;
                    return;
                }
                String currentId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                if (StringUtil.isBlank(currentId)) {
                    ROBOT_LINE_EXEC_STATUS = 0;
                    return;
                }
                break;
            }
            rosWebService.syncSpeedLevel();
            boolean b1 = sendRoutePrompt(robotRouteId);
            if (!b1) {
                return;
            }
            //设置任务类型
            settingTaskType(jedis, robotTask.getType());
            flag3 = true;
            while (true) {
                try {
                    routeToDoListSize = jedis.llen(ROUTE_TO_DO_LIST);
                    if (routeToDoListSize == 0) {
                        log.warn("没有任务信息了");
                        break;
                    }
                    if (flag3) {
                        // 触发路径消毒设置
                        handleDisinfect(jedis, robotTask, robotRouteId, NavType.NAV_FINISH_TYPE.getType().toString());
                        flag3 = false;
                    }
                    long l = System.currentTimeMillis();
                    if (StringUtil.isEmpty(jedis.hget(TASK_INFO, ROUTE_DISINFECT_START_TIME))) {
                        jedis.hset(TASK_INFO, ROUTE_DISINFECT_START_TIME, l + "");
                    }
                    // 获取消毒模式状态
                    String s = RedisUtil.getTopicValue(DOCTOR_MODE_STATE);
                    if (StrUtil.isNotBlank(s)) {
                        _DoctorModeStatus doctorModeStatus = JSONObject.parseObject(s, _DoctorModeStatus.class);
                        // 1开始 2暂停  3继续 4停止 5完成
                        int doctorState = doctorModeStatus.doctorState;
                        if (doctorState == 2) {
                            if (LocalDateTime.now().getSecond() % 2 == 0 && System.currentTimeMillis()%1000 == 0) {
                                log.warn("巡线消毒模式暂停中......");
                            }
                            //  此处添加点阵表情---工作被打扰;---暂停
                            rosWebService.publishUtil(ExpressionType.WORK_DISTURBED.getValue());
                            jedis.hdel(TASK_INFO, ROUTE_DISINFECT_START_TIME);
                            flag4 = true;
                        }
                        if (doctorState == 3) {
                            if (LocalDateTime.now().getSecond() % 2 == 0 && System.currentTimeMillis()%1000 == 0) {
                                log.info("巡线消毒模式继续中......");
                            }
                            if (flag4) {
                                log.info("巡线消毒模式继续中......");
                                handleDisinfect(jedis, robotTask, robotRouteId, NavType.NAV_FINISH_TYPE.getType().toString()); // 触发路径消毒设置
                                flag4 = false;
                            }
                        }
                        if (doctorState == 2 || doctorState == 4 || doctorState == 5) {
                            if (null != timer1) {
                                timer1.cancel();
                                timer1 = null;
                            }
                        } else {
                            if (null == timer1) {
                                timer1 = new Timer();
                                timer1.schedule(new TimerTask() {
                                    @Override
                                    public void run() {
                                        //运动过程中表情和语音交互
                                        workingInteraction();
                                    }
                                }, 0, 15000);
                            }
                        }
                        if (doctorState == 5) {
                            log.info("巡线消毒模式完成" + doctorState);
                            handleRouteTaskFinish(jedis, robotTask);
                            ROBOT_LINE_EXEC_STATUS = 0;
                            //更新任务
                            iRobotTaskRecordService.updateRobotTaskRecord(NavType.NAV_FINISH_TYPE.getType());
                            break;
                        }
                        if (doctorState == EMERGENCY_MAKE_TYPE.getType()) {
                            log.warn("急停开关被摁下");
                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            Thread.sleep(5000);
                            break;
                        } else if (doctorState == COLLISION_MAKE_TYPE.getType()) {
                            log.warn("防撞条被触碰");
                            sendMessageUtil.sendShortMessage("执行任务防撞条被触碰");
                            rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.COLLISION_STRIP_TRIGGER.getType());
                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            Thread.sleep(5000);
                            break;
                        }
//                        else if (doctorState == LOCATION_MAKE_TYPE.getType()) {
//                            log.warn("定位问题");
//                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
//                            rosWebService.sendVoicePrompt(SceneType.DEVIATION_ROUTE, null);
//                            log.warn("巡线任务，定位问题，设置定位丢失");
//                            jedis.hset(ROBOT_SYS_INFO, POSITION_STATUS, FAIL);
//                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
//                            Thread.sleep(10000);
//                            break;
//                        }
                        else if (doctorState == NODES_MAKE_TYPE.getType()) {
                            log.warn("节点错误");
                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
                            rosWebService.sendVoicePrompt(SceneType.SYS_ERROR, null);
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            Thread.sleep(5000);
                            break;
                        } else if (doctorState == MOTOR_MAKE.getType()) {
                            log.warn("电机未使能");
                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
                            rosWebService.sendVoicePrompt(SceneType.SYS_ERROR, null);
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            Thread.sleep(5000);
                            break;
                        } else if (doctorState == CLOSE_OBSTACLE_MAKE.getType() || doctorState == PLAN_MAKE_TYPE.getType()) {
                            log.warn("靠近障碍物导致局部路径规划失败");
                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
                            rosWebService.sendVoicePrompt(SceneType.PLAN_PROCESS, null);
                            if (pathPlanningFailedMessage) {
                                sendMessageUtil.sendShortMessage("靠近障碍物导致局部路径规划失败");
                                pathPlanningFailedMessage = false;
                            }
//                            rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.PATH_PLANNING_FAILED.getType());
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            Result<Boolean> booleanResult = rosWebService.gotoCharging();
                            if (!booleanResult.isSuccess()) {
                                rosWebService.gotoOrigin();
                            }
                            Thread.sleep(10000);
                            break;
                        } else if (doctorState == DEPTH_MAKE_TYPE.getType()) {
                            log.warn("深度问题");
                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
                            rosWebService.sendVoicePrompt(SceneType.SYS_ERROR, null);
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            Thread.sleep(5000);
                            break;
                        } else if (doctorState == MANUAL_CANCEL_TYPE.getType()) {
                            log.warn("手动取消");
                            iRobotTaskRecordService.updateRobotTaskRecord(doctorState);
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            break;
                        }
                    }
                    pathPlanningFailedMessage = true;
                    Thread.sleep(50);
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 普通消毒 常规任务
     */
    private void normalTask() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            // 判断是否有任务队列
            long toDoListSize = jedis.llen(TO_DO_LIST);
            if (!(toDoListSize > 0)) {
                return;
            }
            List<String> all = jedis.lrange(TO_DO_LIST, 0, -1);
            Collections.reverse(all);
            String toDoContent = jedis.lindex(TO_DO_LIST, toDoListSize - 1);
            String[] toDoContentArr = toDoContent.split("#");
            // 任务id
            String taskId = toDoContentArr[0];
            RobotTask robotTask = robotTaskService.getById(taskId);
            if (robotTask == null) {
                log.warn("无法查找到任务:任务ID：{},toDoContent:{}", taskId, toDoContent);
                return;
            }
            if (!robotTaskService.isExecutableTime(robotTask)) {
                rosWebService.taskControl1(taskId, TaskOperationType.STOP.getOperation().toString());
                rosWebService.taskControl1(taskId, TaskOperationType.CANCEL.getOperation().toString());
                log.warn("任务不可以执行，执行返回充电任务");
                if (!iRobotStatusService.isDock()) {
                    rosWebService.gotoCharging();
                }
                return;
            }
            // 点位id
            String positionId = toDoContentArr[1];
            // 点位名称
            RobotPosition robotPosition = iRobotPositionService.getById(positionId);
            if (ObjectUtil.isNull(robotPosition)) {
                log.info("消毒点不存在:{},跳过该点", positionId);
                jedis.rpop(TO_DO_LIST);
                return;
            }
            // 针对门禁的处理
            String robotPositionName = robotPosition.getName();
            if (robotPosition.getType().equals(RobotPositionType.INSIDE_ENTRANCE_GUARD.getType())) {
                robotPositionName = robotPositionName.concat(RobotPositionType.INSIDE_ENTRANCE_GUARD.getValue());
            }
            if (robotPosition.getType().equals(RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType())) {
                robotPositionName = robotPositionName.concat(RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getValue());
            }
            // 针对消毒仓的处理开始
            if (robotPosition.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_1.getType())) {
                robotPositionName = robotPositionName.concat(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_1.getValue());
            }
            if (robotPosition.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_2.getType())) {
                robotPositionName = robotPositionName.concat(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_2.getValue());
            }
            if (robotPosition.getType().equals(RobotPositionType.INSIDE_DISINFECT_BOX_POSITION.getType())) {
                robotPositionName = robotPositionName.concat(RobotPositionType.INSIDE_DISINFECT_BOX_POSITION.getValue());
            }
            // 针对消毒仓的处理结束
            String position = toDoContentArr[toDoContentArr.length - 1];
            //获取下一个位置区域和位置编号
            RobotLocation robotLocation = preHandleTask(jedis, positionId);
            //消毒任务（清空消毒任务信息）
            if (robotTask.getType().equals(DISINFECT.getType())) {
                preHandleDisinfect(jedis, robotTask, robotLocation.getId());
            }
            // 充电和防撞条状态检测
            boolean b = checkStatus(jedis);
            if (!b) {
                log.info("充电状态 | 防撞条 | 急停开关异常");
                return;
            }
            boolean b3 = checkTaskStatus(jedis);
            if (!b3) {
                rosWebService.publishUtil(ExpressionType.WORK_DISTURBED.getValue());
                log.info("任务暂停 | 未运行");
                return;
            }
            // 查找目标点需要经过的门禁区域
            iRobotEntranceGuardService.checkLocalRouteThrough(positionId);
            if (!robotTask.getType().equals(TaskType.ENTRANCE_GUARD.getType()) && StrUtil.isNotBlank(jedis.get(ENTRANCE_GUARD_ID))) {
                String entranceGuardId = jedis.get(ENTRANCE_GUARD_ID);
                rosWebService.startEntranceGuardTask(entranceGuardId);
                jedis.del(ENTRANCE_GUARD_ID);
                log.info("即将通过门禁区域");
                return;
            }
            if (electrlEnable) {
                // 查找目标点是否需要执行梯控服务
                elevatorTaskService.hasElevatorPromise(positionId);
                String elevatorPositionIds = jedis.get(ELEVATOR_POSITION_ID);
                if (!robotTask.getType().equals(TaskType.ELEVATOR.getType()) && StrUtil.isNotBlank(elevatorPositionIds)) {
                    elevatorTaskService.startElevatorTask(elevatorPositionIds);
                    jedis.del(ELEVATOR_POSITION_ID);
                    log.info("要开始梯控任务");
                    return;
                }
            }
            //电机上锁 门和灯光全关 添加任务记录
            if (flag1) {
                //电机上锁
                iRobotMotorService.motorControl(true);
                //判断喷雾水箱状态
                if (null != robotTask) {
                    iRobotTaskRecordService.saveRobotTaskRecord(robotPositionName, taskId);
                }
                flag1 = !flag1;
            }
            jedis.hset(TASK_INFO, NEXT_TARGET, robotPositionName);
            rosWebService.syncSpeedLevel();
            // 发送导航请求
            String topicResult = RedisUtil.getTopicValue(NAV_STATUS);
            if(!topicResult.isEmpty()){
                _RobotNavStatus robotNavStatus = JSON.parseObject(topicResult, _RobotNavStatus.class);
                if(NAV_FINISH_NEARBY_TYPE.getType().equals(robotNavStatus.statusCode) || NAV_FINISH_TYPE.getType().equals(robotNavStatus.statusCode)){
                    robotNavStatus.statusCode = NAV_DOING_TYPE.getType();
                    log.warn("开始导航前对状态码进行覆盖！");
                    redisTemplate.opsForValue().set(TOPIC + "::" + TopicConstants.NAV_STATUS,JSONObject.toJSONString(robotNavStatus));
                }
            }
            int ret = sendNavPrompt(jedis, position, robotTask, positionId);
            if (ret != 0) {
                return;
            }
            //设置任务类型
            settingTaskType(jedis, robotTask.getType());

            boolean flag2 = true;
            boolean flag3 = true;
            while (true) {
                try {
                    //任务状态检测
                    boolean res = checkTaskStatus(jedis);
                    if (!res) {
                        break;
                    }
                    //获取导航状态码
                    Integer statusCode = rosWebService.getStatusCode();
                    //导航进行中
                    if (statusCode.equals(NAV_DOING_TYPE.getType())) {
                        if (null == timer1) {
                            timer1 = new Timer();
                            timer1.schedule(new TimerTask() {
                                @Override
                                public void run() {
                                    //运动过程中表情和语音交互
                                    workingInteraction();
                                }
                            }, 0, 15000);
                        }
                        //运动过程中处理消毒业务逻辑只执行一次
                        if (flag2) {
                            if (robotTask.getType().equals(DISINFECT.getType())
                                    || robotTask.getType().equals(TaskType.ORIGIN.getType())
                                    || robotTask.getType().equals(TaskType.CHARGING.getType())
                                    || robotTask.getType().equals(TaskType.ENTRANCE_GUARD.getType())
                            ) {
                                String robotLocationId = robotLocation == null ? null : robotLocation.getId();
                                handleDisinfect(jedis, robotTask, robotLocationId, statusCode.toString());
                            }
                            flag2 = false;
                        }
                    } else if (statusCode.equals(NAV_FINISH_TYPE.getType()) || statusCode.equals(NAV_FINISH_NEARBY_TYPE.getType())) {
                        if (flag3) {
                            //电机解锁
                            log.info("到达目的或者到达目标点附近");
                            iRobotMotorService.motorControl(false);
                            flag3 = !flag3;
                        }
                        // 到达充电点
                        if (robotTask.getName().equals(TaskType.CHARGING.getValue())) {
                            //到达充电桩导航点，关闭防跌落功能
                            log.warn("到达充电桩导航点，关闭防跌落功能!");
                            String fall = RedisUtil.getHash(ROBOT_FUNCTION, FALL_DOWN_STATE);
                            if(StrUtil.isNotBlank(fall) && !Boolean.valueOf(fall)){
                                log.warn("云平台已关闭防跌落功能，不再自动关闭");
                            }else {
                                iRobotAreasService.fallControl(false);
                            }
                            rosWebService.autoDock();
                            //清空地图
                            rosWebService.clearMap();
                        }
                        // 到达原点
                        if (robotTask.getName().equals(TaskType.ORIGIN.getValue())) {
                            //清空地图
                            rosWebService.clearMap();
                        }
                        // 到达消毒点或原点
                        if (robotTask.getType().equals(DISINFECT.getType()) || robotTask.getType().equals(TaskType.ORIGIN.getType()) || robotTask.getType().equals(TaskType.CHARGING.getType())
                        ) {
                            String robotLocationId = robotLocation == null ? null : robotLocation.getId();
                            handleDisinfect(jedis, robotTask, robotLocationId, statusCode.toString());
                        }
                        // 到达门禁点
                        if (robotTask.getType().equals(TaskType.ENTRANCE_GUARD.getType())) {
                            String entrancePositionId = jedis.get(ENTRANCE_POSITION_ID);
                            if (StrUtil.isNotBlank(entrancePositionId) && entrancePositionId.equals(positionId)) {
                                log.info("到达第一个门禁点");
                                try {
                                    res = iRobotEntranceGuardService.threadOpenDoor(positionId, null);
                                    if (!res) {
                                        log.warn("再次尝试穿越门禁行为");
                                        break;
                                    }
                                } catch (Exception e) {
                                    e.printStackTrace();
                                }
                            } else {
                                log.info("到达第二个门禁点");
                                res = iRobotEntranceGuardService.handleArriveEntranceGuardPositionAndSaveRecord(new Short(DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_CLOSE + ""), positionId, null);
                                jedis.del(ENTRANCE_POSITION_ID);
                            }
                        }
                        // 到达消毒仓停靠点
//                            if(robotTask.getType().equals(TaskType.DISINFECT_BOX.getType())) {
//                                log.info("到达消毒仓停靠点");
//                                res = iRobotDisinfectBoxService.handleArriveDisinfectBoxPosition(taskId,positionId);
//                                if(robotPosition.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_2.getType()) && res) {
//                                    Result<Boolean> booleanResult = rosWebService.gotoCharging();
//                                    if(!booleanResult.isSuccess()) {
//                                        rosWebService.gotoOrigin();
//                                    }
//                                }
//                            }
                        jedis.rpop(TO_DO_LIST);
                        toDoListSize = jedis.llen(TO_DO_LIST);
                        //任务状态检测
                        Integer respCode = null;
                        // 到达电梯停靠点
                        if (electrlEnable) {
                            if (robotTask.getType().equals(TaskType.ELEVATOR.getType())) {
                                log.info("到达电梯停靠点");
                                respCode = elevatorTaskService.handleArriveElevatorPosition(robotPosition);
                            }
                        }
                        boolean res1 = checkTaskStatus(jedis);
                        if (!res1) {
                            break;
                        }
                        if (toDoListSize == 0) {
                            Thread.sleep(1500);
                            rosWebService.clearTaskInfo1();
                            if (electrlEnable) {
                                if (robotTask.getType().equals(TaskType.ELEVATOR.getType())) {
                                    if (respCode == 2) {
                                        elevatorTaskService.handleOutElevator();
                                    }
                                }
                            }
                            // 消毒仓任务完毕返回充电
                            if (robotTask.getType().equals(TaskType.DISINFECT_BOX.getType())) {
                                Result<Boolean> booleanResult = rosWebService.gotoCharging();
                                if (!booleanResult.isSuccess()) {
                                    rosWebService.gotoOrigin();
                                }
                            }
                            // 消毒任务完毕返回原点
                            if (robotTask.getType().equals(DISINFECT.getType())) {
                                String loops = jedis.hget(TASK_INFO, LOOPS + ":" + taskId);

                                if (StrUtil.isBlank(loops)) {
                                    if (null != robotTask.getLoops()) {
                                        jedis.hset(TASK_INFO, LOOPS + ":" + taskId, robotTask.getLoops().toString());
                                    } else {
                                        // 没有设置循环次数，默认一次
                                        jedis.hset(TASK_INFO, LOOPS + ":" + taskId, "1");
                                    }
                                }
                                loops = jedis.hget(TASK_INFO, LOOPS + ":" + taskId);
                                log.info("任务名称" + robotTask);
                                log.info("任务次数" + robotTask.getLoops());
                                log.info("当前循环次数" + loops);
                                if (loops != null) {
                                    if (Integer.valueOf(loops) > 1) {
                                        jedis.hset(TASK_INFO, LOOPS + ":" + taskId, (Integer.valueOf(loops) - 1) + "");
                                        rosWebService.startDisinfectService(robotTask.getId());
                                    } else {
//                                            // 开始消毒仓时间
//                                            log.info("开始执行消毒仓任务");
                                        if (jikeEnable) {
                                            RobotWorkStatusDto robotWorkStatusDto = new RobotWorkStatusDto();
                                            robotWorkStatusDto.setTaskStatus(TaskStatusEnum.BACK_ORIGIN.getValue());
                                            jikePlatformService.handleRobotWorkStatus(robotWorkStatusDto);
                                        }

//                                            iRobotDisinfectBoxService.startDisinfectBoxTask();
                                        Result<Boolean> booleanResult = rosWebService.gotoCharging();
                                        if (!booleanResult.isSuccess()) {
                                            rosWebService.gotoOrigin();
                                        }
                                    }
                                } else {
                                    // 先检测状态
                                    b = checkTaskStatus(jedis);
                                    if (b) {
                                        rosWebService.startDisinfectService(robotTask.getId());
                                    }
                                }
                            }
                        }
                        log.info("到达：" + robotPositionName + "导航点");
                        //到达充电桩导航点后重置控制条件
                        pathPlanningFailedMessage = true;
                        break;
                    } else {
                        if (null != timer1) {
                            timer1.cancel();
                            timer1 = null;
                        }
                        if (jikeEnable) {
                            RobotWorkStatusDto robotWorkStatusDto = new RobotWorkStatusDto();
                            robotWorkStatusDto.setTaskStatus(TaskStatusEnum.STOP.getValue());
                            jikePlatformService.handleRobotWorkStatus(robotWorkStatusDto);
                        }
                        if (statusCode.equals(EMERGENCY_MAKE_TYPE.getType())) {
                            log.warn("急停开关被摁下");
//                            jedis.hset(TASK_INFO, IS_RUNNING, "false");
                            Thread.sleep(5000);
                            break;
                        }
                        if (statusCode.equals(COLLISION_MAKE_TYPE.getType())) {
                            log.warn("防撞条触碰");
                            jedis.hset(TASK_INFO, IS_RUNNING, "false");
                            Thread.sleep(5000);
                            break;
                        }
//                        if (statusCode.equals(LOCATION_MAKE_TYPE.getType())) {
//                            log.warn("定位问题");
//                            jedis.hset(TASK_INFO, IS_RUNNING, "false");
//                            log.info("定点任务检查 ，定位问题,设置定位失败");
//                            rosWebService.sendVoicePrompt(SceneType.DEVIATION_ROUTE, null);
//                            jedis.hset(ROBOT_SYS_INFO, POSITION_STATUS, FAIL);
//                            Thread.sleep(5000);
//                            break;
//                        }
                        if (statusCode.equals(NODES_MAKE_TYPE.getType())) {
                            log.warn("节点错误");
                            jedis.hset(TASK_INFO, IS_RUNNING, "false");
                            rosWebService.sendVoicePrompt(SceneType.SYS_ERROR, null);
                            Thread.sleep(5000);
                            break;
                        }
                        if (statusCode.equals(MOTOR_MAKE.getType())) {
                            log.warn("电机未使能");
                            jedis.hset(TASK_INFO, IS_RUNNING, "false");
                            rosWebService.sendVoicePrompt(SceneType.SYS_ERROR, null);
                            Thread.sleep(5000);
                            break;
                        }
                        if (statusCode.equals(PLAN_MAKE_TYPE.getType()) || statusCode.equals(CLOSE_OBSTACLE_MAKE.getType())) {
                            log.warn("路径规划失败");
                            rosWebService.sendVoicePrompt(SceneType.PLAN_PROCESS, null);
                            if (pathPlanningFailedMessage) {
//                                sendMessageUtil.sendShortMessage("路径规划失败");
                                pathPlanningFailedMessage = false;
                            }
//                            rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.PATH_PLANNING_FAILED.getType());
                            MsgPushTemplate msgPushTemplate = jPushService.defineValue(MsgPushTemplate.PLAN_FAIL,
                                    robotPositionName);
                            //直接跳过当前点位
                            jedis.rpop(TO_DO_LIST);
                            toDoListSize = jedis.llen(TO_DO_LIST);
                            if (toDoListSize == 0) {
                                Result<Boolean> booleanResult = rosWebService.gotoCharging();
                                if (!booleanResult.isSuccess()) {
                                    rosWebService.gotoOrigin();
                                }
                            }
                            if (robotTask.getType().equals(TaskType.ELEVATOR.getType())) {
                                log.info("梯控任务路径规划失败，需要执行相关业务逻辑");
                            }
                            Thread.sleep(5000);
                            break;
                        }
                        if (statusCode.equals(DEPTH_MAKE_TYPE.getType())) {
                            log.warn("深度问题");
                            jedis.hset(TASK_INFO, IS_RUNNING, "false");
                            rosWebService.sendVoicePrompt(SceneType.SYS_ERROR, null);
                            Thread.sleep(5000);
                            break;
                        }
                        if (statusCode.equals(MANUAL_CANCEL_TYPE.getType())) {
                            log.warn("手动取消，导航状态码："+ 4088);
                            log.warn("此处注释了将is_running改为false的逻辑，因为会导致无法恢复任务！");
//                            jedis.hset(TASK_INFO, IS_RUNNING, "false");
                            break;
                        }
                    }
                    pathPlanningFailedMessage = true;
                    Thread.sleep(1000);
                } catch (Exception e) {
                    e.printStackTrace();
                    break;
                }
            }
            //更新任务
            iRobotTaskRecordService.updateRobotTaskRecord(rosWebService.getStatusCode());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 运动过程中表情和语音交互
     */
    private void workingInteraction() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            if (jedis == null) {
                return;
            }
            String taskType = jedis.hget(TASK_INFO, TASK_TYPE);
            if (StrUtil.isNotEmpty(taskType)) {
                Integer type = Integer.valueOf(taskType);
                if (type.equals(DISINFECT.getType())) {
                    //消毒过程中
                    //发布工作中的点阵表情话题---工作中
                    rosWebService.publishUtil(ExpressionType.WORKING.getValue());
                    ControlStatusConstants.EXPRESSION_PRIORITY.working = true;
                    rosWebService.sendVoicePrompt(SceneType.DISINFECT_PROCESS, null);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 消毒业务逻辑处理
     *
     * @param jedis           jedis
     * @param robotTask       机器人任务
     * @param robotLocationId 机器人位置ID
     * @param statusCode      状态码
     */
    private void handleDisinfect(Jedis jedis, RobotTask robotTask, String robotLocationId, String statusCode) {
        //任务状态检测
        boolean res1 = checkTaskStatus(jedis);
        if (!res1) {
            return;
        }
        try {
            //紫外线状态
            boolean ulrayStatus = iRobotStatusService.isUltravioletStatus();
            //喷雾状态
            boolean sprayStatus = iRobotStatusService.isSprayStatus();
            //风扇状态
            boolean fanStatus = iRobotStatusService.isFanStatus();

            // 脉冲状态
            boolean pulseStatus = iRobotStatusService.isPulseStatus();

            //沿途紫外线
            Object ulrayObj = jedis.hget(ROBOT_SYS_INFO, ULRAY);
            //沿途紫外线
            boolean wayUlray = ObjectUtil.isNotNull(ulrayObj) && Boolean.parseBoolean(ulrayObj.toString());
            //沿途喷雾
            Object sprayObj = jedis.hget(ROBOT_SYS_INFO, SPRAY);
            boolean waySpray = ObjectUtil.isNotNull(sprayObj) && Boolean.parseBoolean(sprayObj.toString());
            //沿途速度
            Object disinfectSpeed = jedis.hget(ROBOT_SYS_INFO, DISINFECT_SPEED);
            if (robotTask.getType().equals(TaskType.CHARGING.getType()) || robotTask.getType().equals(TaskType.ORIGIN.getType())) {
                rosWebService.closeAllDisinfect();
                return;
            }
            //添加任务记录id
            String lastTaskRecordId = jedis.hget(ROBOT_SYS_INFO, LAST_TASK_RECORD_ID);
            RobotDisinfect robotDisinfect = null;
            if (robotLocationId != null) {
                List<RobotDisinfect> robotDisinfects = null;
                if (robotTask.getType().equals(DISINFECT.getType())
                        && robotTask.getSubType().equals(DisinfectTaskType.LOCAL_TASK.getType())) {
                    String disinfectIds = RedisUtil.getHash(TASK_INFO, DISINFECT_POINT_LOCAL_TASK);
                    if (StringUtils.isBlank(disinfectIds)) {
                        disinfectIds = "-1";
                    }
                    // 跨楼层问题
                    robotDisinfects = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>()
                            .eq(RobotDisinfect::getRobotLocationId, robotLocationId)
                            .in(RobotDisinfect::getId, Arrays.asList(StringUtils.split(disinfectIds, ","))));
                } else {
                    robotDisinfects = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>()
                            .eq(RobotDisinfect::getRobotLocationId, robotLocationId)
                            .eq(RobotDisinfect::getRobotTaskId, robotTask.getId()));
                }
                if (CollectionUtil.isNotEmpty(robotDisinfects)) {
                    robotDisinfect = robotDisinfects.get(0);
                }
            }
            RobotDisinfectTaskDetail robotDisinfectTaskDetail = null;
            if (robotDisinfect != null) {
                log.info("robotDisinfect:{}", JSON.toJSONString(robotDisinfect));
                RobotTaskRecord robotTaskRecord = iRobotTaskRecordService.getById(lastTaskRecordId);
                robotDisinfectTaskDetail = new RobotDisinfectTaskDetail();
                robotDisinfectTaskDetail.setRobotTaskRecordId(robotTaskRecord == null ? null : robotTaskRecord.getId());
                robotDisinfectTaskDetail.setWayDisinfectSpeed(disinfectSpeed == null ? 0.0 : Double.parseDouble(disinfectSpeed.toString()));
                robotDisinfectTaskDetail.setWayDisinfectUlray(wayUlray ? SUCCESS : FAIL);
                robotDisinfectTaskDetail.setWayDisinfectSpray(waySpray ? SUCCESS : FAIL);
                robotDisinfectTaskDetail.setDisinfectType(robotTask.getSubType());
                robotDisinfectTaskDetail.setUlray(robotDisinfect.getUlray());
                robotDisinfectTaskDetail.setSpray(robotDisinfect.getSpray());
                robotDisinfectTaskDetail.setXt(robotDisinfect.getXt());
                robotDisinfectTaskDetail.setDisinfectTime(robotDisinfect.getDisinfectTime());
                robotDisinfectTaskDetail.setStartTime(new Date());
                robotDisinfectTaskDetail.setSyncStatus("0");
            }
            boolean isLocalTask = DisinfectTaskType.LOCAL_TASK.getType().equals(robotTask.getSubType());
            //发导航任务之前
            if (statusCode.equals(NAV_DOING_TYPE.getType().toString())) {
                if (wayUlray && !ulrayStatus && !isLocalTask) {
                    log.warn("沿途设置中紫外线打开但实际状态没打开");
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_FAN = true;
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = true;
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = true;
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = true;
                    if (iRobotStatusService.isLivingThingsUltraviolet() && iRobotStatusService.isHumanDetectionStatus()) {
                        log.warn("活物检测状态是否开启:{},活物检测:{}",
                                iRobotStatusService.isLivingThingsUltraviolet(),
                                iRobotStatusService.isHumanDetectionStatus());
                    } else {
                        rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.FAN_CTRL);
                        rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SHIELDING_CTRL);
                        rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.PULSE_CTRL);
                        rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.ULRAY_CTRL);
                    }
                }
                if (!wayUlray && ulrayStatus && !isLocalTask) {
                    log.warn("沿途设置中紫外线关闭但实际状态为打开");
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = false;
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = false;
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = false;
                    rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.PULSE_CTRL);
                    rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.ULRAY_CTRL);
                    rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SHIELDING_CTRL);
                }
                if (waySpray && !sprayStatus && !isLocalTask) {
                    log.warn("沿途设置中喷雾打开但实际没打开");
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = true;
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_FAN = true;
                    rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.FAN_CTRL);
                    rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SPRAY_CTRL);
                }
                if (!waySpray && sprayStatus && !isLocalTask) {
                    log.warn("沿途设置中喷雾关闭但实际状态为打开");
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = false;
                    ControlStatusConstants.DEV_TASK_NEED_STATUS_FAN = false;
                    rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SPRAY_CTRL);
                    rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.FAN_CTRL);
                }

            } else if (statusCode.equals(NAV_FINISH_TYPE.getType().toString()) || statusCode.equals(NAV_FINISH_NEARBY_TYPE.getType().toString())) {
                //到达目标点
                if (null != robotDisinfect) {
                    if (jikeEnable) {
                        boolean b = jikePlatformService.roomDisinfectIsStartOrFinish();
                        if (b) {
                            RobotWorkStatusDto robotWorkStatusDto = new RobotWorkStatusDto();
                            robotWorkStatusDto.setTaskStatus(TaskStatusEnum.ARRIVE_TARGET.getValue());
                            jikePlatformService.handleRobotWorkStatus(robotWorkStatusDto);
                        }
                    }
                    String ulray = robotDisinfect.getUlray();
                    String spray = robotDisinfect.getSpray();
                    String hkVideo = robotDisinfect.getIsVideo();
                    if (ulray.equals(SUCCESS) || spray.equals(SUCCESS)) {
                        if (!fanStatus) {
                            log.warn("只有一个消毒设备打开,并且风扇没有打开");
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.FAN_CTRL);
                        }
                    }
                    if (ulray.equals(SUCCESS) && !ulrayStatus) {
                        log.warn("点位设置为紫外灯开并且紫外灯的状态为关闭的时候");
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = true;
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = true;
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = true;
                        if (iRobotStatusService.isLivingThingsUltraviolet() && iRobotStatusService.isHumanDetectionStatus()) {
                            log.info("到达充电点，应该打开设备，但是检测到活物，不开");
                        } else {
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SHIELDING_CTRL);
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.PULSE_CTRL);
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.ULRAY_CTRL);
                        }
                    }
                    log.info("VIDEO_IS_CLOSE:{}", VIDEO_IS_CLOSE);
                    if (SUCCESS.equals(hkVideo) && VIDEO_IS_CLOSE) {
                        int userIdHandler = HkUtils.preRecord();
                        if (userIdHandler != -1) {
                            VIDEO_PATH = HkUtils.getStartRecord(robotTask.getName() + "_" + DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN),
                                    userIdHandler);
                            log.info("VIDEO_PATH_CONTENT:"+VIDEO_PATH);
                        }
                        VIDEO_IS_CLOSE = false;
                        lastTaskRecordId = jedis.hget(ROBOT_SYS_INFO, LAST_TASK_RECORD_ID);
                        RobotTaskRecord robotTaskRecord = new RobotTaskRecord();
                        robotTaskRecord.setId(lastTaskRecordId);
                        robotTaskRecord.setVideoPath(VIDEO_PATH);
                        iRobotTaskRecordService.updateById(robotTaskRecord);
                    }
                    if (ulray.equals(SUCCESS) && !pulseStatus) {
                        log.warn("点位设置为紫外灯开并且紫外灯的状态为关闭的时候");
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = true;
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = true;
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = true;
                        if (iRobotStatusService.isLivingThingsUltraviolet() && iRobotStatusService.isHumanDetectionStatus()) {
                            log.info("到达充电点，应该打开设备，但是检测到活物，不开");
                        } else {
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SHIELDING_CTRL);
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.PULSE_CTRL);
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.ULRAY_CTRL);
                        }
                    }

                    if (ulray.equals(FAIL) && ulrayStatus) {
                        log.warn("点位设置为紫外灯关并且紫外灯的状态为开的时候");
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = false;
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = false;
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = false;
                        rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.PULSE_CTRL);
                        rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.ULRAY_CTRL);
                        rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SHIELDING_CTRL);
                    }
                    if (spray.equals(SUCCESS)) {
                        log.warn("点位设置为喷雾开的时候");
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = true;
                        if (iRobotStatusService.isLivingThingsUltraviolet() && iRobotStatusService.isHumanDetectionStatus()) {
                            log.info("检测到活物");
                        } else {
                            rosWebService.sendDisinfectPrompt(true, DisinfectSwitch.SPRAY_CTRL);
                        }
                    }
                    if (spray.equals(FAIL)) {
                        log.warn("点位设置为喷雾关的时候");
                        ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = false;
                        rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SPRAY_CTRL);
                    }
                    // 消毒时间，秒
                    Integer disinfectTime = robotDisinfect.getDisinfectTime();
                    //消毒过程中
                    try {
                        if (disinfectTime != null) {
                            int count = 0;
                            while (true) {
                                //任务状态检测
                                boolean res = checkTaskStatus(jedis);
                                if (!res) {
                                    break;
                                }
                                // 巡线消毒不进行消毒时间判断
                                if (robotTask.getType().equals(DISINFECT.getType()) &&
                                        (robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType())
                                                || robotTask.getSubType().equals(DisinfectTaskType.DEFINE_LINE_TASK.getType()))) {
                                    break;
                                }
                                if (count >= disinfectTime * 10 || DisinfectTaskType.LOCAL_TASK.getType().equals(robotTask.getSubType())) {
                                    log.warn("消毒时间结束，关掉对应的消毒设备");
                                    if (robotTask.getType().equals(DISINFECT.getType())) {
                                        long toDoListSize = jedis.llen(TO_DO_LIST);
                                        boolean isCloseItem = !isLocalTask || toDoListSize == 0
                                                || !iRobotPositionService.isNextDisinfectPoint();
                                        log.info("isLocalTask:{},toDoListSize:{},isNextDisinfectPoint:{}",
                                                isLocalTask, toDoListSize, iRobotPositionService.isNextDisinfectPoint());
                                        if (!iRobotPositionService.isNextDisinfectPoint()
                                                && DisinfectTaskType.LOCAL_TASK.getType().equals(robotTask.getSubType())) {
                                            log.info("定点消毒中，等待 {} 秒", disinfectTime);
                                            ThreadUtil.sleep(disinfectTime * 1000);
                                        }
                                        if (!waySpray && isCloseItem) {
                                            log.warn("沿途没有开喷雾,时间结束后关闭......");
                                            ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = false;
                                            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SPRAY_CTRL);
                                        }
                                        if (!wayUlray && !waySpray && isCloseItem) {
                                            log.warn("沿途所有设备都没开启,时间结束后关闭风扇......");
                                            ControlStatusConstants.DEV_TASK_NEED_STATUS_FAN = false;
                                            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.FAN_CTRL);
                                        }
                                        if (!wayUlray && isCloseItem) {
                                            log.warn("沿途紫外没开启,时间结束后关闭......");
                                            ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = false;
                                            ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = false;
                                            ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = false;
                                            ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = false;
                                            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.PULSE_CTRL);
                                            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.ULRAY_CTRL);
                                            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SHIELDING_CTRL);
                                            rosWebService.sendDisinfectPrompt(false, DisinfectSwitch.SPRAY_CTRL);
                                        }
                                    }
                                    break;
                                }
                                ThreadUtil.sleep(100);
                                count++;
                            }
                            if (jikeEnable) {
                                boolean b = jikePlatformService.roomDisinfectIsStartOrFinish();
                                if (b) {
                                    RobotWorkStatusDto robotWorkStatusDto = new RobotWorkStatusDto();
                                    robotWorkStatusDto.setTaskStatus(TaskStatusEnum.FINISH_WORK.getValue());
                                    jikePlatformService.handleRobotWorkStatus(robotWorkStatusDto);
                                }
                            }
                            rosWebService.sendMusicPrompt(0);
                            if (robotDisinfectTaskDetail != null) {
                                robotDisinfectTaskDetail.setEndTime(new Date());
                                iRobotDisinfectTaskDetailService.save(robotDisinfectTaskDetail);
                                log.info("添加消毒记录详情");
                            }
                            int waitTime = ObjectUtil.isNotNull(robotDisinfect.getWaitTime()) ? robotDisinfect.getWaitTime() : 0;
                            if (waitTime > 0) {
                                log.warn("等待时间内关闭消毒设备!!!");
                                rosWebService.closeAllDisinfect();
                            }
                            for (int i = 0; i < waitTime; i++) {
                                ThreadUtil.sleep(1000);
                            }
                        }
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                } else {
                    //如果是非消毒点已到达，关闭所有消毒设备
                    log.warn("到达非消毒点位，关闭所有消毒设备");
                    rosWebService.closeAllDisinfect();
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("消毒逻辑处理异常");
        }
    }

    /**
     * 处理路线消毒结束后的逻辑
     *
     * @param jedis     jedis
     * @param robotTask 任务信息
     */
    private void handleRouteTaskFinish(Jedis jedis, RobotTask robotTask) {
        if (robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType())) {
            // 将消毒模式队列弹出
            jedis.rpop(ROUTE_TO_DO_LIST);
            String loops = jedis.hget(TASK_INFO, LOOPS + ":" + robotTask.getId());
            if (StrUtil.isBlank(loops)) {
                if (null != robotTask.getLoops()) {
                    jedis.hset(TASK_INFO, LOOPS + ":" + robotTask.getId(), robotTask.getLoops().toString());
                } else {
                    jedis.hset(TASK_INFO, LOOPS + ":" + robotTask.getId(), "1");
                }
            }
            loops = jedis.hget(TASK_INFO, LOOPS + ":" + robotTask.getId());
            log.info("任务名称" + robotTask);
            log.info("任务次数" + robotTask.getLoops());
            log.info("当前循环次数" + loops);
            if (loops != null) {
                if (Integer.parseInt(loops) > 1) {
                    ControlStatusConstants.VIDEO_LINE_FINISH_IS_CLOSE = false;
                }
                rosWebService.closeAllDisinfect();
                if (Integer.parseInt(loops) > 1) {
                    jedis.hset(TASK_INFO, LOOPS + ":" + robotTask.getId(), (Integer.parseInt(loops) - 1) + "");
                    rosWebService.startDisinfectService(robotTask.getId());
                } else {
                    jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                    Result<Boolean> booleanResult = rosWebService.gotoCharging();
                    if (!booleanResult.isSuccess()) {
                        rosWebService.gotoOrigin();
                    }
                }
            } else {
                // 先检测状态
                boolean b = checkTaskStatus(jedis);
                if (b) {
                    rosWebService.startDisinfectService(robotTask.getId());
                }
            }
        }
        if (robotTask.getSubType().equals(DisinfectTaskType.DEFINE_LINE_TASK.getType())) {
            Long routeToDoListSize = jedis.llen(ROUTE_TO_DO_LIST);
            if (routeToDoListSize > 0) {
                String toDoContent = jedis.lindex(ROUTE_TO_DO_LIST, routeToDoListSize - 1);
                String[] toDoContentArr = toDoContent.split("#");
                String taskId = toDoContentArr[0];
                String robotRouteId = toDoContentArr[1];
                RobotDisinfect one = iRobotDisinfectService.getOne(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, taskId).eq(RobotDisinfect::getRobotLocationId, robotRouteId));
                // 获取消毒时间
                Integer disinfectTime = one.getDisinfectTime();
                String startTime = jedis.hget(TASK_INFO, ROUTE_DISINFECT_START_TIME);
                if (StringUtil.isNotBlank(startTime)) {
                    if ((System.currentTimeMillis() - Long.valueOf(startTime)) / 1000 >= disinfectTime) {
                        jedis.hdel(TASK_INFO, ROUTE_DISINFECT_START_TIME);
                        jedis.rpop(ROUTE_TO_DO_LIST);
                        routeToDoListSize = jedis.llen(ROUTE_TO_DO_LIST);
                        if (routeToDoListSize == 0) {
                            rosWebService.closeAllDisinfect();
                            jedis.hset(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                            Result<Boolean> booleanResult = rosWebService.gotoCharging();
                            if (!booleanResult.isSuccess()) {
                                rosWebService.gotoOrigin();
                            }
                        }
                    }
                }
            }
        }
    }

    /**
     * 任务预处理（获取下一个位置区域和位置编号）
     *
     * @param jedis
     * @param positionId
     * @return
     */
    private RobotLocation preHandleTask(Jedis jedis, String positionId) {
        RobotLocation robotLocation = null;
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("position_id", positionId);
        List<RobotLocation> robotLocationList = iRobotLocationService.list(queryWrapper);
        if (null != robotLocationList && !robotLocationList.isEmpty()) {
            RobotLocation robotLocation1 = robotLocationList.get(robotLocationList.size() - 1);
            String robotLocationId = robotLocation1.getId();
            robotLocation = iRobotLocationService.getById(robotLocationId);
            jedis.hset(TASK_INFO, NEXT_LOCATION_INFO, robotLocation.getLocationInfo());
            jedis.hset(TASK_INFO, NEXT_LOCATION_CODE, robotLocation.getLocationCode());
        }
        return robotLocation;
    }


    /**
     * 取物预处理(获取下一层)
     *
     * @param jedis
     * @param toDoContentArr
     * @param all
     */
    private String preHandleMeals(Jedis jedis, String[] toDoContentArr, List<String> all) {
        String nextLay = "";
        String position = toDoContentArr[toDoContentArr.length - 1];
        if (toDoContentArr.length == 4) {
            for (String item : all) {
                String[] itemArr = item.split("#");
                String itemPosition = itemArr[itemArr.length - 1];
                _NavControlReq navControlReq1 = JSON.parseObject(position, _NavControlReq.class);
                _NavControlReq navControlReq2 = JSON.parseObject(itemPosition, _NavControlReq.class);
                if (navControlReq1.pose_name.equals(navControlReq2.pose_name)) {
                    nextLay = nextLay + itemArr[2] + ",";
                }
            }
            if (nextLay.endsWith(",")) {
                nextLay = nextLay.substring(0, nextLay.length() - 1);
            }
        }
        return nextLay;
    }

    /**
     * 消毒预处理
     *
     * @param jedis
     * @param robotTask
     * @param id
     */
    private void preHandleDisinfect(Jedis jedis, RobotTask robotTask, String id) {
        List<RobotDisinfect> robotDisinfectList = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>()
                .eq(RobotDisinfect::getRobotLocationId, id)
                .eq(RobotDisinfect::getRobotTaskId, robotTask.getId()));
        if (CollectionUtil.isNotEmpty(robotDisinfectList)) {
            RobotDisinfect robotDisinfect = robotDisinfectList.get(0);
            jedis.hset(TASK_INFO, IS_ULRAY, robotDisinfect.getUlray().equals(SUCCESS) ? "true" : "false");
            jedis.hset(TASK_INFO, IS_SPRAY, robotDisinfect.getSpray().equals(SUCCESS) ? "true" : "false");
            jedis.hset(TASK_INFO, IS_XT, robotDisinfect.getXt().equals(SUCCESS) ? "true" : "false");
            jedis.hset(TASK_INFO, DISINFECT_TIME, robotDisinfect.getDisinfectTime() == null ? "" : robotDisinfect.getDisinfectTime().toString());
        }
    }

    /**
     * 充电状态、防撞条、急停开关
     *
     * @param jedis
     * @return true: 状态检测通过 false: 不通过
     */
    private boolean checkStatus(Jedis jedis) {
        boolean res = true;
        //判断是否在手动充电
        boolean manualCharge = rosWebService.isManualCharge();
        if (manualCharge) {
            res = false;
        }
        //急停开关是否被按下
        if (rosWebService.isStopping()) {
            res = false;
        }
        // 判断是否在自动充电，如果再自动充电则先结束
        String autoChargingStatusStr = jedis.hget(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
        Integer autoChargingStatus = Integer.valueOf(autoChargingStatusStr);
        if (autoChargingStatus.equals(AutoChargingStatus.SUCCESS.getValue())
                && res
                && iRobotStatusService.getSystemChargeState()) {
            for (int i=0;i<4;i++){
                if(rosWebService.endCharging()){
                    log.info("脱离充电桩成功！！！！！！");
                    break;
                }
            }
            //防撞条是否触发
            if (rosWebService.isCollision()) {
                res = false;
            }
            jedis.set(TOPIC + "::" + TopicConstants.POINT_MARKER, "");
            jedis.hset(ROBOT_SYS_INFO, END_CHARGING, Boolean.FALSE.toString());
            jedis.hset(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.UNDO.getValue().toString());
            iRobotChargingRecordService.saveRobotChargingRecord(2);
        }
        return res;
    }

    /**
     * 检测任务状态
     *
     * @param jedis
     * @return true:开始任务 / false: 任务停止
     */
    public boolean checkTaskStatus(Jedis jedis) {
        boolean res = true;
        String isRunning = jedis.hget(TASK_INFO, IS_RUNNING);
        String isStopping = jedis.hget(TASK_INFO, IS_STOPPING);
        if ("true".equals(isStopping)) {
            //发布工作被打扰的点阵表情话题---工作被打扰
            rosWebService.publishUtil(ExpressionType.WORK_DISTURBED.getValue());
            res = false;
        }
        if ("false".equals(isRunning)) {
            res = false;
        }
        if ("false".equals(isRunning) || "true".equals(isStopping)) {
            if (null != timer1) {
                timer1.cancel();
                timer1 = null;
            }
            if (null != timer2) {
                timer2.cancel();
                timer2 = null;
            }
        }
        return res;
    }

    /**
     * 发送导航任务
     *
     * @param position 位置信息 _NavControlReq
     */
    public int sendNavPrompt(Jedis jedis, String position, RobotTask robotTask, String positionId) {
        _Uint8 uint8 = new _Uint8();
        if (robotTask.getType().equals(TaskType.ENTRANCE_GUARD.getType())) {
            String entrancePositionId = jedis.get(ENTRANCE_POSITION_ID);
            if (StrUtil.isNotBlank(entrancePositionId) && entrancePositionId.equals(positionId)) {
                log.info("前往第1个门禁点");
                uint8.data = 1;
            } else {
                uint8.data = 3;
            }
        } else {
            uint8.data = 1;
        }
        rosBridgeService.publish(LOCATION_ACCURACY, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
        _NavControlReq navControlReq = JSON.parseObject(position, _NavControlReq.class);
        _NavControlRep navControlRep = null;
        int ret = -1;
        try {
            String s1 = rosBridgeService.callService(ServiceConstants.YX_NAV_CONTROL, Message.getMessageType(_NavControlReq.class), JSON.toJSONString(navControlReq));
            if (StrUtil.isNotEmpty(s1)) {
                navControlRep = JSON.parseObject(s1, _NavControlRep.class);
            }
            flag1 = !flag1;
            Thread.sleep(500);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("前往：" + navControlReq.pose_name + "导航点");
        if (jikeEnable) {
            // 工作状态预处理开始
            RobotWorkStatusDto robotWorkStatusDto = new RobotWorkStatusDto();
            RobotLocation one = iRobotLocationService.getOne(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getPositionId, positionId));
            if (one != null) {
                String target = one.getLocationCode();
                if (StrUtil.isNotBlank(target) && target.indexOf("-") != -1) {
                    if (target.endsWith("-1")
                            || target.endsWith("-2")
                            || target.endsWith("-3")
                            || target.endsWith("-4")
                            || target.endsWith("-5")) {
                        target = target.substring(0, target.lastIndexOf("-"));
                    }
                    robotWorkStatusDto.setDestinationId(target);
                    String[] split = target.split("-");
                    if (split.length > 0) {
                        robotWorkStatusDto.setRoomNo(split[2]);
                    }
                }
                robotWorkStatusDto.setTaskStatus(TaskStatusEnum.GOTO_TARGET.getValue());
                jikePlatformService.handleRobotWorkStatus(robotWorkStatusDto);
            }
        }
        // 工作状态预处理结束
        if (null != navControlRep) {
            ret = navControlRep.ret;
        }
        return ret;
    }

    /**
     * 发送路径请求
     *
     * @return
     */
    private boolean sendRoutePrompt(String routeId) {
        // 开启低精度
        _Uint8 uint8 = new _Uint8();
        uint8.data = 3;
        rosBridgeService.publish(LOCATION_ACCURACY, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
        boolean res = iRobotDisinfectService.routeDisinfectOperation(routeId, TaskOperationType.START.getOperation());
        flag2 = !flag2;
        return res;
    }

    /**
     * 移动到路线起始点
     * @param routeId
     * @return
     */
    public Boolean moveLineStartPoint(String routeId) {
//      解决在返回任务起始点时取消任务不能再开始任务的BUG；
        log.info("ZT:开始导航到起始点开启电机控制");
        iRobotMotorService.motorControl(true);
        log.info("ZT:开始导航到起始点");
        RobotRoute robotRoute = iRobotRouteService.getById(routeId);
        if (ObjectUtil.isNull(robotRoute) || StringUtil.isBlank(robotRoute.getPositions())) {
            return false;
        }
        List<_Pose> poseList = JSONObject.parseArray(robotRoute.getPositions(), _Pose.class);
        _Pose startPoint = poseList.get(0);
        Jedis jedis = RedisUtil.getJedis();
        RobotTask robotTask = new RobotTask();
        robotTask.setType(DISINFECT.getType());

        _NavControlReq req = new _NavControlReq();
        req.cmd = 1;
        req.pose_name = "路线起始点";
        req.nav_mode = 0;
        req.pose_info = startPoint;
        try {
            String position = JSON.toJSONString(req);
            log.info("position:{}", position);
            int s1 = sendNavPrompt(jedis, position, robotTask, null);
            log.info("s1:{}", s1);
            return 0 == s1;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return false;
    }

    /**
     * 设置任务类型
     *
     * @param jedis
     */
    private void settingTaskType(Jedis jedis, Integer taskType) {
        jedis.hset(TASK_INFO, TASK_TYPE, taskType == null ? "" : taskType.toString());
    }
}
