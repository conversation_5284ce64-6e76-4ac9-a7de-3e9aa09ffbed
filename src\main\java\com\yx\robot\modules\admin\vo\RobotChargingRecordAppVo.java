package com.yx.robot.modules.admin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/8/12
 * description：用于APP端查看充电记录
 */
@Data
public class RobotChargingRecordAppVo {

    private String taskRecordId;

    private Integer type;

    private String typeText;

    private Date startTime;

    private Double voltage;

    private Double current;

    private Double percentage;

    private Integer tryTimes;

    private Integer conditionType;

    private String status;

    private String dataStatus;

    private String syncStatus;

}
