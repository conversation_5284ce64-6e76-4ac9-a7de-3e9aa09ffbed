package com.yx.robot.modules.admin.service;

import com.yx.robot.common.enums.RobotOperationStatus;
import com.yx.robot.modules.admin.message._BatteryState;

/**
 * 机器人状态服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 13:59
 */
public interface IRobotStatusService {

    /**
     * 检测水箱水位状态
     *
     * @return true:满了 / false: 未满
     */
    boolean checkSprayLiquidOverflow();


    /**
     * 判断机器人是否在充电
     *
     * @return true:正在充电/false：未充电
     */
    boolean isDock();

    /**
     * 获取机器人充电状态
     * <p>
     * 0：未充电，1：充电器充电（手动充电），2：自动充电，3：手推充电
     *
     * @return 0：未充电，1：充电器充电（手动充电），2：自动充电，3：手推充电
     */
    Short getDockState();

    /**
     * 机器人液位状态检测
     * 系统初始化时，订阅ros话题，保存到redis
     * 通过redis,获取获取缺液状态
     *
     * @return true:缺液，false:不缺液
     */
    boolean isSprayLiquidLevelWarning();

    /**
     * 液位灯光话题发布
     *
     * @param operate
     */
    void publishLiquidLightTopic(Integer operate);

    /**
     * 活物检测开关
     *
     * @return true/打开 false/关闭
     */
    boolean isLivingThingsUltraviolet();

    /**
     * 人体检测状态 是否发现人
     *
     * @return true（发现）/false(未发现)
     */
    boolean isHumanDetectionStatus();

    /**
     * 是否开启紫外
     *
     * @return true（开启）/false(未开始)
     */
    boolean isUltravioletStatus();

    /**
     * 是否开启喷雾
     *
     * @return true（开启）/false(未开始)
     */
    boolean isSprayStatus();

    /**
     * 是否开启脉冲
     *
     * @return true（开启）/false(未开始)
     */
    boolean isPulseStatus();

    /**
     * 是否开启风扇
     *
     * @return true（开启）/false(未开始)
     */
    boolean isFanStatus();

    /**
     * 是否打开语音告警
     *
     * @return true(打开) / false(未打开)
     */
    boolean isOpenVoiceWarning();

    /**
     * 升降杆状态
     *
     * @return true(打开) / false(未打开)
     */
    boolean isShieldingStatus();

    /**
     * 获取机器人运行状态
     *
     * @return 机器人运行状态
     */
    RobotOperationStatus getOperationStatus();

    /**
     * 检测任务状态信息
     *
     * @return true/false
     */
    boolean checkTaskStatus();

    /**
     * 获取任务状态
     * @return
     */
    Integer getTaskStatus();

    /**
     * 获取机器人定位状态
     *
     * @return true:定位成功，false:定位失败
     */
    boolean getPositionStatus();

    /**
     * 获取电池电量信息
     *
     * @return 电池全部信息
     */
    _BatteryState getBatteryState();

    /**
     * 获取电池电量百分比
     *
     * @return 电量百分比
     */
    Float getBatteryPercentage();

    /**
     * 获取防跌落状态
     *
     * @return true:防跌落触发,false: 防跌落暂停
     */
    Boolean getMotorLock();

    /**
     * 判断机器人是否接触到充电片
     *
     * @return true(是)/false(否)
     */
    Boolean getSystemChargeState();
}
