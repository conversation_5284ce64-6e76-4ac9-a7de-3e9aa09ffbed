package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_areas")
@ApiModel(value = "机器人区域")
public class RobotAreas extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序号")
    private String orderNumber;

    @ApiModelProperty(value = "地图Id")
    private String mapId;

    @ApiModelProperty(value = "类型 1:速度区域 2:禁行区域 3:膨胀区域")
    private Integer type;

    @ApiModelProperty(value = "坐标点")
    private String positions;

    @ApiModelProperty(value = "最大线速度")
    private Double maxLineSpeed;

    @ApiModelProperty(value = "最大角速度")
    private Double maxAngularSpeed;

    @ApiModelProperty(value = "排序值")
    @Column(precision = 10, scale = 2)
    private BigDecimal sortOrder;

}