package com.yx.robot.common.enums;

/**
 * 充电类型（底层）
 * <AUTHOR>
 * @date 2020/09/17
 */
public enum AutoDockType {

    //1：手动充电 2：自动充电 3：手推充电

    /**
     *  手动充电
     */
    MANUAL_DOCK(1,"手动充电"),

    /**
     * 自动充电
     */
    AUTO_DOCK(2,"自动充电"),

    /**
     * 手推充电
     */
    HANDLE_PUSH_DOCK(3,"手推充电");

    private Integer type;

    private String value;

    AutoDockType(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return this.type;
    }

    public String getValue() {
        return value;
    }
}
