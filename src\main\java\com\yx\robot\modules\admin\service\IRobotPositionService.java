package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.entity.RobotWorldPosition;
import com.yx.robot.modules.admin.message._Pose;
import com.yx.robot.modules.admin.message._PoseStamped;
import com.yx.robot.modules.admin.vo.RobotPositionVo;
import com.yx.robot.modules.admin.vo.RobotSubPositionVo;

import java.util.List;

/**
 * 机器人列表接口
 *
 * <AUTHOR>
 */
public interface IRobotPositionService extends IService<RobotPosition> {
    /**
     * 添加点位数据
     *
     * @param robotPositionVo
     * @return
     */
    Result<Boolean> add(RobotPositionVo robotPositionVo);

    /**
     * robot实体转vo
     *
     * @param robotPosition
     * @return
     */
    RobotPositionVo robotPositionToRobotPositionVo(RobotPosition robotPosition);

    /**
     * robot实体转subPositionVo
     *
     * @return
     */
    RobotSubPositionVo robotPositionToRobotSubPositionVo(RobotPosition robotPosition);

    /**
     * 排序
     *
     * @return
     */
    boolean sort();

    /**
     * 更新点位数据
     *
     * @param robotPositionVo
     * @return
     */
    Result<Boolean> update(RobotPositionVo robotPositionVo);

    /**
     * 删除点位数据
     *
     * @param id 位置ID
     * @return false/true
     */
    boolean delete(String id);

    /**
     * 将从ROS中获取的数据转换成世界坐标系
     *
     * @return 世界地图信息
     */
    RobotWorldPosition getRobotPoseFromRos();

    /**
     * 判断同一张地图下面的点位名称是否重复
     *
     * @param mapId 地图ID
     * @param name  地图名称
     * @return true/false
     */
    boolean isRepeat(String mapId, String name);

    /**
     * 检测标点是否可用
     *
     * @param robotWorldPosition 世界地图信息
     * @return true/false
     */
    boolean checkPointValue(RobotWorldPosition robotWorldPosition);

    /**
     * 标注起始点和充电点
     *
     * @return
     */
    boolean markStartAndChargingPose();

    /**
     * 标注起始点和充电点
     *
     * @return
     */
    boolean markStartAndChargingPose(String mapId);

    /**
     * 更新充电桩位置
     *
     * @param mapId 地图ID
     * @return true:成功/false:失败
     */
    boolean updateDockCheck(String mapId);

    /**
     * 保存地图完成时，更新充电桩位置和充电点
     *
     * @return true/false
     */
    boolean updateDockPointAndOriginBySaveMap();

    /**
     * 获取深度相机图片路劲
     *
     * @return 图片路劲
     */
    String getImagePath();

    /**
     * 获取机器人位置信息
     *
     * @return json格式
     */
    String getRobotPoseJson();

    /**
     * 获取机器人位置信息
     *
     * @return 对象格式
     */
    _Pose getRobotPose();

    /**
     * 获取机器人位置信息
     *
     * @return json格式
     */
    String getRobotPoseStampedJson();

    /**
     * 获取机器人位置信息
     *
     * @return 对象格式
     */
    _PoseStamped getRobotPoseStamped();

    /**
     * 获取两个点路径上的点位个数
     *
     * @param start 起始点
     * @param end   结束点
     * @return 路径上点的数量
     */
    Integer getRoutePointSum(_PoseStamped start, _PoseStamped end);

    /**
     * 获取两个点路径上的点位个数
     *
     * @param startId 起始点
     * @param endId   结束点
     * @return 路径上点的数量
     */
    Integer getRoutePointSum(String startId, String endId);

    /**
     * 获取停靠点在地图的最短路径
     *
     * @param positionIdList 停靠点列表
     * @return 排序后停靠点列表
     */
    List<String> shortestPaths(List<String> positionIdList);

    /**
     * 获取停靠点在地图的最短路径
     *
     * @param start          开始点
     * @param positionIdList 停靠点列表
     * @return 排序后停靠点列表
     */
    List<String> shortestPaths(_PoseStamped start, List<String> positionIdList);

    /**
     * 下一个点是否位消毒
     *
     * @return true/false
     */
    boolean isNextDisinfectPoint();

    /**
     * 设置定位状态
     *
     * @param status 定位状态
     */
    void setPositionStatus(String status);

    /**
     * 根据点位类型和地图ID 获取点位ID
     *
     * @param typeName 类型名称
     * @param mapId    地图ID
     * @return 点位列表
     */
    List<String> getPositionIdByTypeAndMapId(String typeName, String mapId);

    /**
     * 删除点位 根据类型名称和地图ID
     *
     * @param typeName
     * @param mapId
     */
    void deleteByTypeNameAndMapId(String typeName, String mapId);
}