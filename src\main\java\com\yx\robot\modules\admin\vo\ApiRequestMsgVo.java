package com.yx.robot.modules.admin.vo;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.util.Date;

/**
 * api请求rocket消息封装
 * <AUTHOR>
 * @date 2020-1-16
 */
@Data
public class ApiRequestMsgVo {

    private ApiRequestMsgVo(){}

    public ApiRequestMsgVo(String host, String url, String requestMethod, Date time) {
        this.host = host;
        this.url = url;
        this.requestMethod = requestMethod;
        this.time = time;
    }

    public ApiRequestMsgVo(String host, String url, String requestMethod, JSONObject variables, JSONObject params, Date time) {
        this.host = host;
        this.url = url;
        this.requestMethod = requestMethod;
        this.variables = variables;
        this.params = params;
        this.time = time;
    }

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 请求主机
     */
    private String host;

    /**
     * 请求路径
     */
    private String url;

    /**
     * 请求方式
     */
    private String requestMethod;

    /**
     * 请求url参数
     */
    private JSONObject variables;

    /**
     * 请求body参数
     */
    private JSONObject params;

    /**
     * 时间
     */
    private Date time;
}
