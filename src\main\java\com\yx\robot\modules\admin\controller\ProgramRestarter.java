package com.yx.robot.modules.admin.controller;

import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.util.ArrayList;
import java.util.List;

public class ProgramRestarter {

   /* public static void main(String[] args) {
        System.out.println("程序启动 - 版本: 1.0");

        // 添加关闭钩子，在程序退出时执行清理工作
        Runtime.getRuntime().addShutdownHook(new Thread(() -> {
            System.out.println("执行清理工作...");
        }));

        // 模拟程序运行
        for (int i = 0; i < 5; i++) {
            System.out.println("程序运行中... " + i);
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            // 模拟在某些条件下需要重启
            if (i == 3) {
                System.out.println("检测到需要重启的条件...");
                restartApplication();
            }
        }
    }*/
    /**
     * 重启当前Java应用程序
     */
    public static void restartApplication() {
        try {
            System.out.println("准备重启应用程序...");

            // 获取当前Java进程的启动命令
            String javaPath = System.getProperty("java.home") + "/bin/java";
            List<String> vmArguments = ManagementFactory.getRuntimeMXBean().getInputArguments();
            List<String> command = new ArrayList<>();
            command.add(javaPath);
            command.addAll(vmArguments);

            // 添加程序主类和类路径
            command.add("-cp");
            command.add(ManagementFactory.getRuntimeMXBean().getClassPath());
            command.add(ProgramRestarter.class.getName());

            // 打印重启命令（调试用）
            System.out.println("重启命令: " + String.join(" ", command));

            // 启动新进程
            ProcessBuilder processBuilder = new ProcessBuilder(command);
            processBuilder.inheritIO();
            Process process = processBuilder.start();

            // 等待新进程启动
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            // 退出当前进程
            System.exit(0);

        } catch (IOException e) {
            System.err.println("重启应用程序失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}