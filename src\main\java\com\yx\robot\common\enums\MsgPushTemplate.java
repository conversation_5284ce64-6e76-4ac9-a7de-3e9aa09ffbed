package com.yx.robot.common.enums;

/**
 * 消息推送模板
 */
public enum MsgPushTemplate {

    /**
     * "开始消毒","提示","主人，我要去消毒了哦！"
     */
    START_DISINFECT("开始消毒", "提示", "主人，我要去消毒了哦！"),

    /**
     * "消毒任务失败","告警","主人，非常抱歉！消毒任务失败了！"
     */
    DISINFECT_FAIL("消毒任务失败", "告警", "主人，非常抱歉！消毒任务失败了！"),

    /**
     * "消毒任务完成","提示","主人，啦啦！我已经成功完成消毒任务了！"
     */
    DISINFECT_FINISH("消毒任务完成", "提示", "主人，啦啦！我已经成功完成消毒任务了！"),

    /**
     * "返回充电","提示","主人，我要去补充能量了！"
     */
    GOTO_CHARGING("返回充电", "提示", "主人，我要去补充能量了！"),

    /**
     * "充电成功","提示","主人，我在充电桩上充电哦！"
     */
    CHARGING_SUCCESS("充电成功", "提示", "主人，我在充电桩上充电哦！"),

    /**
     * "电量过低","提示","主人，电量过低哦！"
     */
    LOW_BATTERY("电量过低", "提示", "主人，电量过低哦！"),

    /**
     * "急停被摁下","告警","主人，我的急停开关被按下了！"
     */
    EMERGENCY("急停被摁下", "告警", "主人，我的急停开关被按下了！"),

    /**
     * "防撞条触发", "告警","主人，防撞条触发了，快来看看我吧！"
     */
    COLLISION("防撞条触发", "告警", "主人，防撞条触发了，快来看看我吧！"),

    /**
     * "缺液","告警","主人，我没有消毒液了！"
     */
    SPRAY_WIQUID_WARNING("缺液", "告警", "主人，我没有消毒液了！"),

    /**
     * "导航失败","告警", "主人，路径规划失败"
     */
    PLAN_FAIL("导航失败", "告警", "主人，路径规划失败"),

    /**
     * "门禁失败","告警","主人，门禁指令执行异常"
     */
    ENTRANCE_FAIL("门禁失败", "告警", "主人，门禁指令执行异常"),

    /**
     * "保养提醒","告警","设备需要保养"
     */
    DEVICE_MAINTENANCE_ALERT("保养提醒", "告警", "设备需要保养"),

    /**
     * "保养提醒","告警","脉冲灯需要保养"
     */
    ULRAY_MAINTENANCE_ALERT("保养提醒", "告警", "脉冲灯需要保养");

    private String scene;

    private String type;

    private String value;

    MsgPushTemplate(String scene, String type, String value) {
        this.scene = scene;
        this.type = type;
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public String getScene() {
        return scene;
    }

    public String getType() {
        return type;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setValue(String value) {
        this.value = value;
    }

}
