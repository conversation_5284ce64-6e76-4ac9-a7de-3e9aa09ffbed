package com.yx.robot.modules.admin.electrl.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.enums.ElevatorPromise;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.utils.HttpSendUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.ElevatorPromiseDto;
import com.yx.robot.modules.admin.electrl.constant.ElevatorTopicConstants;
import com.yx.robot.modules.admin.electrl.enums.ElevatorBindDeviceUniqueEnum;
import com.yx.robot.modules.admin.electrl.enums.ElevatorPositionEnum;
import com.yx.robot.modules.admin.electrl.enums.StepEnum;
import com.yx.robot.modules.admin.electrl.msg.DeveloperLoginRep;
import com.yx.robot.modules.admin.electrl.msg.DeveloperLoginReq;
import com.yx.robot.modules.admin.electrl.msg.TaskInfoRep;
import com.yx.robot.modules.admin.electrl.msg.TaskInfoReq;
import com.yx.robot.modules.admin.electrl.service.ElevatorFactoryService;
import com.yx.robot.modules.admin.electrl.service.ElevatorTaskService;
import com.yx.robot.modules.admin.electrl.service.impl.ElevatorTaskServiceImpl;
import com.yx.robot.modules.admin.electrl.vo.TaskInfoVo;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.message._ElevatorTaskFeedback;
import com.yx.robot.modules.admin.message._Pose;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import com.yx.robot.modules.admin.service.core.InitService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 梯控调度
 *
 * <AUTHOR>
 * @date 2021/04/29
 */
@Component
@Slf4j
@Order(value = 3)
public class ElevatorTaskInfoScheduler implements CommandLineRunner {

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1, new BasicThreadFactory.Builder().namingPattern("scheduled-pool-%d").daemon(true).build());

    public static RobotPosition robotPosition = null;

    @Value("${platform.electrl.enable}")
    private boolean electrlEnable;

    private final HttpSendUtil httpSendUtil;

    private final IRobotPositionService iRobotPositionService;

    private final ElevatorFactoryService elevatorFactoryService;

    private final ElevatorTaskService elevatorTaskService;

    private final RosWebService rosWebService;

    private final InitService initService;

    @Autowired
    public ElevatorTaskInfoScheduler(HttpSendUtil httpSendUtil, IRobotPositionService iRobotPositionService,
                                     ElevatorFactoryService elevatorFactoryService, ElevatorTaskService elevatorTaskService,
                                     RosWebService rosWebService, InitService initService) {
        this.httpSendUtil = httpSendUtil;
        this.iRobotPositionService = iRobotPositionService;
        this.elevatorFactoryService = elevatorFactoryService;
        this.elevatorTaskService = elevatorTaskService;
        this.rosWebService = rosWebService;
        this.initService = initService;
    }

    /**
     * 是否呼叫电梯
     */
    public volatile static boolean hasCallElevator = false;

    /**
     * 是否进入电梯
     */
    public volatile static boolean hasIntoElevator = false;

    /**
     * 是否出电梯
     */
    public volatile static boolean hasOutElevator = false;

    /**
     * 是否关闭电梯
     */
    public volatile static boolean hasCloseElevator = false;

    @Override
    public void run(String... args) throws Exception {
        if (electrlEnable) {
            executorService.scheduleWithFixedDelay(() -> {
                handleDeveloperLogin();
                handleElevatorWork();
            }, 0, 1, TimeUnit.SECONDS);
        }
    }

    /**
     * 处理登录认证
     */
    public void handleDeveloperLogin() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String token = jedis.get(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN);
            if (StrUtil.isBlank(token)) {
                DeveloperLoginReq developerLoginReq = new DeveloperLoginReq();
                DeveloperLoginRep developerLoginRep = elevatorFactoryService.developerLogin(developerLoginReq);
                if (developerLoginRep != null) {
                    jedis.set(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN, developerLoginRep.getData().getToken());
                    // 厂商默认token过期时间为一周，我们设置为1天更新一次token
                    jedis.expire(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN, 60 * 60 * 24);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 处理电梯任务
     */
    public void handleElevatorWork() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            if (hasCallElevator) {
                String s = Objects.requireNonNull(jedis).get(ELEVATOR_PROMISE);
                ElevatorPromiseDto elevatorPromiseDto = JSONObject.parseObject(s, ElevatorPromiseDto.class);
                String nextMapId = jedis.hget(ROBOT_SYS_INFO, NEXT_MAP);
                TaskInfoRep elevatorStatus = elevatorFactoryService.getTaskInfo(jedis, new TaskInfoReq());
                Long delayTime = 20L;
                if (elevatorStatus != null) {
                    Map<String, String> result = JSONObject.parseObject(elevatorStatus.getData().toString(), Map.class);
                    delayTime = Long.parseLong(result.get("doorOpenTime")) - (System.currentTimeMillis() - Long.parseLong(result.get("doorOpenTimeBaseTimestamp"))) / 1000;
//                    log.info("电梯状态" + JSONObject.toJSONString(elevatorStatus));
                    log.info("电梯状态，当前step:{},剩余开门时间:{},计算剩余开门时间：{}，当前楼层:{},目标楼层：{},doorOpenTimeBaseTimestamp:{},timestamp:{}",
                            result.get("step"), result.get("doorOpenTime"),
                            delayTime,
                            result.get("floor"), result.get("toFloor"),
                            result.get("doorOpenTimeBaseTimestamp"), result.get("timestamp"));
                }
                _ElevatorTaskFeedback elevatorTaskFeedback = elevatorTaskService.elevatorTaskFeedback(jedis);
                rosWebService.isStopping();
                String isRunning = jedis.hget(TASK_INFO, IS_RUNNING);
                String isStopping = jedis.hget(TASK_INFO, IS_STOPPING);
                if ("false".equals(isRunning) || "true".equals(isStopping)) {
                    log.info("任务已经被取消");
                    hasCallElevator = false;
                }
                if (elevatorStatus != null && elevatorStatus.getData() != null && StrUtil.isNotBlank(elevatorStatus.getData().toString())) {
                    TaskInfoVo taskInfoVo = JSONObject.parseObject(JSONObject.toJSONString(elevatorStatus.getData()), TaskInfoVo.class);
                    String step = taskInfoVo.getStep();
                    String currentFloor = taskInfoVo.getFloor();
                    String entryDeviceUnique = taskInfoVo.getEntryDeviceUnique();
                    log.info("taskInfoVo:{}", JSON.toJSONString(taskInfoVo));
                    List<RobotPosition> robotPositionList = null;
                    log.info("当前楼层{},目标楼层{},电梯所在楼层{}", elevatorPromiseDto.getCurrentFloor(), elevatorPromiseDto.getTargetFloor(), currentFloor);
                    if (step.equals(StepEnum.SECOND.getStep())) {
                        if (!hasIntoElevator) {
                            log.info("电梯已到达,当前楼层{},目标楼层{},电梯所在楼层{}", elevatorPromiseDto.getCurrentFloor(), elevatorPromiseDto.getTargetFloor(), currentFloor);
                            elevatorTaskService.delayElevator(jedis, ElevatorPositionEnum.OUT.getValue(), "98", entryDeviceUnique);
                            elevatorPromiseDto.setCmd(ElevatorPromise.INTO_ELEVATOR.getCmd());
                            log.info("robotPosition:{}", JSON.toJSONString(robotPosition));
                            if (entryDeviceUnique.equals(ElevatorBindDeviceUniqueEnum.A.getDeviceUnique())) {
                                log.info("获取电梯内停靠点");
                                robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                                        .eq(RobotPosition::getMapId, robotPosition.getMapId())
                                        .eq(RobotPosition::getType, RobotPositionType.INSIDE_ELEVATOR_POSITION.getType())
                                        .like(RobotPosition::getName, ElevatorBindDeviceUniqueEnum.A.getName()));
                            }
                            if (entryDeviceUnique.equals(ElevatorBindDeviceUniqueEnum.B.getDeviceUnique())) {
                                robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getMapId, robotPosition.getMapId()).eq(RobotPosition::getType, RobotPositionType.INSIDE_ELEVATOR_POSITION.getType()).like(RobotPosition::getName, ElevatorBindDeviceUniqueEnum.B.getName()));
                            }
                            log.info("robotPositionList:{}", JSON.toJSONString(robotPositionList));
                            if (CollectionUtil.isNotEmpty(robotPositionList)) {
                                elevatorTaskService.intoElevator(elevatorPromiseDto, robotPositionList.get(0));
                                elevatorTaskService.clearElevatorTaskFeedback(jedis);
                                elevatorTaskFeedback = elevatorTaskService.elevatorTaskFeedback(jedis);
                                hasIntoElevator = true;
                            }
                        }
                    }
                    if (hasIntoElevator && !hasCloseElevator) {
                        if (ObjectUtil.isNull(elevatorTaskFeedback)) {
                            log.info("等待获取进入电梯反馈数据");
                        } else if (elevatorTaskFeedback.success) {
                            httpSendUtil.handleRestartNetWork();
                            elevatorTaskService.closeElevatorDoor(jedis, ElevatorPositionEnum.IN.getValue(), entryDeviceUnique);
                            hasCloseElevator = true;
                        } else {
                            log.info("进入电梯异常");
                        }
                    }
                    // 当电梯位于当前楼层并且门是开着的
                    if (step.equals(StepEnum.FOURTH.getStep())) {
                        if (delayTime < 20) {
                            elevatorTaskService.delayElevator(jedis, ElevatorPositionEnum.IN.getValue(), "98", entryDeviceUnique);
                        }
                        log.info("StepEnum.FOURTH:{}", hasOutElevator);
                        if (!hasOutElevator) {
                            elevatorTaskService.delayElevator(jedis, ElevatorPositionEnum.IN.getValue(), "98", entryDeviceUnique);
                            elevatorPromiseDto.setCmd(ElevatorPromise.OUT_ELEVATOR.getCmd());
                            robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                                    .eq(RobotPosition::getMapId, nextMapId)
                                    .eq(RobotPosition::getType, RobotPositionType.OUTSIDE_ELEVATOR_POSITION.getType())
                                    .like(RobotPosition::getName, ElevatorBindDeviceUniqueEnum.A.getName()));
                            log.info("电梯外停靠点" + JSONObject.toJSONString(robotPositionList));
                            if (CollectionUtil.isNotEmpty(robotPositionList)) {
                                elevatorTaskService.outElevator(elevatorPromiseDto, robotPositionList.get(0));
                                elevatorTaskService.clearElevatorTaskFeedback(jedis);
                                elevatorTaskFeedback = elevatorTaskService.elevatorTaskFeedback(jedis);
                                hasOutElevator = true;
                            }
                        }
                    }
                    if (hasOutElevator) {
                        if (ObjectUtil.isNull(elevatorTaskFeedback)) {
                            log.info("等待获取出电梯反馈数据");
                        } else if (elevatorTaskFeedback.success) {
                            httpSendUtil.handleRestartNetWork();
                            elevatorTaskService.closeElevatorDoor(jedis, ElevatorPositionEnum.OUT.getValue(), entryDeviceUnique);
                            log.info("成功离开电梯,进行地图切换");
                            if (StrUtil.isNotBlank(nextMapId)) {
                                Result<Boolean> booleanResult = rosWebService.selectMap(nextMapId);
                                if (booleanResult.isSuccess()) {
                                    robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                                            .eq(RobotPosition::getMapId, nextMapId).eq(RobotPosition::getType, RobotPositionType.OUTSIDE_ELEVATOR_POSITION.getType())
                                            .like(RobotPosition::getName, ElevatorBindDeviceUniqueEnum.A.getName()));
                                    _Pose pose = elevatorTaskService.position2Pose(robotPositionList.get(0));
                                    boolean b = initService.initPose(false, pose);
                                    if (b) {
                                        log.info("电梯停靠点重定位成功");
                                    }
                                }
                            }
                            hasCallElevator = false;
                            hasIntoElevator = false;
                            hasCloseElevator = false;
                            hasOutElevator = false;
                            ElevatorTaskServiceImpl.hasFinishElevator = true;
                        } else {
                            log.info("离开电梯异常");
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }
}
