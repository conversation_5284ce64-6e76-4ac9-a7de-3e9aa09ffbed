package com.yx.robot.modules.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.DisinfectManualOperationDto;
import com.yx.robot.modules.admin.dto.RobotTaskItemDto;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.entity.RobotTaskItem;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.DisinfectPointVo;
import com.yx.robot.modules.admin.vo.DisinfectTaskVo;
import com.yx.robot.modules.admin.vo.DisinfectTypeVo;
import com.yx.robot.modules.admin.vo.RobotTaskVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.yx.robot.common.constant.ControlStatusConstants.AUTO_DOCK_ERROR_CURRENT_TIME;
import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人任务管理接口")
@RequestMapping("/yx/api-v1/robotTask")
@Transactional
@CrossOrigin
public class RobotTaskController {

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotTaskItemService iRobotTaskItemService;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotTask> get(@PathVariable String id) {

        RobotTask robotTask = iRobotTaskService.getById(id);
        return new ResultUtil<RobotTask>().setData(robotTask);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotTask>> getAll(@RequestParam String mapId) {
        QueryWrapper<RobotTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", mapId);
        List<RobotTask> list = iRobotTaskService.list(queryWrapper);
        return new ResultUtil<List<RobotTask>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotTask>> getByPage(@ModelAttribute PageVo page, @RequestParam String mapId) {
        QueryWrapper<RobotTask> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(page.getName())) {
            queryWrapper.like("name", page.getName());
        }
        queryWrapper.eq("map_id", mapId);
        IPage<RobotTask> data = iRobotTaskService.page(PageUtil.initMpPage(page), queryWrapper);
        return new ResultUtil<IPage<RobotTask>>().setData(data);
    }

    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    @ApiOperation(value = "新增数据")
    public Result<Boolean> insert(@RequestBody RobotTaskVo robotTaskVo) {
        return iRobotTaskService.add(robotTaskVo);
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "更新数据")
    public Result<Boolean> update(@RequestBody RobotTaskVo robotTaskVo) {
        return iRobotTaskService.update(robotTaskVo);
    }

    @RequestMapping(value = "/navControl", method = RequestMethod.GET)
    @ApiOperation(value = "导航控制，只用于暂停和继续")
    public Result<Boolean> navControl(@RequestParam String id, @RequestParam String operation) {
        if (StrUtil.isNotEmpty(id)
                && TaskOperationType.CONTINUE.getOperation().toString().equals(operation)) {
            RobotTask task = iRobotTaskService.getById(id);
            Asserts.check(iRobotTaskService.isExecutableTime(task), "任务不在可执行的时间段内");
        }
        if (StringUtils.isBlank(RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID))) {
            return ResultUtil.error("当前无任务");
        }
        // 如果发送的是取消任务，则先停止任务：解决正在执行任务时，取消任务，机器人继续向前移动，喷雾不关闭BUG
        if (TaskOperationType.CANCEL.getOperation().toString().equals(operation)) {
            rosWebService.taskControl1(id, TaskOperationType.STOP.getOperation().toString());
            boolean res = rosWebService.taskControl1(id, operation);
            if (TaskOperationType.CANCEL.getOperation().toString().equals(operation) && res) {
                rosWebService.sendVoicePrompt(SceneType.CANCEL_WORK, null);
                rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
            }
            return new ResultUtil<Boolean>().setData(res);
        }

        if (rosWebService.isStopping()) {
            return ResultUtil.error("急停被按下，无法执行当前操作");
        }
        if (!iRobotStatusService.getPositionStatus()
                && TaskOperationType.CONTINUE.getOperation().toString().equals(operation)) {
            return ResultUtil.error("定位丢失，无法进行消毒工作");
        }

        boolean res = rosWebService.taskControl1(id, operation);
        if (TaskOperationType.STOP.getOperation().toString().equals(operation) && res) {
            rosWebService.sendVoicePrompt(SceneType.STOP_WORK, null);
        }
        if (TaskOperationType.CONTINUE.getOperation().toString().equals(operation) && res) {
            rosWebService.sendVoicePrompt(SceneType.CONTINUE_WORK, null);
            rosWebService.sendRingLightPrompt(RingLightDefine.DISINFECT_STATE);
        }
        return new ResultUtil<Boolean>().setData(res);
    }

    @RequestMapping(value = "/taskDetail/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "任务详情")
    public Result<List<RobotTaskItemDto>> taskDetail(@PathVariable String id) {
        List<RobotTaskItemDto> robotTaskItemDtoList = iRobotTaskService.taskDetail(id);
        return new ResultUtil<List<RobotTaskItemDto>>().setData(robotTaskItemDtoList);
    }

    @RequestMapping(value = "/updateNavPositions", method = RequestMethod.PUT)
    @ApiOperation(value = "更新导航点")
    public Result<Boolean> updateNavPositions(@RequestBody List<RobotTaskItem> robotTaskItems) {
        if (null != robotTaskItems && !robotTaskItems.isEmpty()) {
            RobotTaskItem robotTaskItem = robotTaskItems.get(0);
            QueryWrapper<RobotTaskItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("task_id", robotTaskItem.getTaskId());
            iRobotTaskItemService.remove(queryWrapper);
            iRobotTaskItemService.saveBatch(robotTaskItems);
        }
        return new ResultUtil<Boolean>().setData(true);
    }

    @RequestMapping(value = "/getDisinfectCounts", method = RequestMethod.GET)
    @ApiOperation(value = "获取消毒循环次数")
    public Result<Integer> getDisinfectCounts() {
        Integer disinfectCounts = iRobotTaskService.getDisinfectCounts();
        return new ResultUtil<Integer>().setData(disinfectCounts);
    }

    @RequestMapping(value = "/updateDisinfectCounts", method = RequestMethod.GET)
    @ApiOperation(value = "更新消毒循环次数")
    public Result<Boolean> updateDisinfectLoops(@RequestParam Integer counts) {
        iRobotTaskService.setDisinfectCounts(counts);
        return new ResultUtil<Boolean>().setData(true);
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Boolean> delAllByIds(@PathVariable String[] ids) {
        try {
            for (String id : ids) {
                iRobotTaskService.delete(id);
            }
            return new ResultUtil<Boolean>().setData(true);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultUtil<Boolean>().setData(false);
        }
    }

    @RequestMapping(value = "/clearAll", method = RequestMethod.DELETE)
    @ApiOperation(value = "清空全部数据")
    public Result<Object> clearAll() {
        List<RobotTask> robotTasks = iRobotTaskService.list();
        if (null != robotTasks && !robotTasks.isEmpty()) {
            for (RobotTask robotTask : robotTasks) {
                iRobotTaskService.delete(robotTask.getId());
            }
        }
        return new ResultUtil<>().setSuccessMsg("清空数据成功");
    }

    @RequestMapping(value = "/meals/stop", method = RequestMethod.GET)
    @ApiOperation(value = "停止任务")
    public Result<Boolean> stopMealService() {
        rosWebService.sendVoicePrompt(SceneType.STOP_WORK, null);
        boolean result = rosWebService.taskControl1("", "2");
        return new ResultUtil<Boolean>().setData(result);
    }

    @RequestMapping(value = "/meals/continueWork", method = RequestMethod.GET)
    @ApiOperation(value = "继续工作")
    public Result<Boolean> continueWork() {
        boolean res = true;
        rosWebService.taskControl1(null, "3");
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作成功");
        }
    }

    @RequestMapping(value = "/meals/gotoCharging", method = RequestMethod.GET)
    @ApiOperation(value = "返回充电")
    public Result<Boolean> gotoCharging() {
        boolean isCharging = rosWebService.isCharging();
        if (isCharging) {
            log.warn("正在充电中,无法继续返回充电");
            return new ResultUtil<Boolean>().setErrorMsg("正在充电中,无法继续返回充电");
        }
        if (rosWebService.isStopping()) {
            return ResultUtil.error("急停被按下，无法执行当前操作");
        }
        // 让电机使能：解决执行任务时返回充电需要点击两次的BUG
        iRobotMotorService.motorControl(true);
        // 先停止任务
        rosWebService.taskControl1(null, "2");
        // 再取消任务
        rosWebService.taskControl1(null, "4");
        Result<Boolean> result = rosWebService.gotoCharging();
        AUTO_DOCK_ERROR_CURRENT_TIME = 0;
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CHARGING_CONDITION, ChargingCondition.AUTO_CHARGING_MANUAL.getType().toString());
        return result;
    }

    @RequestMapping(value = "/disinfect/gotoOrigin", method = RequestMethod.GET)
    @ApiOperation(value = "返回原点")
    public Result<Boolean> gotoOrigin() {
        Result<Boolean> result = rosWebService.gotoOrigin();
        return result;
    }

    @RequestMapping(value = "disinfect/taskList", method = RequestMethod.GET)
    @ApiOperation(value = "获取消毒任务列表")
    public Result<List<DisinfectTaskVo>> disinfectTaskList() {
        List<DisinfectTaskVo> disinfectTaskVoList = rosWebService.disinfectTaskList();
        return ResultUtil.data(disinfectTaskVoList);
    }

    @RequestMapping(value = "disinfect/currentTaskInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取当前任务信息")
    public Result<DisinfectTaskVo> currentTaskInfo() {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        if (o != null) {
            DisinfectTaskVo disinfectTask = rosWebService.getDisinfectTask(o.toString());
            return ResultUtil.data(disinfectTask);
        } else {
            return ResultUtil.error("无法获取当前任务信息");
        }
    }

    @RequestMapping(value = "disinfect/allTaskList", method = RequestMethod.GET)
    @ApiOperation(value = "获取所有楼层地图的消毒任务列表")
    public Result<List<DisinfectTaskVo>> allDisinfectTaskList() {
        List<DisinfectTaskVo> disinfectTaskVoList = rosWebService.allDisinfectTaskList();
        return ResultUtil.data(disinfectTaskVoList);
    }

    @RequestMapping(value = "disinfect/taskTypes", method = RequestMethod.GET)
    @ApiOperation(value = "消毒任务类型列表")
    public Result<List<DisinfectTypeVo>> disinfectTaskTypes() {
        List<DisinfectTypeVo> disinfectTypeVos = new ArrayList<>();
        for (DisinfectTaskType value : DisinfectTaskType.values()) {
            DisinfectTypeVo disinfectTypeVo = new DisinfectTypeVo(value);
            disinfectTypeVos.add(disinfectTypeVo);
        }
        return ResultUtil.data(disinfectTypeVos);
    }

    @RequestMapping(value = "disinfect/taskDetail/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "获取消毒任务详情列表")
    public Result<List<DisinfectPointVo>> disinfectTaskDetailList(@PathVariable String id) {
        List<DisinfectPointVo> disinfectTaskInfo = rosWebService.getDisinfectTaskInfo(id);
        return ResultUtil.data(disinfectTaskInfo);
    }

    @RequestMapping(value = "disinfect/saveOrUpdateTask", method = RequestMethod.POST)
    @ApiOperation(value = "添加或更新消毒任务")
    public Result<Boolean> saveOrUpdateTask(@RequestBody DisinfectTaskVo disinfectTaskVo) {
        log.info("任务更新内容：{}", JSON.toJSONString(disinfectTaskVo));
        Result<Boolean> result = rosWebService.saveOrUpdateTask(disinfectTaskVo);
        return result;
    }


    @RequestMapping(value = "disinfect/deleteTask/{id}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除消毒任务")
    public Result<Boolean> deleteTask(@PathVariable String id) {
        //删除任务
        iRobotTaskService.delete(id);
        return ResultUtil.data(true);
    }

    @RequestMapping(value = "disinfect/deleteDisinfectPoint/{id}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除消毒点位")
    public Result<Boolean> deleteDisinfectPoint(@PathVariable String id) {
        iRobotDisinfectService.removeById(id);
        return ResultUtil.data(true);
    }

    @RequestMapping(value = "disinfect/getDefaultTaskId", method = RequestMethod.GET)
    @ApiOperation("获取默认任务id")
    public Result<String> getDefaultTaskId() {
        Object currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (currentMap == null || StrUtil.isBlank(currentMap.toString())) {
            rosWebService.sendVoicePrompt(SceneType.NO_MAP, null);
            return ResultUtil.error("地图不存在，请先创建地图或选择地图");
        }
        String data = iRobotTaskService.getDefaultTaskId();
        return ResultUtil.data(data);
    }

    @RequestMapping(value = "disinfect/start/{id}", method = RequestMethod.GET)
    @ApiOperation("开始消毒任务")
    public Result<Boolean> startDisinfect(@PathVariable String id) {
        AUTO_DOCK_ERROR_CURRENT_TIME = 0;
        Result<Boolean> result = rosWebService.startDisinfectService(id);
        return result;
    }

    @RequestMapping(value = "disinfect/startByPoints", method = RequestMethod.GET)
    @ApiOperation("开始消毒任务")
    public Result<Boolean> startDisinfectByPoints(@RequestParam String ids, @RequestParam(required = false) String orderId) {
        Result<Boolean> result = rosWebService.startDisinfectServiceByPoint(ids, orderId);
        return result;
    }

    @RequestMapping(value = "disinfect/manualDisinfect", method = RequestMethod.POST)
    @ApiOperation("手动消毒模式控制")
    public Result<Boolean> manualOperation(@RequestBody DisinfectManualOperationDto disinfectManualOperationDto) {
        Result<Boolean> result = rosWebService.manualDisinfect(disinfectManualOperationDto);
        return result;
    }

    @RequestMapping(value = "/isContinueTask", method = RequestMethod.GET)
    @ApiOperation(value = "低电量时，任务结束，充满电后是否继续任务")
    @ApiImplicitParam(name = "isContinue", value = "true:继续任务,false:取消任务", dataType = "Boolean", allowableValues = "true,false")
    public Result<Boolean> isContinueTask(boolean isContinue) {
        RedisUtil.hset(ROBOT_SYS_INFO, IS_CONTINUE_TASK, isContinue + "");
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/continueTaskStatus", method = RequestMethod.GET)
    @ApiOperation(value = "低电量时，任务结束，充满电后是否继续任务:true:继续任务,false:取消任务")
    public Result<Boolean> getContinueTaskStatus() {
        return new ResultUtil<Boolean>().setData(Boolean.TRUE.toString()
                .equalsIgnoreCase(RedisUtil.getHash(ROBOT_SYS_INFO, IS_CONTINUE_TASK)), "操作成功");
    }

    @RequestMapping(value = "/batchAddFixTimeTask", method = RequestMethod.POST)
    @ApiOperation(value = "批量添加定时任务---此接口仅为测试时方便添加定时任务使用")
    public Result<Boolean> batchAddFixTimeTask(@RequestBody DisinfectTaskVo disinfectTaskVo) {
        /*
         * 定点定时任务JSON数据
         * DisinfectTaskVo=============================================DisinfectTaskVo
         * (id=null,
         * floor=null,
         * name=定点任务111,
         * type=1,
         * fixedTime=true,
         * weekdays=3,
         * startTime=15:59,
         * period=null,
         * loops=1,
         * defaultTask=false,
         * disinfectRouteVo=DisinfectRouteVo(id=, ulray=false, spray=true, xt=true),
         * defineDisinfectRouteVos=null,
         * disinfectPointVos=
         * [DisinfectPointVo(id=null, robotLocationId=612058534573510656, locationInfo=自定义区域, locationCode=消毒1, ulray=false, spray=true, xt=true, disinfectTime=30, finish=false),
         * DisinfectPointVo(id=null, robotLocationId=612058798093242368, locationInfo=自定义区域, locationCode=消毒4, ulray=false, spray=true, xt=true, disinfectTime=30, finish=false),
         * DisinfectPointVo(id=null, robotLocationId=612058923913973760, locationInfo=自定义区域, locationCode=消毒5, ulray=false, spray=true, xt=true, disinfectTime=30, finish=false)],
         * executableStartTime=00:00:00, executableEndTime=24:00:00)
         */
        /*
        巡线定时任务JSON数据
        DisinfectTaskVo=============================================DisinfectTaskVo
        (id=null,
        floor=null,
        name=定时任务ztt,
        type=3,
        fixedTime=true,
        weekdays=3,
        startTime=14:30,
        period=null,
        loops=1,
        defaultTask=false,
//        此id是路径id
        disinfectRouteVo=DisinfectRouteVo(id=600513013811777536, ulray=false, spray=true, xt=true),
        defineDisinfectRouteVos=null,
        disinfectPointVos=[],
        executableStartTime=00:00:00,
        executableEndTime=24:00:00)
         */
        Integer hour = 0;
        String taskVoName = disinfectTaskVo.getName();
        for (int i = 0; i < 24; i++) {
            log.info("taskVoName:" + taskVoName);
            DisinfectTaskVo taskVo = disinfectTaskVo;
            taskVo.setFixedTime(true);
            taskVo.setName(taskVoName + i);
            taskVo.setWeekdays("1,2,3,4,5,6,7");
            String startTime = disinfectTaskVo.getStartTime();
            String[] split = startTime.split(":");
            hour = i;
            int minute = Integer.parseInt(split[1]);
            String finalStartTime = null;
            if (hour < 10) {
                finalStartTime = "0" + i + ":" + minute;
            } else {
                finalStartTime = i + ":" + minute;
            }
            taskVo.setStartTime(finalStartTime);
            rosWebService.saveOrUpdateTask(taskVo);
        }
        return ResultUtil.success("OK");
    }
}
