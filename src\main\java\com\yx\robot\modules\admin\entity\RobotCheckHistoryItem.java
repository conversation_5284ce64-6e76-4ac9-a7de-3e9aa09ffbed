package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_check_history_item")
@ApiModel(value = "机器人检测历史记录条目")
public class RobotCheckHistoryItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "历史检测记录id")
    private String historyId;

    @ApiModelProperty(value = "检测内容")
    private String title;

    @ApiModelProperty(value = "步骤")
    private Integer step;

    @ApiModelProperty(value = "数据响应")
    private String response;

    @ApiModelProperty(value = "是否检测")
    private String checked;

    @ApiModelProperty(value = "状态")
    private String status;

    @ApiModelProperty(value = "排序值")
    @Column(precision = 10, scale = 2)
    private BigDecimal sortOrder;
}