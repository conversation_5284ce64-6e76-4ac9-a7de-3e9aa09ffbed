package com.yx.robot.common.constant;

import com.yx.robot.common.enums.RingLightDefine;
import com.yx.robot.common.vo.ExpressionPriority;

import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 用于定义一个简单锁
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 15:14
 */
public class ControlStatusConstants {

    /**
     * 超出水位超出是否语音告警
     * true 发出，false 不发出
     */
    public static AtomicBoolean isOverSprayWiquid = new AtomicBoolean(true);

    /**
     * 超出水位超出语音告警控制条件
     */
    public static Integer isFullSprayWiquid = 1;

    /**
     * 是否发送过电机休眠或者唤醒指令
     * 0:未发送过指令
     * 1：发送过true唤醒指令
     * 2：发送过false休眠指令
     */
    public static AtomicInteger sendMotorSleepStatus = new AtomicInteger(0);

    /**
     * 0:未发送过指令
     */
    public static final int SEND_CMD_STATUS_NOT = 0;

    /**
     * 1:发送过true或者1指令
     */
    public static final int SEND_CMD_STATUS_TRUE = 1;

    /**
     * 2:发送过false或者2指令
     */
    public static final int SEND_CMD_STATUS_FALSE = 2;

    /**
     * 当前灯光
     * 每次更新灯光时，需判断类型是否相同，相同则不需要发送
     */
    public static RingLightDefine CURRENT_RING_LIGHT = null;

    /**
     * 设备执行任务时，需要执行的状态
     * <p>
     * 喷雾设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_TASK_NEED_STATUS_SPRAY = false;

    /**
     * 设备当前执行状态
     * 喷雾设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_CURRENT_STATUS_SPRAY = false;

    /**
     * 设备执行任务时，需要执行的状态
     * 脉冲设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_TASK_NEED_STATUS_PULSE = false;

    /**
     * 设备当前执行状态
     * 脉冲设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_CURRENT_STATUS_PULSE = false;

    /**
     * 设备执行任务时，需要执行的状态
     * 升降杆设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_TASK_NEED_STATUS_SHIELDING = false;

    /**
     * 设备当前执行状态
     * 升降杆设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_CURRENT_STATUS_SHIELDING = false;

    /**
     * 设备执行任务时，需要执行的状态
     * 紫外设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_TASK_NEED_STATUS_ULTRAVIOLET = false;

    /**
     * 设备当前执行状态
     * 紫外设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_CURRENT_STATUS_ULTRAVIOLET = false;

    /**
     * 设备执行任务时，需要执行的状态
     * 风扇设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_TASK_NEED_STATUS_FAN = false;

    /**
     * 设备当前执行状态
     * 风扇设备，true:开启设备，false:关闭设备
     */
    public static Boolean DEV_CURRENT_STATUS_FAN = false;

    /**
     * 执行了检测到活物操作
     * true: 打开了，false:未打开
     */
    public static Boolean CHECK_LIVING_OPEN = false;

    /**
     * 判断定位是否成功
     * <p>
     * true:成功 false:失败
     */
    public static boolean IS_LOCATION_SUCCESS = false;

    /**
     * 低电量是否执行充电操作 false:不需要 true:需要
     */
    public static boolean IS_NEED_GO_CHARGING_LOW_POWER = true;

    /**
     * 最近一次正常电量
     */
    public static Float LAST_CORRECT_BATTERY_VALUE = null;

    /**
     * 语音交流状态：在发送语音时会判断是否处于语音交流状态，如果处于此状态则停止其他语音播报
     */
    public static boolean TALK_STATUS = false;

    /**
     * 语音交流状态:将此键存入redis，过期时间为60秒，此60秒内机器人的对话回复优先级高于机器人自己播报的语音；
     */
    public static String TALK_STATUS_CTRL = "TALK_STATUS_CTRL";

    /**
     * 点阵表情---优先级
     */
    public static final ExpressionPriority EXPRESSION_PRIORITY = new ExpressionPriority();


    /**
     * 0、未开始 1、正在前往起始点 2、已经到达起始点 3、到达起始点异常 4、消毒任务进行中
     */
    public static int ROBOT_LINE_EXEC_STATUS = 0;

    /**
     * 取消任务:0:是，1:否
     */
    public static int IS_ROBOT_LINE_EXEC_STATUS_CANCEL = 0;

    /**
     * 尝试次数
     */
    public static Integer AUTO_DOCK_ERROR_TRY_TIME = 3;
    /**
     * 当前次数
     */
    public static Integer AUTO_DOCK_ERROR_CURRENT_TIME = 0;

    /**
     * 判断视频录像是否关闭
     */
    public static boolean VIDEO_IS_CLOSE = true;

    /**
     * 是否是巡线完成关闭
     * <p>
     * 循环任务结束，执行时关闭录像。否则巡线任务完成不关闭录像
     */
    public static boolean VIDEO_LINE_FINISH_IS_CLOSE = true;

    /**
     * 全局路径
     */
    public static String VIDEO_PATH = null;

    /**
     * 防跌落控制条件
     */
    public static boolean MOTOR_LOCK_CONTROL = false;

    /**
     * 防跌落触发后，是否需要继续任务。
     * true: 需要，false:不需要
     */
    public static boolean IS_MOTOR_LOCK_CONTINUE_TASK = true;

    /**
     * 雷达休眠状态;
     * true:雷达正在休眠：雷达处于关闭状态
     * false:雷达没有休眠：雷达处于开启状态
     */
    public static boolean LIDAR_DORMANCY_STATUE = false;

    /**
     * 建图状态控制条件
     */
    public static boolean CREATE_MAP_STATUE = false;

    /**
     * 防跌落语音播报控制条件
     */
    public static Integer MOTOR_LOCK_COUNT = 10;

    /**
     * 手动控制雷达标志
     * true: 手动控制
     * false: 自动控制
     */
    public static boolean CONTROL_SLEEP = false;

    /**
     * 定时重启机器人标志
     */
    public static String FIXED_TIME_RESTART_ROBOT = "restart_robot";

    /**
     * 上一次点阵表情发送
     */
    public static Integer LAST_EXPRESSION_SEND = 0;

    /**
     * 上一次灯光发送
     */
    public static RingLightDefine LAST_LIGHT_SEND = null;
}
