package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.enums.AutoDockOptionType;
import com.yx.robot.common.enums.RosResult;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.IRobotChargingService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import static com.yx.robot.common.constant.ServiceConstants.AUTO_DOCK;
import static com.yx.robot.common.constant.TopicConstants.UDRIVE_AUTO_DOCK_STATE;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/19 15:15
 */
@Service
@Slf4j
public class IRobotChargingServiceImpl implements IRobotChargingService {

    private final RosBridgeService rosBridgeService;

    @Autowired
    public IRobotChargingServiceImpl(RosBridgeService rosBridgeService) {
        this.rosBridgeService = rosBridgeService;

    }

    /**
     * 保存充电位置，并返回充电桩位置点，和充电导航点
     *
     * @return 保存充电桩位置发牛结果
     */
    @Override
    public SaveDockPositionMsg saveDockPosition() {
        // 记录定位点
        String s = rosBridgeService.callService(ServiceConstants.SAVE_DOCK_POSITION,
                "", "{}");
        if (StrUtil.isNotEmpty(s)) {
            SaveDockPositionMsg saveDockPositionMsg = JSON.parseObject(s, SaveDockPositionMsg.class);
            if (saveDockPositionMsg.getRet() == RosResult.SUCCESS.getCode()) {
                log.info("设置充电桩成功");
                return saveDockPositionMsg;
            } else {
                log.info("设置充电桩失败");
            }
        } else {
            log.info("设置充电桩服务调用失败");
        }
        return null;
    }

    /**
     * 对接充电桩
     *
     * @param option 操作类型
     * @return 接充电桩返回结果
     */
    @Override
    public _RobotDockRep autoDockControl(Integer option) {
        _RobotDockReq robotDockReq = new _RobotDockReq();
        robotDockReq.cmd = option;
        String s2 = rosBridgeService.callService(AUTO_DOCK, Message.getMessageType(_RobotDockReq.class), JSON.toJSONString(robotDockReq));
        if (StrUtil.isNotEmpty(s2)) {
            return JSON.parseObject(s2, _RobotDockRep.class);
        }
        return null;
    }

    /**
     * 开始对接对接充电桩
     * 2：对接充电桩 3：脱离充电桩 4：取消充电
     *
     * @return 接充电桩返回结果
     */
    @Override
    public _RobotDockRep startDock() {
        return autoDockControl(AutoDockOptionType.START.getType());
    }

    /**
     * 脱离充电桩
     * 2：对接充电桩 3：脱离充电桩 4：取消充电
     *
     * @return 接充电桩返回结果
     */
    @Override
    public _RobotDockRep outDock() {
        _RobotDockRep robotDockRep = autoDockControl(AutoDockOptionType.OUT_DOCK.getType());
        if (ObjectUtil.isNotNull(robotDockRep) && robotDockRep.ret == RosResult.SUCCESS.getCode()) {
            log.info("脱离 充电桩成功");
        } else {
            log.info("脱离 充电桩失败");
//            偶发性的脱离充电桩失败，日志不打印脱离充电桩服务调用
            log.info("robotDockRep:"+robotDockRep);
        }
        return robotDockRep;
    }

    /**
     * 取消对接充电桩
     *
     * @return 接充电桩返回结果
     */
    @Override
    public _RobotDockRep cancelDock() {
        log.info("取消对接充电装");
        return autoDockControl(AutoDockOptionType.CANCEL.getType());
    }

    /**
     * 获取对接充电桩状态
     *
     * @return 充电桩对接状态
     */
    @Override
    public _UdriveAutoDockState getAutoDockState() {
        String autoDock = RedisUtil.getTopicValue(UDRIVE_AUTO_DOCK_STATE);
        if (StringUtils.isBlank(autoDock)) {
            return null;
        }
        return JSONObject.parseObject(autoDock, _UdriveAutoDockState.class);
    }

}
