package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.DisinfectTaskType;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.enums.RobotType;
import com.yx.robot.common.enums.TaskType;
import com.yx.robot.modules.admin.dao.mapper.RobotDisinfectMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotRouteMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotTaskMapper;
import com.yx.robot.modules.admin.dto.RobotRouteInfoDto;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import com.yx.robot.modules.admin.service.IRobotRouteService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.service.IRobotTaskService;
import com.yx.robot.modules.admin.service.IRobotWorldPositionService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.RobotMapVo;
import com.yx.robot.modules.admin.vo.RobotRouteVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.yx.robot.common.constant.ControlStatusConstants.CREATE_MAP_STATUE;
import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 机器人路线接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotRouteServiceImpl extends ServiceImpl<RobotRouteMapper, RobotRoute> implements IRobotRouteService {

    @Autowired
    private RobotRouteMapper robotRouteMapper;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private RobotDisinfectMapper robotDisinfectMapper;

    @Autowired
    private RobotTaskMapper robotTaskMapper;

    @Autowired
    private IRobotPositionService robotPositionService;

    @Autowired
    private IRobotWorldPositionService robotWorldPositionService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Autowired
    private IRobotTaskService robotTaskService;

    private List<_Pose> handleRoutePoses(List<_Pose> poses) {
        List<_Pose> result = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(poses)) {
            Object mapId = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
            // 保存路线
            if (mapId != null && StrUtil.isNotBlank(mapId.toString())) {
                List<RobotPosition> robotPositionList = robotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                        .eq(RobotPosition::getMapId, mapId.toString())
                        .eq(RobotPosition::getType, RobotPositionType.START_POSITION.getType()));
                RobotPosition one = null;
                if (CollectionUtil.isNotEmpty(robotPositionList)) {
                    one = robotPositionList.get(robotPositionList.size() - 1);
                }
                if (one != null) {
                    RobotWorldPosition robotWorldPosition = robotWorldPositionService.getById(one.getWorldPoseId());
                    if (robotWorldPosition != null) {
                        Double targetX = robotWorldPosition.getPositionX();
                        Double targetY = robotWorldPosition.getPositionY();
                        for (int i = 0; i < poses.size(); i++) {
                            _Pose pose = poses.get(i);
                            if (pose != null) {
                                Double x = pose.position.x;
                                Double y = pose.position.y;
                                double tempX = x > targetX ? (x - targetX) : (targetX - x);
                                double tempY = y > targetY ? (y - targetY) : (targetY - y);
                                double distance = Math.sqrt(tempX * tempX + tempY * tempY);
                                if (distance >= 1.5 && i != 0) {
                                    result.add(pose);
                                }
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    /**
     * 自动保存路径并新建路线任务
     */
    @Override
    public void saveRouteAndTask(RobotMapVo robotMapVo) {
        RobotRouteVo robotRouteVo = new RobotRouteVo();
        Object mapId = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        // 保存路线
        if (mapId != null && StrUtil.isNotBlank(mapId.toString())) {
            robotRouteVo.setMapId(mapId.toString());
            robotRouteVo.setLocationInfo(robotMapVo.getLocationInfo());
            robotRouteVo.setLocationCode(robotMapVo.getLocationCode());
            robotRouteVo.setPoses(robotMapVo.getPoses());
            robotRouteVo.setIsDefault(SUCCESS);
            this.remove(new LambdaQueryWrapper<RobotRoute>().eq(RobotRoute::getMapId, mapId));
            RobotRoute robotRoute = this.save(robotRouteVo);
            // 新建默认任务
            RobotTask robotTask = rosWebService.createDefaultDisinfectLineTask();
            // 保存任务相关数据
            RobotDisinfect robotDisinfect = new RobotDisinfect();
            robotDisinfect.setDisinfectTime(null);
            robotDisinfect.setSpray(SUCCESS);
            // 如果是X1则默认任务不开启脉冲
            if(RobotType.X1.getType().equals(RobotBaseInfoConstant.type)){
                robotDisinfect.setUlray(FAIL);
            }else{
                robotDisinfect.setUlray(SUCCESS);
            }
            robotDisinfect.setXt(SUCCESS);
            robotDisinfect.setDisinfectTime(0);
            robotDisinfect.setRobotTaskId(robotTask.getId());
            robotDisinfect.setRobotLocationId(robotRoute.getId());
            robotDisinfect.setCreateTime(new Date());
            robotDisinfect.setUpdateTime(new Date());
            robotDisinfectMapper.insert(robotDisinfect);
        }
    }

    /**
     * 获取所有路线二级结构
     *
     * @return
     */
    @Override
    public List<RobotRouteInfoDto> getAllRoute() {
        List<RobotRouteInfoDto> robotRouteInfoDtoList = new ArrayList<>();
        try {
            Object mapId = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
            if (mapId == null) {
                return robotRouteInfoDtoList;
            }
            LambdaQueryWrapper<RobotRoute> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RobotRoute::getMapId, mapId.toString());
            List<RobotRoute> robotRouteList = robotRouteMapper.selectList(queryWrapper);
            if (CollectionUtil.isEmpty(robotRouteList)) {
                return robotRouteInfoDtoList;
            }
            Set<String> locationInfos = new LinkedHashSet<>();
            for (RobotRoute robotRoute : robotRouteList) {
                locationInfos.add(robotRoute.getLocationInfo());
            }
            for (String locationInfo : locationInfos) {
                RobotRouteInfoDto robotRouteInfoDto = new RobotRouteInfoDto();
                robotRouteInfoDto.setLocationInfo(locationInfo);
                List<Map<String, Object>> locationCodes = new ArrayList<>();
                for (RobotRoute robotRoute : robotRouteList) {
                    if (robotRoute.getLocationInfo().equals(locationInfo)) {
                        Map<String, Object> locationCode = new HashMap<>();
                        locationCode.put("id", robotRoute.getId());
                        locationCode.put("locationCode", robotRoute.getLocationCode());
                        locationCodes.add(locationCode);
                    }
                }
                robotRouteInfoDto.setLocationCodes(locationCodes);
                robotRouteInfoDtoList.add(robotRouteInfoDto);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return robotRouteInfoDtoList;
    }

    /**
     * 保存路线
     *
     * @param robotRouteVo
     * @return
     */
    @Override
    public RobotRoute save(RobotRouteVo robotRouteVo) {
        Object o = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (o == null || StrUtil.isBlank(o.toString())) {
            return null;
        }
        List<RobotRoute> robotRouteList = robotRouteMapper.selectList(new LambdaQueryWrapper<RobotRoute>().eq(RobotRoute::getMapId, o.toString()));
        List<_Pose> poses = robotRouteVo.getPoses();
//        if(CollectionUtil.isEmpty(poses)) {
//            String s = stringRedisTemplate.opsForValue().get(TOPIC + "::" + TopicConstants.PATH_RECORD);
//            if(StrUtil.isNotBlank(s)) {
//                _Path path = JSONObject.parseObject(s, _Path.class);
//                _PoseStamped[] poseStampeds = path.poses;
//                for (_PoseStamped poseStamped : poseStampeds) {
//                    poses.add(poseStamped.pose);
//                }
//            }
//        }
        if (CollectionUtil.isEmpty(poses)) {
            log.warn("路径不能为空");
            return null;
        }
        poses = handleRoutePoses(poses);
        RobotRoute robotRoute = new RobotRoute();
        robotRoute.setMapId(o.toString());
        robotRoute.setLocationInfo(robotRouteVo.getLocationInfo());
        robotRoute.setLocationCode(robotRouteVo.getLocationCode());
        robotRoute.setIsDefault(robotRouteVo.getIsDefault());
        if (robotRouteVo.getIsDefault() != null) {
            robotRoute.setIsDefault(robotRouteVo.getIsDefault());
        }
        robotRoute.setPositionNumber(poses.size());
        robotRoute.setPositions(JSONObject.toJSONString(poses));
        robotRoute.setOrderNumber(robotRouteList.size() + 1 + "");
        robotRoute.setCreateTime(new Date());
        robotRoute.setUpdateTime(new Date());
        robotRouteMapper.insert(robotRoute);
        return robotRoute;
    }


    /**
     * 编辑路线
     *
     * @param robotRouteVo
     * @return
     */
    @Override
    public boolean edit(RobotRouteVo robotRouteVo) {
        Object currentMap = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (currentMap == null || StrUtil.isBlank(currentMap.toString())) {
            return false;
        }

        List<_Pose> poses = robotRouteVo.getPoses();
//        if(CollectionUtil.isEmpty(poses)) {
//            String s = stringRedisTemplate.opsForValue().get(TOPIC + "::" + TopicConstants.PATH_RECORD);
//            if(StrUtil.isNotBlank(s)) {
//                _Path path = JSONObject.parseObject(s, _Path.class);
//                _PoseStamped[] poseStampeds = path.poses;
//                for (_PoseStamped poseStamped : poseStampeds) {
//                    poses.add(poseStamped.pose);
//                }
//            }
//        }
        if (CollectionUtil.isEmpty(poses)) {
            log.warn("路径不能为空");
            Asserts.check(!CollectionUtil.isEmpty(poses), "路径不能为空");
//            return false;
        }
        RobotRoute robotRoute = robotRouteMapper.selectById(robotRouteVo.getId());
        robotRoute.setMapId(currentMap.toString());
        robotRoute.setLocationInfo(robotRouteVo.getLocationInfo());
        robotRoute.setLocationCode(robotRouteVo.getLocationCode());
        if (robotRouteVo.getIsDefault() != null) {
            robotRoute.setIsDefault(robotRouteVo.getIsDefault());
        }
        if (CollectionUtil.isNotEmpty(poses)) {
            robotRoute.setPositionNumber(poses.size());
            robotRoute.setPositions(JSONObject.toJSONString(poses));
        }
        robotRoute.setUpdateTime(new Date());
        robotRouteMapper.updateById(robotRoute);
        List<RobotRoute> robotRoutes = robotRouteMapper.selectList(new LambdaQueryWrapper<RobotRoute>()
                .eq(RobotRoute::getMapId, currentMap.toString())
                .eq(RobotRoute::getIsDefault, SUCCESS));
        if (CollectionUtil.isNotEmpty(robotRoutes)) {
            for (RobotRoute route : robotRoutes) {
                // 更新之前的路径
                if (!route.getId().equals(robotRoute.getId())) {
                    route.setIsDefault(FAIL);
                    robotRouteMapper.updateById(route);
                }
            }
        }
        String defaultTaskId = robotTaskService.getDefaultTaskId();
        List<RobotDisinfect> robotDisinfects = robotDisinfectMapper.selectList(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, defaultTaskId));
        if (CollectionUtil.isEmpty(robotDisinfects)) {
            RobotDisinfect robotDisinfect = new RobotDisinfect();
            robotDisinfect.setDisinfectTime(null);
            robotDisinfect.setSpray(SUCCESS);
            robotDisinfect.setUlray(SUCCESS);
            robotDisinfect.setXt(SUCCESS);
            robotDisinfect.setDisinfectTime(0);
            robotDisinfect.setRobotTaskId(defaultTaskId);
            robotDisinfect.setRobotLocationId(robotRoute.getId());
            robotDisinfect.setCreateTime(new Date());
            robotDisinfect.setUpdateTime(new Date());
            robotDisinfectMapper.insert(robotDisinfect);
        } else {
            RobotDisinfect robotDisinfect = robotDisinfects.get(0);
            robotDisinfect.setRobotLocationId(robotRoute.getId());
            robotDisinfectMapper.updateById(robotDisinfect);
        }
        return true;
    }

    /**
     * 路径录制操作
     *
     * @return
     */
    @Override
    public boolean initPathRecord(Integer operation) {
        _PathRecordInitReq pathRecordInitReq = new _PathRecordInitReq();
        pathRecordInitReq.cmd = operation;
        String s = rosBridgeService.callService(ServiceConstants.INIT_PATH_RECORD, Message.getMessageType(_PathRecordInitReq.class), JSONObject.toJSONString(pathRecordInitReq));
        if (StrUtil.isNotBlank(s)) {
            _PathRecordInitRep pathRecordInitRep = JSONObject.parseObject(s, _PathRecordInitRep.class);
            if (pathRecordInitRep.ret == 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取默认路径id
     */
    @Override
    public String getDefaultRouteId() {
        String data = "";
        Object currentMapObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (currentMapObj != null && StrUtil.isNotBlank(currentMapObj.toString())) {
            List<RobotRoute> robotRouteList = this.list(new LambdaQueryWrapper<RobotRoute>()
                    .eq(RobotRoute::getIsDefault, "0")
                    .eq(RobotRoute::getMapId, currentMapObj.toString()));
            if (CollectionUtil.isNotEmpty(robotRouteList)) {
                data = robotRouteList.get(0).getId();
            }
        }
        return data;
    }
}