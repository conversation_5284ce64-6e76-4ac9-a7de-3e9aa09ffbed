package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.yx.robot.modules.admin.message._Pose;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_world_position")
@ApiModel(value = "机器人列表")
public class RobotWorldPosition extends BaseEntity {

    private static final long serialVersionUID = 1L;

    public RobotWorldPosition() {}

    public RobotWorldPosition(_Pose pose) {
        this.positionX = pose.position.x;
        this.positionY = pose.position.y;
        this.positionZ = pose.position.z;
        this.orientationW = pose.orientation.w;
        this.orientationX = pose.orientation.x;
        this.orientationY = pose.orientation.y;
        this.orientationZ = pose.orientation.z;
        this.setCreateTime(new Date());
        this.setUpdateTime(new Date());
    }

    @ApiModelProperty(value = "uuid")
    private String uuId;

    @ApiModelProperty(value = "位置x轴数据")
    private Double positionX;

    @ApiModelProperty(value = "位置y轴数据")
    private Double positionY;

    @ApiModelProperty(value = "位置z轴数据")
    private Double positionZ;

    @ApiModelProperty(value = "方向w轴数据")
    private Double orientationW;

    @ApiModelProperty(value = "方向x轴数据")
    private Double orientationX;

    @ApiModelProperty(value = "方向y轴数据")
    private Double orientationY;

    @ApiModelProperty(value = "方向z轴数据")
    private Double orientationZ;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}