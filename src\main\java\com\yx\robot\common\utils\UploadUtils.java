package com.yx.robot.common.utils;

import cn.hutool.core.io.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import sun.misc.BASE64Decoder;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.InputStream;

/**
 * 上传工具类
 * @Data 2019-10-18
 * <AUTHOR>
 */
@Slf4j
@Component
public class UploadUtils {

    /**
     * 上传图片
     * @param baseData
     * @param fileName
     * @param pathName
     * @return
     */
    public boolean uploadImage(String baseData,String fileName, String pathName) {
        //实体部分数据
        String data = "";
        InputStream inputStream = null;
        if(baseData == null || "".equals(baseData)) {
            log.warn("上传图片失败,图片数据为空");
            return false;
        }
        String [] d = baseData.split("base64,");
        if(d != null && d.length == 2) {
            data = d[1];
        }else {
            log.warn("数据不合法");
            return false;
        }
        BASE64Decoder decoder = new BASE64Decoder();
        try {
            byte[] bytes = decoder.decodeBuffer(data.trim());
            inputStream = new ByteArrayInputStream(bytes);
            FileUtil.writeFromStream(inputStream,new File(pathName + "/" + fileName));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

}
