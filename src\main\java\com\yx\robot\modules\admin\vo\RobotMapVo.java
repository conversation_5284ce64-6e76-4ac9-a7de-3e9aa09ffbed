package com.yx.robot.modules.admin.vo;

import com.yx.robot.modules.admin.message._Point;
import com.yx.robot.modules.admin.message._Pose;
import lombok.Data;

import java.util.List;

/**
 * 地图保存视图层
 *
 * <AUTHOR>
 * @date 2020/11/26
 */
@Data
public class RobotMapVo {
    /**
     * 地图名称
     */
    public String mapName;

    /**
     * 临时地图名称
     * 1、直接保存地图
     * 2、选用存档地图
     */
    public Integer type;

    /**
     * 服务地点
     */
    public String storeName;

    /**
     * 楼层
     */
    public Integer floor;

    /**
     * 路径保存
     */
    public boolean routeVisible;

    /**
     * 位置区域
     */
    public String locationInfo;

    /**
     * 位置编号
     */
    public String locationCode;

    /**
     * 路径包点
     */
    public List<_Pose> poses;
}
