package com.yx.robot.modules.admin.message;

@MessageType(string = "sensor_msgs/BatteryState")
public class _BatteryState extends Message {
    // Power supply status constants
    public short POWER_SUPPLY_STATUS_UNKNOWN = 0;
    public short POWER_SUPPLY_STATUS_CHARGING = 1;
    public short POWER_SUPPLY_STATUS_DISCHARGING = 2;
    public short POWER_SUPPLY_STATUS_NOT_CHARGING = 3;
    public short POWER_SUPPLY_STATUS_FULL = 4;

    // Power supply health constants
    public short POWER_SUPPLY_HEALTH_UNKNOWN = 0;
    public short POWER_SUPPLY_HEALTH_GOOD = 1;
    public short POWER_SUPPLY_HEALTH_OVERHEAT = 2;
    public short POWER_SUPPLY_HEALTH_DEAD = 3;
    public short POWER_SUPPLY_HEALTH_OVERVOLTAGE = 4;
    public short POWER_SUPPLY_HEALTH_UNSPEC_FAILURE = 5;
    public short POWER_SUPPLY_HEALTH_COLD = 6;
    public short POWER_SUPPLY_HEALTH_WATCHDOG_TIMER_EXPIRE = 7;
    public short POWER_SUPPLY_HEALTH_SAFETY_TIMER_EXPIRE = 8;

    // Power supply technology (chemistry) constants
    public short POWER_SUPPLY_TECHNOLOGY_UNKNOWN = 0;
    public short POWER_SUPPLY_TECHNOLOGY_NIMH = 1;
    public short POWER_SUPPLY_TECHNOLOGY_LION = 2;
    public short POWER_SUPPLY_TECHNOLOGY_LIPO = 3;
    public short POWER_SUPPLY_TECHNOLOGY_LIFE = 4;
    public short POWER_SUPPLY_TECHNOLOGY_NICD = 5;
    public short POWER_SUPPLY_TECHNOLOGY_LIMN = 6;

    public Header header;
    // Voltage in Volts (Mandatory) 电压
    public float voltage;
    // Negative when discharging (A)  (If unmeasured NaN) 电流
    public float current;
    // Current charge in Ah  (If unmeasured NaN)
    public float charge;
    // Capacity in Ah (last full capacity)  (If unmeasured NaN)
    public float capacity;
    // Capacity in Ah (design capacity)  (If unmeasured NaN)
    public float design_capacity;
    // Charge percentage on 0 to 1 range  (If unmeasured NaN)
    public float percentage;
    // The charging status as reported. Values defined above
    public short power_supply_status;
    // The battery health metric. Values defined above
    public short power_supply_health;
    // The battery chemistry. Values defined above
    public short power_supply_technology;
    // True if the battery is present
    public boolean present;
    // An array of individual cell voltages for each cell in the pack
    // If individual voltages unknown but number of cells known set each to NaN
    public float[] cell_voltage;
    // The location into which the battery is inserted. (slot number or plug)
    public String location;
    // The best approximation of the battery serial number
    public String serial_number;


}
