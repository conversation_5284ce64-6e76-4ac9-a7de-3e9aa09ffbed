package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._Bool;
import com.yx.robot.modules.admin.message._MotorControlStatus;
import com.yx.robot.modules.admin.service.IRobotMotorService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import static com.yx.robot.common.constant.ControlStatusConstants.*;
import static com.yx.robot.common.constant.RosWebConstants.TOPIC;
import static com.yx.robot.common.constant.TopicConstants.MOTOR_CONTROL;
import static com.yx.robot.common.constant.TopicConstants.MOTOR_SLEEP;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/1 11:38
 */
@Slf4j
@AllArgsConstructor
@Service
public class IRobotMotorServiceImpl implements IRobotMotorService {

    private RosBridgeService rosBridgeService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 设置电机是否休眠
     * <p>
     * 和ros建立通讯，将消息发送到ros
     * <p>
     * 电机充电成功时，设置电机休眠。
     * 电机充电脱离充电装前，设置电机唤醒
     * <p>
     * 如果正在充电状态，且没有发送过休眠指令则发送休眠
     * 如果没有在在充电，且没有发送过唤醒指令则发送唤醒指令
     *
     * @param isNotSleep false:休眠，true:唤醒
     * @return true:设置成功， false:设置失败
     */
    @Override
    public boolean setMotorSleep(Boolean isNotSleep) {
        _Bool motorSleepBool = new _Bool();
        motorSleepBool.data = isNotSleep;
        // 是否发送指令变量 true发送，false不发送
        boolean flg = isNotSleep ? sendMotorSleepStatus.get() != SEND_CMD_STATUS_TRUE
                : sendMotorSleepStatus.get() != SEND_CMD_STATUS_FALSE;

        if (sendMotorSleepStatus.get() == SEND_CMD_STATUS_NOT || flg) {
            sendMotorSleepStatus.set(isNotSleep ? SEND_CMD_STATUS_TRUE : SEND_CMD_STATUS_FALSE);
            log.info(isNotSleep ? "设置电机唤醒" : "设置电机休眠");
            rosBridgeService.publish(MOTOR_SLEEP, Message.getMessageType(_Bool.class), JSON.toJSONString(motorSleepBool));
        }
        return true;
    }

    /**
     * 电机操作
     *
     * @param operate 控制电机(true)/不控制(false)
     * @return true:操作成功,false:操作失败
     * 返回值未被使用过；
     */
    @Override
    public boolean motorControl(boolean operate) {
        _MotorControlStatus motorControlStatus = new _MotorControlStatus();
        motorControlStatus.data = operate;
        log.info("电机控制:" + operate);
        //如果急停没被按下,允许程序控制电机
        if (!getEmergencyState()) {
            rosBridgeService.publish(MOTOR_CONTROL, Message.getMessageType(_MotorControlStatus.class), JSON.toJSONString(motorControlStatus));
        } else {
            //暂停任务后会有一个取消电机控制无法执行（不确定是否会引起BUG）
            log.info("急停被按下，逻辑规定不允许操控电机！");
        }
        return true;
    }

    /**
     * 获取急停开关状态
     *
     * @return true 被按下  false 没被按下
     */
    @Override
    public Boolean getEmergencyState() {
        String emergencyResult = redisTemplate.opsForValue().get(TOPIC + "::" + TopicConstants.EMERGENCY_BUTTON);
        if (StrUtil.isNotEmpty(emergencyResult)) {
            _Bool bool = JSON.parseObject(emergencyResult, _Bool.class);
            return bool.data;
        }
        return null;
    }
}
