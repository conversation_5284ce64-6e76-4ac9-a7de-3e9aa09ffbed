package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.dao.mapper.RobotPositionMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotWorldPositionMapper;
import com.yx.robot.modules.admin.dto.YxRobotWorldPositionDto;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.entity.RobotWorldPosition;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.IRobotWorldPositionService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Random;

/**
 * 机器人列表接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class IRobotWorldPositionServiceImpl extends ServiceImpl<RobotWorldPositionMapper, RobotWorldPosition> implements IRobotWorldPositionService {

    private RobotWorldPositionMapper robotWorldPositionMapper;

    private RobotPositionMapper robotPositionMapper;

    /**
     * 更新世界坐标系的值
     *
     * @param yxRobotWorldPositionDto
     * @return
     */
    @Override
    public boolean updateRobotWorldPosition(YxRobotWorldPositionDto yxRobotWorldPositionDto) {
        String robotWorldPositionId = yxRobotWorldPositionDto.getRobotWorldPositionId();
        RobotWorldPosition robotWorldPosition = robotWorldPositionMapper.selectById(robotWorldPositionId);
        if (robotWorldPosition != null) {
            robotWorldPosition.setPositionX(robotWorldPosition.getPositionX());
            robotWorldPosition.setPositionY(robotWorldPosition.getPositionY());
            robotWorldPosition.setPositionZ(robotWorldPosition.getPositionZ());
            robotWorldPosition.setOrientationW(robotWorldPosition.getOrientationW());
            robotWorldPosition.setOrientationX(robotWorldPosition.getOrientationX());
            robotWorldPosition.setOrientationY(robotWorldPosition.getOrientationY());
            robotWorldPosition.setOrientationZ(robotWorldPosition.getOrientationZ());
            robotWorldPositionMapper.updateById(robotWorldPosition);
            return true;
        }
        return false;
    }

    /**
     * 通过世界坐标系获取 机器人位置信息
     *
     * @param robotWorldPosition 世界坐标系
     * @return 机器人位置信息
     */
    @Override
    public _PoseStamped getPoseStamped(RobotWorldPosition robotWorldPosition) {
        _PoseStamped poseStamped = new _PoseStamped();
        poseStamped.header = new Header();
        poseStamped.header.frame_id = DateUtil.format(new Date(), DatePattern.PURE_DATETIME_PATTERN);
        poseStamped.header.seq = new Random().nextInt(10);
        poseStamped.header.stamp = new TimePrimitive();
        poseStamped.header.stamp.secs = 0;
        poseStamped.header.stamp.nsecs = 0;
        poseStamped.pose = new _Pose();
        poseStamped.pose.position = new _Point();
        poseStamped.pose.position.x = robotWorldPosition.getPositionX();
        poseStamped.pose.position.y = robotWorldPosition.getPositionY();
        poseStamped.pose.position.z = robotWorldPosition.getPositionZ();
        poseStamped.pose.orientation = new _Quaternion();
        poseStamped.pose.orientation.w = robotWorldPosition.getOrientationW();
        poseStamped.pose.orientation.x = robotWorldPosition.getOrientationX();
        poseStamped.pose.orientation.y = robotWorldPosition.getOrientationY();
        poseStamped.pose.orientation.z = robotWorldPosition.getOrientationZ();
        return poseStamped;
    }

    /**
     * 通过停靠点ID获取机器人位置信息
     *
     * @param positionId 停靠点
     * @return 机器人位置信息
     */
    @Override
    public _PoseStamped getPoseStamped(String positionId) {
        RobotPosition robotPosition = robotPositionMapper.selectById(positionId);
        return getPoseStamped(robotWorldPositionMapper.selectById(robotPosition.getWorldPoseId()));
    }
}