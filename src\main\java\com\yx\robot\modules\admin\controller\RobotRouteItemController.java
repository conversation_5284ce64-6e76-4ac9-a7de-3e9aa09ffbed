package com.yx.robot.modules.admin.controller;

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotRouteItem;
import com.yx.robot.modules.admin.service.IRobotRouteItemService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人路线条目管理接口")
@RequestMapping("/yx/api-v1/robotRouteItem")
@Transactional
public class RobotRouteItemController {

    @Autowired
    private IRobotRouteItemService iRobotRouteItemService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotRouteItem> get(@PathVariable String id){

        RobotRouteItem robotRouteItem = iRobotRouteItemService.getById(id);
        return new ResultUtil<RobotRouteItem>().setData(robotRouteItem);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotRouteItem>> getAll(){

        List<RobotRouteItem> list = iRobotRouteItemService.list();
        return new ResultUtil<List<RobotRouteItem>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotRouteItem>> getByPage(@ModelAttribute PageVo page){

        IPage<RobotRouteItem> data = iRobotRouteItemService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotRouteItem>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotRouteItem> saveOrUpdate(@ModelAttribute RobotRouteItem robotRouteItem){

        if(iRobotRouteItemService.saveOrUpdate(robotRouteItem)){
            return new ResultUtil<RobotRouteItem>().setData(robotRouteItem);
        }
        return new ResultUtil<RobotRouteItem>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotRouteItemService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
