package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotDisinfect;
import com.yx.robot.modules.admin.vo.DisinfectPointVo;
import com.yx.robot.modules.admin.vo.PositionsVo;
import com.yx.robot.modules.admin.vo.RouteVo;

import java.util.List;

/**
 * 机器人消毒任务条目接口
 *
 * <AUTHOR>
 */
public interface IRobotDisinfectService extends IService<RobotDisinfect> {

    /**
     * 路径消毒
     *
     * @param id        机器人路径ID
     * @param operation (1: 开始任务 2: 暂停任务 3：继续任务 4：取消任务)
     * @return true:成功，失败
     */
    boolean routeDisinfectOperation(String id, Integer operation);

    /**
     * 获取局部消毒详情
     *
     * @return list
     */
    List<DisinfectPointVo> getLocalDisinfectTaskInfo();

    /**
     * 更新局部消毒详情
     *
     * @param disinfectPointVo
     * @return
     */
    boolean updateLocalDisinfect(DisinfectPointVo disinfectPointVo);

    /**
     * 是否进行喷雾消毒
     *
     * @param list 本次任务所执行的消毒设备和点位关系
     * @return true：是，false:否
     */
    boolean isSprayDisinfect(List<RobotDisinfect> list);

    /**
     * 是否进行喷雾消毒
     *
     * @param taskId 当前任务ID
     * @return true：是，false:否
     */
    boolean isSprayDisinfect(String taskId);

    /**
     * 是否进行紫外或者脉冲消毒
     *
     * @param list 本次任务所执行的消毒设备和点位关系
     * @return true：是，false:否
     */
    boolean isUltravioletDisinfect(List<RobotDisinfect> list);


    /**
     * 根据任务ID获取消毒点位ID信息
     *
     * @param id
     * @return
     */
    List<PositionsVo> getPoseInfoBytaskId(String id);

    /**
     * 根据任务ID获取消毒路径信息
     *
     * @param id
     * @return
     */
    RouteVo getRouteInfoByTaskId(String id);

}