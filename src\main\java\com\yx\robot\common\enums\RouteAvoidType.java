package com.yx.robot.common.enums;

/**
 * 路径消毒类型
 *
 * <AUTHOR>
 * @date 2021/10/15
 */
public enum RouteAvoidType {

    /**
     * 0,"非自动避障"
     */
    NOT_AUTO_AVOID(0, "非自动避障"),

    /**
     * 1,"自动避障"
     */
    AUTO_AVOID(1, "自动避障");

    public final Integer type;

    public final String label;

    RouteAvoidType(Integer type, String label) {
        this.type = type;
        this.label = label;
    }

    public Integer getType() {
        return type;
    }

    public String getLabel() {
        return label;
    }
}
