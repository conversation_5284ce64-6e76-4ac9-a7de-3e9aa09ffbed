package com.yx.robot.test;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/1 11:31
 */
@Slf4j
public class TimeTest {
    public static final String DATE_FORMAT = "yyyy-MM-dd HH:mm:ss";

    private static final long TICKS_AT_EPOCH_NT = 116444736000000000L;

    private static final long TICKS_PER_MILLISECOND = 10000;

    // 注意要使用的时区
    private static TimeZone TIME_ZONE = TimeZone.getDefault();

    private static final long TICKS_AT_EPOCH = 621355968000000000L;

    /**
     * NT时间戳转换
     * @param str NT时间戳
     * @return String 1.空返回null,2."0"返回"0",3.返回yyyy-MM-dd HH:mm:ss格式字符串
     */
    public static String fromNTTimeToJdate(String str) {
        String zero = "0";
        if (StrUtil.isEmpty(str)) {
            return null;
        } else if (zero.equals(str)) {
            return zero;
        } else {
            Calendar calendar = Calendar.getInstance(TIME_ZONE);
            calendar.setTimeInMillis((Long.parseLong(str) - TICKS_AT_EPOCH_NT) / TICKS_PER_MILLISECOND);
            calendar.setTimeInMillis(calendar.getTimeInMillis() - calendar.getTimeZone().getRawOffset());
            return (new SimpleDateFormat(DATE_FORMAT)).format(calendar.getTime());
        }
    }

    /**
     * .net18位时间戳转换
     * @param str .net18位时间戳
     * @return String 1.空返回null,2."0"返回"0",3.返回yyyy-MM-dd HH:mm:ss格式字符串
     */
    public static String fromDnetToJdate(String str) {
        String zero = "0";
        if (StrUtil.isEmpty(str)) {
            return null;
        } else if (zero.equals(str)) {
            return zero;
        } else {
            Calendar calendar = Calendar.getInstance(TIME_ZONE);
            calendar.setTimeInMillis((Long.parseLong(str)-TICKS_AT_EPOCH)/TICKS_PER_MILLISECOND);
            calendar.setTimeInMillis(calendar.getTimeInMillis()-calendar.getTimeZone().getRawOffset());
            System.out.println(calendar.getTimeInMillis());
            return (new SimpleDateFormat(DatePattern.ISO8601_PATTERN)).format(calendar.getTime());
        }
    }

    public static void main(String[] args) {
        String ntStr = "638013532524749556";
        log.info("NT时间戳：" + fromNTTimeToJdate(ntStr));
        String dntStr = "638013532524749556";
        log.info(".net18位时间戳转换：" + fromDnetToJdate(dntStr));
        dntStr = "638013532525030439";
        log.info(".net18位时间戳转换：" + fromDnetToJdate(dntStr));
        dntStr = "637948045156597133";
        log.info(".net18位时间戳转换：" + fromDnetToJdate(dntStr));
        dntStr = "637948045155934622";
        log.info(".net18位时间戳转换：" + fromDnetToJdate(dntStr));
//        dntStr = "637948079920205879";
//        log.info(".net18位时间戳转换：" + fromDnetToJdate(dntStr));
        dntStr = "637948045161270547";
        log.info(".net18位时间戳转换：" + fromDnetToJdate(dntStr));
    }
}
