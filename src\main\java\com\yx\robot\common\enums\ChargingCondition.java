package com.yx.robot.common.enums;

/**
 * 充电触发条件（上层）
 *
 * <AUTHOR>
 * @date 2020/09/17
 */
public enum ChargingCondition {

    /**
     * 0,"无触发条件"
     */
    NO_CONDITION(0, "无触发条件"),

    /**
     * 1,"手动充电"
     */
    MANUAL(1, "手动充电"),

    /**
     * 2,"手动触发"
     */
    AUTO_CHARGING_MANUAL(2, "手动触发"),

    /**
     * 3,"自动充电"
     */
    AUTO_CHARGING(3, "自动充电"),

    /**
     * 4,"手推充电"
     */
    AUTO_PUSH_CHARGING(4, "手推充电"),

    /**
     * 5,"低电量"
     */
    AUTO_CHARGING_LOWER_BATTERY(5, "低电量");

    private final Integer type;

    private final String value;

    ChargingCondition(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return this.type;
    }

    public String getValue() {
        return value;
    }
}
