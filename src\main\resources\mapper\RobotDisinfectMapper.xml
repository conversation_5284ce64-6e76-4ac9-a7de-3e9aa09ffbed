<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yx.robot.modules.admin.dao.mapper.RobotDisinfectMapper">
    <select id="getPoseInfoBytaskId" resultType="com.yx.robot.modules.admin.vo.PositionsVo">
        select
        trp.name AS positionName,
        trwp.position_x AS positionX,
        trwp.position_y AS positionY,
        trwp.position_z AS positionZ,
        trwp.orientation_w AS orientationW,
        trwp.orientation_x AS orientationX,
        trwp.orientation_y AS orientationY,
        trwp.orientation_z AS orientationZ
        from
        t_robot_task trt
        left join t_robot_disinfect trd on trd.robot_task_id = trt.id
        left join t_robot_location trl on trd.robot_location_id = trl.id
        left join t_robot_position trp on trl.position_id = trp.id
        left join t_robot_world_position trwp on trp.world_pose_id = trwp.id
        where trt.id = #{id}
    </select>

    <select id="getRouteInfoByTaskId" resultType="com.yx.robot.modules.admin.vo.RouteVo">
        select
        trt.name AS taskName,
        trr.id AS routeId,
        trr.location_code AS locationCode,
        trr.location_info AS locationInfo,
        trr.positions AS positions,
        trr.position_number AS positionNumber
        from
        t_robot_task trt
        left join t_robot_disinfect trd on trd.robot_task_id = trt.id
        left join t_robot_route trr on trd.robot_location_id = trr.id
        where trt.id = #{id}
    </select>
</mapper>