package com.yx.robot.modules.admin.service.core;

import com.yx.robot.modules.admin.message._Pose;

/**
 * 初始化相关接口
 *
 * <AUTHOR>
 * @date 2020/09/19
 */
public interface InitService {
    /**
     * 初始化服务地点id
     */
    void initServerId();

    /**
     * 初始化ROS通信
     */
    void initRos();

    /**
     * 初始化地图加载
     */
    void initMap();

    /**
     * 话题服务
     */
    void initTopic();

    /**
     * 计算运行时间
     */
    void sumRunTime();

    /**
     * 初始化任务
     */
    void initTask();

    /**
     * 初始化日志推送
     */
//    void initLogPush();

    /**
     * 重定位
     *
     * @param firstInit 第一次重启的时候
     * @param pose 位置信息
     * @return
     */
    boolean initPose(boolean firstInit, _Pose pose);

    /**
     * 初始化场景语音列表
     */
    void initSceneSpeech();

    /**
     * 初始化设备信息
     */
    void initDeviceInfo();
}
