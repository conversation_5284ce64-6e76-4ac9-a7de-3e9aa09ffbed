package com.yx.robot.modules.admin.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_charging_record")
@ApiModel(value = "机器人充电记录")
public class RobotChargingRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务记录id")
    private String taskRecordId;

    @ApiModelProperty(value = "类型 1:充电开始 2:充电结束")
    private Integer type;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @ApiModelProperty(value = "当前电压")
    private Double voltage;

    @ApiModelProperty(value = "当前电流")
    private Double currentFlow;

    @ApiModelProperty(value = "当前电量")
    private Double percentage;

    @ApiModelProperty(value = "尝试次数")
    private Integer tryTimes;

    @ApiModelProperty(value = "触发条件")
    private Integer conditionType;

    @ApiModelProperty(value = "状态 1 成功 -1:寻找充电桩失败 -2:充电头对接充电桩失败")
    private String status;

    @ApiModelProperty(value = "数据状态 1 已更新 0 未更新")
    private String dataStatus;

    @ApiModelProperty(value = "同步状态 1 已同步 0 未同步")
    private String syncStatus;
}