package com.yx.robot.modules.admin.jike.constant;

public interface JikeConstants {

   String JIKE = "jike";

   String UVR = "uvr";

   String UVR_POINT_ID = "uvr_point_id";

   Integer UVR_UC_TIME = 90; // 单位秒

   String TOKEN = "token";

   String FAC_CODE = "JIKE";

   String ROBOT_NO = "机科消杀2";

   String TASK_DELAY = "task_delay"; //任务延时

//   Integer TASK_DELAY_TIME = 10 * 60;

   String BULID_NO = "A4";

   String ORDER_ID = "orderId";

   String ORDER_STATE = "orderState";

   String API_VERSION = "1.0";

   String UVR_HOST = "uvr_host";

   String UVR_POINT_ID_VALUE = "A4-4F-1K";
}
