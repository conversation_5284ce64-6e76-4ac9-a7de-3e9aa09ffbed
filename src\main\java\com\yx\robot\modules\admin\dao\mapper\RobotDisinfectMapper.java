package com.yx.robot.modules.admin.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yx.robot.modules.admin.entity.RobotDisinfect;
import com.yx.robot.modules.admin.vo.PositionsVo;
import com.yx.robot.modules.admin.vo.RouteVo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 机器人消毒任务条目数据处理层
 * <AUTHOR>
 */
@Repository
public interface RobotDisinfectMapper extends BaseMapper<RobotDisinfect> {

    /**
     * 根据任务ID获取消毒点位ID信息
     * @param id
     * @return
     */
    List<PositionsVo> getPoseInfoBytaskId(@Param("id") String id);

    /**
     * 根据任务ID获取消毒路径信息
     * @param id
     * @return
     */
    RouteVo getRouteInfoByTaskId(@Param("id") String id);
}