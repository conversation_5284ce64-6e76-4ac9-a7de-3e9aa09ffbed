package com.yx.robot.modules.admin.service;

import com.yx.robot.modules.admin.dto.RobotBaseInfoVo;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 14:07
 */
public interface IRobotBaseInfoService {

    /**
     * 从系统环境变量中获取机器人基础信息，
     * 将这些信息保存到map中
     *
     * @return map
     */
    RobotBaseInfoVo getBaseInfoFromRobot();

    /**
     * 保存机器人基本信息到对象中（RobotBaseInfoConstant），并且保存信息到redis中
     * 只有在系统重启时，执行该操作
     *
     * @return
     */
    boolean catchBaseInfoToRedis();

    /**
     * 初始化机器人基础信息
     * 只有在系统重启时，执行该操作
     */
    void initRobotBaseInfo();
}
