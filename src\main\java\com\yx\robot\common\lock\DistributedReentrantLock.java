package com.yx.robot.common.lock;

import java.util.concurrent.TimeUnit;

/**
 * 分布式重入锁
 * <AUTHOR>
 * @date 2016/2/26
 */
public interface DistributedReentrantLock {

    /**
     * 尝试获取锁
     * @param timeout
     * @param unit
     * @return
     * @throws InterruptedException
     */
    boolean tryLock(long timeout, TimeUnit unit) throws InterruptedException;

    /**
     * 释放锁
     */
    void unlock();
}
