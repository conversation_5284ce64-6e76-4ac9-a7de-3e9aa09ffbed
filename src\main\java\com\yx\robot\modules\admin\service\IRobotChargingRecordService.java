package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotChargingRecord;
import com.yx.robot.modules.admin.vo.RobotChargingRecordAppVo;

import javax.servlet.http.HttpServletRequest;

/**
 * 机器人充电记录接口
 * <AUTHOR>
 */
public interface IRobotChargingRecordService extends IService<RobotChargingRecord> {

    /**
     * 保存充电记录
     * @param type 1:开始充电 2:结束充电
     */
    void saveRobotChargingRecord(Integer type);




}