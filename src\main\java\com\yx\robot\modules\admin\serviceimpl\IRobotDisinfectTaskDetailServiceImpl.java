package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.StrUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotDisinfectTaskDetailMapper;
import com.yx.robot.modules.admin.dto.RobotDisinfectTaskDetailDto;
import com.yx.robot.modules.admin.entity.RobotDisinfectTaskDetail;
import com.yx.robot.modules.admin.service.IRobotDisinfectTaskDetailService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;
import java.util.Date;

import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 机器人消毒任务详情接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotDisinfectTaskDetailServiceImpl extends ServiceImpl<RobotDisinfectTaskDetailMapper, RobotDisinfectTaskDetail> implements IRobotDisinfectTaskDetailService {

    @Autowired
    private RobotDisinfectTaskDetailMapper robotDisinfectTaskDetailMapper;

    /**
     * 添加消毒任务详情
     *
     * @param robotDisinfectTaskDetailDto
     */
    @Override
    public void saveRobotDisinfectTaskDetail(RobotDisinfectTaskDetailDto robotDisinfectTaskDetailDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            RobotDisinfectTaskDetail robotDisinfectTaskDetail = new RobotDisinfectTaskDetail();
            robotDisinfectTaskDetail.setDisinfectType(robotDisinfectTaskDetailDto.getDisinfectType());
            String lastTaskRecordId = jedis.hget(ROBOT_SYS_INFO, LAST_TASK_RECORD_ID);
            String ulrayStr = jedis.hget(ROBOT_SYS_INFO, ULRAY);
            boolean wayUlray = StrUtil.isEmpty(ulrayStr) ? false : Boolean.valueOf(ulrayStr);
            String sprayStr = jedis.hget(ROBOT_SYS_INFO, SPRAY);
            boolean waySpray = StrUtil.isEmpty(sprayStr) ? false : Boolean.valueOf(sprayStr);
            String disinfectSpeedStr = jedis.hget(ROBOT_SYS_INFO, DISINFECT_SPEED);
            double disinfectSpeed = StrUtil.isEmpty(disinfectSpeedStr) ? 0.0 : Double.valueOf(disinfectSpeedStr);
            robotDisinfectTaskDetail.setRobotTaskRecordId(lastTaskRecordId);
            robotDisinfectTaskDetail.setWayDisinfectUlray(wayUlray ? SUCCESS : FAIL);
            robotDisinfectTaskDetail.setWayDisinfectSpray(waySpray ? SUCCESS : FAIL);
            robotDisinfectTaskDetail.setWayDisinfectSpeed(disinfectSpeed);
            robotDisinfectTaskDetail.setUlray(robotDisinfectTaskDetailDto.getUlray());
            robotDisinfectTaskDetail.setSpray(robotDisinfectTaskDetailDto.getSpray());
            robotDisinfectTaskDetail.setXt(robotDisinfectTaskDetailDto.getXt());
            robotDisinfectTaskDetail.setDisinfectTime(robotDisinfectTaskDetailDto.getDisinfectTime());
            robotDisinfectTaskDetail.setStartTime(new Date());
            robotDisinfectTaskDetail.setSyncStatus("0");
            robotDisinfectTaskDetailMapper.insert(robotDisinfectTaskDetail);
            jedis.hset(ROBOT_SYS_INFO,LAST_DISINFECT_DETAIL_ID,robotDisinfectTaskDetail.getId());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 更新消毒任务详情
     */
    @Override
    public void updateRobotDisinfectTaskDetail() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String lastDisinfectDetailId = jedis.hget(ROBOT_SYS_INFO, LAST_DISINFECT_DETAIL_ID);
            if(StrUtil.isNotEmpty(lastDisinfectDetailId)) {
                RobotDisinfectTaskDetail robotDisinfectTaskDetail = robotDisinfectTaskDetailMapper.selectById(lastDisinfectDetailId);
                robotDisinfectTaskDetail.setEndTime(new Date());
                robotDisinfectTaskDetailMapper.updateById(robotDisinfectTaskDetail);
                jedis.hdel(ROBOT_SYS_INFO,LAST_DISINFECT_DETAIL_ID);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }
}