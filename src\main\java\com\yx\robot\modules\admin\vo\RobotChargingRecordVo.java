package com.yx.robot.modules.admin.vo;

import com.yx.robot.modules.admin.entity.RobotChargingRecord;
import lombok.Data;
import java.util.Date;

/**
 * 机器人充电记录
 * <AUTHOR>
 * @date 2020/09/17
 */
@Data
public class RobotChargingRecordVo {

    public RobotChargingRecordVo(RobotChargingRecord robotChargingRecord) {
        this.id = robotChargingRecord.getId();
        this.taskRecordId = robotChargingRecord.getTaskRecordId();
        this.type = robotChargingRecord.getType();
        this.startTime = robotChargingRecord.getStartTime();
        this.voltage = robotChargingRecord.getVoltage();
        this.current = robotChargingRecord.getCurrentFlow();
        this.percentage = robotChargingRecord.getPercentage();
        this.tryTimes = robotChargingRecord.getTryTimes();
        this.conditionType = robotChargingRecord.getConditionType();
        this.status = robotChargingRecord.getStatus();
    }

    /**
     * id
     */
    private String id;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 任务记录id
     */
    private String taskRecordId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 当前电压值
     */
    private Double voltage;

    /**
     * 当前电流值
     */
    private Double current;

    /**
     * 当前电量
     */
    private Double percentage;

    /**
     * 尝试次数
     */
    private Integer tryTimes;

    /**
     * 触发条件类型
     */
    private Integer conditionType;

    /**
     * 状态
     */
    private String status;

}
