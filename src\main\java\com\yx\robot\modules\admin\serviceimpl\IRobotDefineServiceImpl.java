package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.DeviceInfoConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dto.DirectionalControlDto;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._DirectionalControl;
import com.yx.robot.modules.admin.service.IRobotDefineService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.time.LocalDate;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

/**
 * 机器人设备实现类
 *
 * <AUTHOR>
 * @date 2021/04/22
 */
@Service
@Slf4j
public class IRobotDefineServiceImpl implements IRobotDefineService {

    private final RosWebService rosWebService;

    private final RosBridgeService rosBridgeService;

    @Autowired
    public IRobotDefineServiceImpl(RosWebService rosWebService, RosBridgeService rosBridgeService) {
        this.rosWebService = rosWebService;
        this.rosBridgeService = rosBridgeService;
    }

    @Override
    public void initRobotInfo() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            if (!Objects.requireNonNull(jedis).exists(DeviceInfoConstants.DEVICE_INFO)) {
                // 设备默认保养时长（3年）
                long deviceMaintenanceTime = 3;
                jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.DEVICE_MAINTENANCE_TIME, deviceMaintenanceTime + "");
                LocalDate today = LocalDate.now();
                LocalDate deviceMaintenanceDate = today.plus(deviceMaintenanceTime, ChronoUnit.YEARS);
                jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.DEVICE_MAINTENANCE_DATE, deviceMaintenanceDate.toString());
                // 脉冲灯管的保养时长(3年)
                long ulrayMaintenanceTime = 3;
                jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_MAINTENANCE_TIME, ulrayMaintenanceTime + "");
                // 计算脉冲灯的保养日期
                LocalDate ulrayMaintenanceDate = today.plus(ulrayMaintenanceTime, ChronoUnit.YEARS);
                jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_MAINTENANCE_DATE, ulrayMaintenanceDate.toString());
                // 设置脉冲的使用时间
                jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME, "0");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 修改保养信息
     *
     * @param key   key
     * @param value value
     */
    @Override
    public boolean updateDeviceInfo(String key, String value) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            if (StrUtil.isBlank(value)) {
                return false;
            }
            assert jedis != null;
            jedis.hset(DeviceInfoConstants.DEVICE_INFO, key, value);
            if (key.equals(DeviceInfoConstants.VOLUME_SIZE)) {
                rosWebService.volumeCtrl(Integer.valueOf(value));
            }
            if (key.equals(DeviceInfoConstants.DEVICE_MAINTENANCE_TIME)) {
                LocalDate today = LocalDate.now();
                LocalDate deviceMaintenanceDate = today.plus(Integer.parseInt(value), ChronoUnit.YEARS);
                jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.DEVICE_MAINTENANCE_DATE,
                        deviceMaintenanceDate.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return true;
    }

    /**
     * 获取机器人设备信息
     *
     * @return 设备信息
     */
    @Override
    public Map<String, Object> getDeviceInfo() {
        Map<String, Object> deviceInfo = new HashMap<>(16);
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            assert jedis != null;
            Set<String> hkeys = jedis.hkeys(DeviceInfoConstants.DEVICE_INFO);
            if (CollectionUtil.isNotEmpty(hkeys)) {
                for (String key : hkeys) {
                    String hget = jedis.hget(DeviceInfoConstants.DEVICE_INFO, key);
                    if (StrUtil.isNotBlank(hget)) {
                        deviceInfo.put(key, hget);
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return deviceInfo;
    }

    /**
     * 方向控制
     *
     * @param directionalControlDto 方向控制参数
     * @return true:成功，false:失败
     */
    @Override
    public boolean directionControl(DirectionalControlDto directionalControlDto) {
        RedisUtil.delTopicValue(TopicConstants.DIRECTIONAL_CONTROL);
        _DirectionalControl directionalControl = new _DirectionalControl();
        directionalControl.setValue(directionalControlDto.getX(), directionalControlDto.getZ());
        rosBridgeService.publish(TopicConstants.DIRECTIONAL_CONTROL, Message.getMessageType(_DirectionalControl.class), JSON.toJSONString(directionalControl));
        ThreadUtil.sleep(120);
        DirectionalControlDto result = getDirectionalControl();
        return result.getX() == directionalControlDto.getX() && result.getZ() == directionalControlDto.getZ();
    }

    /**
     * 获取当前机器人运动状态
     *
     * @return
     */
    @Override
    public DirectionalControlDto getDirectionalControl() {
        String direcCtl = RedisUtil.getTopicValue(TopicConstants.DIRECTIONAL_CONTROL);
        if (StringUtils.isNotBlank(direcCtl)) {
            _DirectionalControl directionalControl = JSONObject.parseObject(direcCtl, _DirectionalControl.class);
            DirectionalControlDto directionalControlDto = new DirectionalControlDto();
            directionalControlDto.setX(directionalControl.getLinear().x);
            directionalControlDto.setZ(directionalControl.getAngular().z);
            return directionalControlDto;
        }
        return null;
    }

}
