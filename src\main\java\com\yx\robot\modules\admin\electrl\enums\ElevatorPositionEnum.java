package com.yx.robot.modules.admin.electrl.enums;

/**
 * 电梯位置指令
 * <AUTHOR>
 * @date 2021/12/18
 */
public enum ElevatorPositionEnum {

    /**
     * "in","内部"
     */
    IN("in","内部"),

    /**
     * "out","外部"
     */
    OUT("out","外部");

    private final String value;

    private final String label;

    ElevatorPositionEnum(String value, String label) {
        this.value = value;
        this.label = label;
    }

    public String getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
