package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import com.yx.robot.common.constant.DeviceInfoConstants;
import com.yx.robot.modules.admin.dao.mapper.RobotDeviceInfoMapper;
import com.yx.robot.modules.admin.entity.RobotDeviceInfo;
import com.yx.robot.modules.admin.service.IRobotDeviceOperateService;
import com.yx.robot.modules.admin.service.core.RosCallService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/29 13:29
 */
@Slf4j
@Service
@AllArgsConstructor
public class IRobotDeviceOperateServiceImpl implements IRobotDeviceOperateService {

    private RobotDeviceInfoMapper robotDeviceInfoMapper;

    private RosCallService rosCallService;

    /**
     * 操作设备接口 通过类型
     *
     * @param robotDeviceInfo 设备信息
     * @param cmdType         命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    @Override
    public boolean operateDeviceByType(RobotDeviceInfo robotDeviceInfo, Short cmdType) {
        if (robotDeviceInfo.getType() == DeviceInfoConstants.DEVICE_TYPE_OTHER
                && robotDeviceInfo.getSubType() == DeviceInfoConstants.DEVICE_OTHER_ENTRANCE_GUARD) {
            return operateEntranceGuard(robotDeviceInfo, cmdType);
        }
        /*else {
            throw new IllegalStateException("设备类型不存在");
        }*/
        return true;
    }

    /**
     * 操作设备接口 通过控制方式
     *
     * @param robotDeviceInfo 设备信息
     * @param cmdType         命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    @Override
    public boolean operateDeviceByControlMethod(RobotDeviceInfo robotDeviceInfo, Short cmdType) {
        log.info("未实现");
        // TODO 后面会优化
        return false;
    }

    /**
     * 操作设备接口
     *
     * @param id          设备信息ID
     * @param operateType 0:根据类型操作设备,1:根据通讯方式（control_method）操作设备
     * @param cmdType     命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    @Override
    public boolean operateDevice(String id, Integer operateType, Short cmdType) {

        RobotDeviceInfo robotDeviceInfo = robotDeviceInfoMapper.selectById(id);
        Asserts.check(ObjectUtil.isNotNull(robotDeviceInfo), "设备不存在:" + id);

        return operateDevice(robotDeviceInfo, operateType, cmdType);
    }

    /**
     * 操作设备接口
     *
     * @param robotDeviceInfo 设备信息
     * @param operateType     0:根据类型操作设备,1:根据通讯方式（control_method）操作设备
     * @param cmdType         命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    @Override
    public boolean operateDevice(RobotDeviceInfo robotDeviceInfo, Integer operateType, Short cmdType) {
        if (DeviceInfoConstants.DEVICE_OPERATE_SELECT_CONTROL_METHOD == operateType) {
            return operateDeviceByControlMethod(robotDeviceInfo, cmdType);
        } else {
            return operateDeviceByType(robotDeviceInfo, cmdType);
        }
    }

    /**
     * 门禁操作
     *
     * @param robotDeviceInfo 设备信息
     * @param cmdType         命令类型
     * @return true:成功，false:失败
     */
    @Override
    public boolean operateEntranceGuard(RobotDeviceInfo robotDeviceInfo, Short cmdType) {

        Asserts.check(StringUtils.isNumeric(robotDeviceInfo.getQueryParams()), "门禁参数格式错误,请输入1000-9999之间的数字");

        Asserts.check(Integer.parseInt(robotDeviceInfo.getQueryParams()) < 10000
                && Integer.parseInt(robotDeviceInfo.getQueryParams()) > 999, "请输入1000-9999之间的数字");

        String params = robotDeviceInfo.getQueryParams();
        char[] paramsArray = params.toCharArray();
        Short[] paramsShortArray = new Short[paramsArray.length];
        for (int i = 0; i < paramsArray.length; i++) {
            paramsShortArray[i] = new Short(paramsArray[i] + "");
        }
        return rosCallService.callEntranceGuard(cmdType, paramsShortArray);
    }
}
