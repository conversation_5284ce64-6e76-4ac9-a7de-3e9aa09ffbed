package com.yx.robot.modules.admin.vo;

import com.yx.robot.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * 任务视图
 */
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel("任务信息")
public class RobotTaskVo extends BaseEntity {

    /**
     * 地图Id
     */
    private String mapId;
    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务类型(立即执行任务，定时执行任务，周期任务，紧急任务)
     */
    private Integer type;

    /**
     * 任务子类型
     */
    private Integer subType;

    /**
     * 任务等级
     */
    private Integer level;

    /**
     * 任务循环次数
     */
    private Integer loops;

    /**
     * 排序值
     */
    private BigDecimal sortOrder;

    @ApiModelProperty(value = "任务可执行，开始时间")
    private String executableStartTime;

    @ApiModelProperty(value = "任务可执行，结束时间")
    private String executableEndTime;

}
