package com.yx.robot.common.utils;

import cn.hutool.core.util.ObjectUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.config.YamlPropertiesFactoryBean;
import org.springframework.core.env.Environment;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.io.Resource;

import java.util.Properties;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/27 18:17
 */
@Slf4j
public class YmlUtils {

//    private static Properties properties;

    private static Environment environment;

    public static void setEnvironment(Environment environment) {
        YmlUtils.environment = environment;
    }

    public static Object getValue(String key) {
//        if (CollectionUtil.isEmpty(properties)) {
//            properties = getProperties("application.yml");
//            Assert.notNull(properties, "配置文件不存在");
//            Object typeYml = properties.get("spring.profiles.active");
//
//            if (ObjectUtil.isNotNull(typeYml)) {
//                properties = getProperties("application-" + typeYml + ".yml");
//            }
//        }
//        if (ObjectUtil.isNull(properties)) {
//            log.error("配置文件不存在");
//            return null;
//        }
//        return properties.get(key);
        if (ObjectUtil.isNull(environment)) {
            log.info("environment:null");
            return null;
        }
        return environment.getProperty(key);
    }

    public static Properties getProperties(String filePath) {
        Resource resource = new ClassPathResource(filePath);
        Properties properties;
        try {
            YamlPropertiesFactoryBean yamlFactory = new YamlPropertiesFactoryBean();
            yamlFactory.setResources(resource);
            properties = yamlFactory.getObject();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return properties;
    }

    public static void main(String[] args) {
        System.out.println(getValue("logging.config"));
    }
}
