package com.yx.robot.modules.admin.service.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yx.robot.common.constant.*;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.hk.HkUtils;
import com.yx.robot.common.utils.*;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.DisinfectManualOperationDto;
import com.yx.robot.modules.admin.dto.RobotBaseInfoVo;
import com.yx.robot.modules.admin.dto.RobotDisinfectTaskDetailDto;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.jike.constant.JikeConstants;
import com.yx.robot.modules.admin.jike.enums.OrderStateEnum;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.scheduler.RabbitMQScheduler;
import com.yx.robot.modules.admin.scheduler.RobotStatusScheduler;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.serviceimpl.IRobotChargingRecordServiceImpl;
import com.yx.robot.modules.admin.vo.*;
import com.yx.robot.modules.base.utils.LidarControlUtil;
import com.yx.robot.modules.base.utils.SendMessageUtil;
import io.micrometer.core.instrument.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.util.Asserts;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.io.File;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.yx.robot.common.constant.ControlStatusConstants.*;
import static com.yx.robot.common.constant.RobotRedisConstants.ENTRANCE_CURRENT_OPEN_DEV_ID;
import static com.yx.robot.common.constant.RobotRedisConstants.ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.ServiceConstants.SPEED_LEVEL;
import static com.yx.robot.common.constant.ServiceConstants.*;
import static com.yx.robot.common.constant.TopicConstants.*;
import static com.yx.robot.modules.admin.scheduler.RobotHealthScheduler.shoutdownCtrl;
import static com.yx.robot.modules.admin.scheduler.RobotStatusScheduler.*;
import static com.yx.robot.modules.admin.serviceimpl.IRobotChargingRecordServiceImpl.cycleOutChargingMessage;
import static com.yx.robot.modules.base.utils.LidarControlUtil.END_CHARING;
import static com.yx.robot.modules.base.utils.LidarControlUtil.SLEEP;

@Service("RosWebService")
@Slf4j
@Transactional
public class RosWebService {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private IRobotWorldPositionService iRobotWorldPositionService;

    @Autowired
    private IRobotMapService iRobotMapService;

    @Autowired
    private IRobotRouteService iRobotRouteService;

    @Autowired
    private IRobotTaskItemService iRobotTaskItemService;

    @Autowired
    private IRobotLocationService iRobotLocationService;

    @Autowired
    private IRobotTaskRecordService iRobotTaskRecordService;

    @Autowired
    private IRobotDisinfectTaskDetailService iRobotDisinfectTaskDetailService;

    @Autowired
    private UploadUtils uploadUtils;

    @Autowired
    private InitService initService;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotAreasService iRobotAreasService;

    @Autowired
    private IRobotChargingRecordService iRobotChargingRecordService;

    @Autowired
    private IRobotCheckHistoryRecordService iRobotCheckHistoryRecordService;

    @Autowired
    private IRobotCheckHistoryItemService iRobotCheckHistoryItemService;

    @Autowired
    private IRobotDisinfectantRecordService iRobotDisinfectantRecordService;

    @Autowired
    private IRobotEntranceGuardService iRobotEntranceGuardService;

    @Autowired
    private IRobotSwitchRecordService iRobotSwitchRecordService;

    @Autowired
    private IRobotRouteItemService iRobotRouteItemService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Autowired
    private RabbitMQScheduler rabbitMQScheduler;

    @Autowired
    private SendMessageUtil sendMessageUtil;

    @Autowired
    private LidarControlUtil lidarControlUtil;

    @Autowired
    private IRobotTaskService robotTaskService;

    private static final ExecutorService executorService = Executors.newCachedThreadPool();

    private static Integer musicOperation = -1;

    @Value("${robot.file.map-path}")
    private String mapPath;

    @Value("${robot.file.map-back-suffix}")
    private String mapBackSuffix;

    private static boolean mapping = false;

    private static boolean initPose = false;

    @Value("${yx-yun.host}")
    private String yxYunHost;

    @Value("${yx-yun.port}")
    private Integer yxYunPort;

    @Value("${yx-yun.context-path}")
    private String yxYunContextPath;

    @Value("${robot.file.temp-map-path}")
    private String tempMapPath;

    @Value("${robot.file.robot-map}")
    private String robotMap;

    @Value("${yx-yun.url.initData}")
    private String yxYunInitData;

    /**
     * 机器人基础信息文件路径
     */
    @Value("${robot.baseInfo}")
    private String robotBaseInfoPath;

    /**
     * 消毒效率（每小时0.8L的消毒量）
     */
    private static final Double disinfectEfficiency = 0.8;

    /**
     * 消毒规格（每立方5ml）
     */
    private static final Double disinfectStandard = 5.0;

    @Autowired
    private IRobotChargingService iRobotChargingService;

    /**
     * 根据导航点生成队列信息
     */
    public String navToQueueInfo(RobotTaskItem robotTaskItem) {
        String jstr = "";
        String taskId = robotTaskItem.getTaskId();
        _NavControlReq navControlReq = new _NavControlReq();
        navControlReq.frame_id = UUID.randomUUID().toString();
        //0 自主规划 1 固定路线
        navControlReq.nav_mode = robotTaskItem.getNavMode();
        //1 开始导航 4 取消导航
        navControlReq.cmd = Integer.valueOf(robotTaskItem.getCmd());
        RobotPosition robotPosition = iRobotPositionService.getById(robotTaskItem.getPositionId());
        RobotWorldPosition robotWorldPosition = null;
        if (null != robotPosition) {
            navControlReq.pose_name = robotPosition.getName();
            navControlReq.pose_type = robotPosition.getType();
            robotWorldPosition = iRobotWorldPositionService.getById(robotPosition.getWorldPoseId());
        }
        if (null != robotWorldPosition) {
            _Pose pose = new _Pose();
            pose.orientation = new _Quaternion();
            pose.orientation.w = robotWorldPosition.getOrientationW();
            pose.orientation.x = robotWorldPosition.getOrientationX();
            pose.orientation.y = robotWorldPosition.getOrientationY();
            pose.orientation.z = robotWorldPosition.getOrientationZ();
            pose.position = new _Point();
            pose.position.x = robotWorldPosition.getPositionX();
            pose.position.y = robotWorldPosition.getPositionY();
            pose.position.z = robotWorldPosition.getPositionZ();
            navControlReq.pose_info = pose;
            jstr = JSON.toJSONString(navControlReq);
            jstr = taskId + "#" + robotTaskItem.getPositionId() + "#" + jstr;
        }
        return jstr;
    }


    /**
     * 创建停止导航指令
     */
    public _NavControlReq getStopNav() {
        _NavControlReq navControlReq = new _NavControlReq();
        navControlReq.frame_id = UUID.randomUUID().toString();
        navControlReq.cmd = 4;
        navControlReq.nav_mode = 0;
        navControlReq.pose_name = "";
        navControlReq.pose_type = 0;
        _Pose pose = new _Pose();
        pose.orientation = new _Quaternion();
        pose.orientation.w = 0.0;
        pose.orientation.x = 0.0;
        pose.orientation.y = 0.0;
        pose.orientation.z = 0.0;
        pose.position = new _Point();
        pose.position.x = 0.0;
        pose.position.y = 0.0;
        pose.position.z = 0.0;
        navControlReq.pose_info = pose;
        return navControlReq;
    }

    /**
     * 获取导航状态码
     */
    public Integer getStatusCode() {
        Integer statusCode = null;
        String topicResult = RedisUtil.getTopicValue(NAV_STATUS);
        if (!topicResult.isEmpty()) {
            _RobotNavStatus robotNavStatus = JSON.parseObject(topicResult, _RobotNavStatus.class);
            statusCode = robotNavStatus.statusCode;
        }
        return statusCode;
    }

    /**
     * 新版任务控制
     *
     * @param taskId    任务名称
     * @param operation START(1,"开始任务"),STOP(2, "停止任务"),CONTINUE(3,"继续任务"),CANCEL(4, "取消任务");
     * @return true 成功/false 失败
     */
    public boolean taskControl1(String taskId, String operation) {
        try {
            if (StrUtil.isNotEmpty(taskId)) {
                RobotTask task = iRobotTaskService.getById(taskId);
                if (task != null) {
                    if (iRobotTaskService.isExecutableTime(task)) {
                        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_TASK_ID, taskId);
                    } else if (TaskOperationType.START.toString().equals(operation)
                            || TaskOperationType.CONTINUE.toString().equals(operation)) {
                        log.warn("任务不在可执行的时间段内");
                        return false;
                    }
                }
            }
            if (TaskOperationType.START.getOperation().toString().equals(operation)) {
                startFallBeforeTask();
                // 开始任务前，检测机器人是否正在充电中，正在充电中则取消充电
                Object autoChargingStatus = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
                // 是否正在执行充电
                boolean res = autoChargingStatus != null && autoChargingStatus.toString().equals(AutoChargingStatus.DOING.getValue().toString());
                _UdriveAutoDockState udriveAutoDockState = iRobotChargingService.getAutoDockState();
                log.info("udriveAutoDockState:{}", JSON.toJSONString(udriveAutoDockState));
                if (res || (ObjectUtil.isNotNull(udriveAutoDockState) && udriveAutoDockState.working)) {
                    iRobotChargingService.cancelDock();
                }
                // 给电机使能：解决机器人前往任务路线起始点的时候，按下取消任务，就会导致机器人不动BUG
                iRobotMotorService.motorControl(true);
                clearTaskInfo1();
                redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "true");
                //查询任务导航点
                QueryWrapper<RobotTaskItem> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("task_id", taskId);
                queryWrapper.orderByAsc("sort_order");
                List<RobotTaskItem> robotTaskItemList = iRobotTaskItemService.list(queryWrapper);
                //任务队列
                List<String> navControlReqList = new ArrayList<>();
                if (null != robotTaskItemList && !robotTaskItemList.isEmpty()) {
                    for (RobotTaskItem robotTaskItem : robotTaskItemList) {
                        String jstr = navToQueueInfo(robotTaskItem);
                        navControlReqList.add(jstr);
                    }
                }
                if (CollectionUtil.isNotEmpty(navControlReqList)) {
                    redisTemplate.opsForList().leftPushAll(TO_DO_LIST, navControlReqList);
                }
                //发布工作中的点阵表情话题---工作中---后续加的，还没测试
                publishUtil(ExpressionType.WORKING.getValue());
                ControlStatusConstants.EXPRESSION_PRIORITY.working = true;
            } else if (TaskOperationType.STOP.getOperation().toString().equals(operation)) {
                IS_ROBOT_LINE_EXEC_STATUS_CANCEL = 1;
                // 充电状态
                Object autoChargingStatus = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
                // 是否正在执行充电
                boolean res = autoChargingStatus != null && autoChargingStatus.toString().equals(AutoChargingStatus.DOING.getValue().toString());
                _UdriveAutoDockState udriveAutoDockState = iRobotChargingService.getAutoDockState();
                log.info("udriveAutoDockState:{}", JSON.toJSONString(udriveAutoDockState));
                if (res || (ObjectUtil.isNotNull(udriveAutoDockState) && udriveAutoDockState.working)) {
                    iRobotChargingService.cancelDock();
                } else {
                    stopTaskService();
//                    if (ObjectUtil.isNotNull(iRobotChargingService.getAutoDockState())) {
//                        iRobotChargingService.cancelDock();
//                    }
                }
                Thread.sleep(1000);
                log.info("任务暂停");
                iRobotMotorService.motorControl(false);
            } else if (TaskOperationType.CONTINUE.getOperation().toString().equals(operation)) {
                if (ControlStatusConstants.ROBOT_LINE_EXEC_STATUS == 1) {
                    log.info("执行到起始点时，暂停，之后继续任务");
                    ControlStatusConstants.ROBOT_LINE_EXEC_STATUS = 0;
                }
                log.info("恢复任务");
                //定点任务完成后，执行回充电桩充电操作
                boolean isTaskFish = false;
                redisTemplate.opsForHash().put(TASK_INFO, IS_STOPPING, "false");
                redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "true");
                IS_ROBOT_LINE_EXEC_STATUS_CANCEL = 1;
                Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                // 巡线任务或者自定义巡线任务
                if (null != o && StrUtil.isNotBlank(o.toString())) {
                    RobotTask robotTask = iRobotTaskService.getById(o.toString());
                    if (null != robotTask && robotTask.getType().equals(TaskType.DISINFECT.getType())
                            && (robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType()) ||
                            robotTask.getSubType().equals(DisinfectTaskType.DEFINE_LINE_TASK.getType()))) {
                        iRobotDisinfectService.routeDisinfectOperation(null, TaskOperationType.CONTINUE.getOperation());
                        iRobotMotorService.motorControl(true);
                    }

                    if (ObjectUtil.isNull(robotTask)) {
                        isTaskFish = true;
                    } else if (robotTask.getType().equals(TaskType.DISINFECT.getType())
                            // 常规任务
                            && robotTask.getSubType().equals(DisinfectTaskType.ROUTINE_TASK.getType())
                            && RedisUtil.getSize(TO_DO_LIST) == 0) {
                        isTaskFish = true;
                    } else if (TaskType.CHARGING.getType().equals(robotTask.getType())) {
                        isTaskFish = true;
                    }
                }

                if (isTaskFish) {
                    RedisUtil.hdel(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                    Result<Boolean> booleanResult = gotoCharging();
                    log.info("继续任务，执行返回充电操作。。。。");
                    if (!booleanResult.isSuccess()) {
                        gotoOrigin();
                    }
                }
            } else if (TaskOperationType.CANCEL.getOperation().toString().equals(operation)) {
                IS_ROBOT_LINE_EXEC_STATUS_CANCEL = 0;
                clearTaskInfo1();
                //清空手动消毒模式信息
                cleanManualDisinfectInfo();
                // 如果是定点消毒任务
                Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                // 更新消毒任务记录
                iRobotTaskRecordService.updateRobotTaskRecord(NavType.MANUAL_CANCEL_TYPE.getType());
                redisTemplate.opsForValue().set(TOPIC + "::" + TopicConstants.NAV_STATUS, "");
                redisTemplate.opsForValue().set(TOPIC + "::" + TopicConstants.DOCTOR_MODE_STATE, "");
                redisTemplate.opsForHash().delete(TASK_INFO, LOOPS + ":" + taskId);
                if (null != o && StrUtil.isNotBlank(o.toString())) {
                    RobotTask robotTask = iRobotTaskService.getById(o.toString());
                    if (null != robotTask && robotTask.getType().equals(TaskType.DISINFECT.getType())
                            && robotTask.getSubType().equals(DisinfectTaskType.FIXED_POINT_TASK.getType())) {
                        // 停止手动消毒
                        DisinfectManualOperationDto manualOperationDto = new DisinfectManualOperationDto();
                        manualOperationDto.setOperation(0);
                        manualDisinfect(manualOperationDto);
                    }
                    if (null != robotTask && robotTask.getType().equals(TaskType.DISINFECT.getType())
                            && (robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType())
                            || robotTask.getSubType().equals(DisinfectTaskType.DEFINE_LINE_TASK.getType()))) {
                        iRobotDisinfectService.routeDisinfectOperation(null, TaskOperationType.CANCEL.getOperation());
                        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                    }
                    // 门禁任务
                    if (null != robotTask && robotTask.getType().equals(TaskType.ENTRANCE_GUARD.getType())) {
                        iRobotEntranceGuardService.handleArriveEntranceGuardPosition(new Short(DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_CLOSE + ""), null, null);
                        RedisUtil.delHash(ROBOT_SYS_INFO, ENTRANCE_CURRENT_OPEN_DEV_ID);
                    }
                }
                stopTaskService();
                log.info("取消当前任务，并删除当前任务ID:{}", ObjectUtil.toString(o));
                redisTemplate.opsForHash().delete(ROBOT_SYS_INFO, CURRENT_TASK_ID);
//              如果任务取消先发送一个空闲；
                ControlStatusConstants.EXPRESSION_PRIORITY.gotoCharing = false;
                publishUtil(ExpressionType.SMILE_ONE.getValue());
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /**
     * 添加目标点到redis
     *
     * @param layPositionId 层数点位ID，现在代表一个key
     * @param taskId        任务ID
     * @return true/false
     */
    public boolean addPointToRedis(Map<String, Object> layPositionId, String taskId) {
        try {
            // 任务列表
            List<String> result = new ArrayList<>();
            if (StrUtil.isNotEmpty(taskId)) {
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_TASK_ID, taskId);
            }
            if (null != layPositionId && !layPositionId.isEmpty()) {
                for (Map.Entry<String, Object> entry : layPositionId.entrySet()) {
                    //任务队列
                    String positionId = entry.getValue().toString();
                    String layer = entry.getKey();
                    _NavControlReq navControlReq = new _NavControlReq();
                    navControlReq.frame_id = UUID.randomUUID().toString();
                    //0 自主规划 1 固定路线
                    navControlReq.nav_mode = 0;
                    //1 开始导航 4 取消导航
                    navControlReq.cmd = 1;
                    RobotPosition robotPosition = iRobotPositionService.getById(positionId);
                    if (null != robotPosition) {
                        navControlReq.pose_name = robotPosition.getName();
                        if (null != robotPosition.getType()) {
                            navControlReq.pose_type = robotPosition.getType();
                        }
                        _Pose pose = new _Pose();
                        RobotWorldPosition robotWorldPosition = iRobotWorldPositionService.getById(robotPosition.getWorldPoseId());
                        if (null != robotWorldPosition) {
                            pose.orientation = new _Quaternion();
                            pose.orientation.w = robotWorldPosition.getOrientationW();
                            pose.orientation.x = robotWorldPosition.getOrientationX();
                            pose.orientation.y = robotWorldPosition.getOrientationY();
                            pose.orientation.z = robotWorldPosition.getOrientationZ();
                            pose.position = new _Point();
                            pose.position.x = robotWorldPosition.getPositionX();
                            pose.position.y = robotWorldPosition.getPositionY();
                            pose.position.z = robotWorldPosition.getPositionZ();
                        }
                        navControlReq.pose_info = pose;
                    }
                    // 导航请求参数
                    String jstr = JSON.toJSONString(navControlReq);
                    result.add(taskId + "#" + positionId + "#" + layer + "#" + jstr);
                }
                RobotTask byId = iRobotTaskService.getById(taskId);
                if (byId != null && !byId.getType().equals(TaskType.ENTRANCE_GUARD.getType())) {
                    clearTaskInfo1();
                    redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "true");
                    if (!result.isEmpty()) {
                        redisTemplate.opsForList().leftPushAll(TO_DO_LIST, result);
                    }
                } else {
                    redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "true");
                    if (!result.isEmpty()) {
                        redisTemplate.opsForList().rightPushAll(TO_DO_LIST, result);
                    }
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
        return true;
    }

    /**
     * 开始消毒（点位消毒模式）
     *
     * @param ids     iRobotDisinfectId
     * @param orderId 订单ID
     * @return result
     */
    public Result<Boolean> startDisinfectServiceByPoint(String ids, String orderId) {
        startFallBeforeTask();
        String id = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        if (StringUtils.isNotBlank(id)) {
            taskControl1(id, TaskOperationType.STOP.getOperation().toString());
            taskControl1(id, TaskOperationType.CANCEL.getOperation().toString());
        }
        RedisUtil.hset(TASK_INFO, DISINFECT_POINT_LOCAL_TASK, ids);
        // 开始消毒则赋予订单id
        if (StrUtil.isNotBlank(orderId)) {
            redisTemplate.opsForHash().put(JikeConstants.JIKE, JikeConstants.ORDER_ID, orderId);
        }
        String[] idArr = ids.split(",");
        boolean manualCharge = isManualCharge();
        if (manualCharge) {
            return ResultUtil.error("正在进行手动充电，无法进行消毒工作");
        }
        Float batteryPercentage = iRobotStatusService.getBatteryPercentage();
        if (batteryPercentage != null && batteryPercentage <= 20) {
            return ResultUtil.error("当前电量不足，无法进行消毒工作");
        }
        // 判断是否已经执行了充电逻辑
        String autoChargingStatusObj = RedisUtil.getHash(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
        if (StringUtils.isNotBlank(autoChargingStatusObj)) {
            Integer autChargingStatus = Integer.valueOf(autoChargingStatusObj);
            if (autChargingStatus.equals(AutoChargingStatus.DOING.getValue())) {
                return new ResultUtil<Boolean>().setErrorMsg("正在对接充电中,请先结束充电");
            }
        }
        // 如果急停被按下，则不执行任务
        if (iRobotMotorService.getEmergencyState()) {
            return ResultUtil.error("急停被按下，无法进行消毒工作");
        }
        log.info("准备开始执行消毒任务(点位模式)......");
        String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
        List<RobotTask> robotTaskList = iRobotTaskService.list(new LambdaQueryWrapper<RobotTask>()
                .eq(RobotTask::getType, TaskType.DISINFECT.getType())
                .eq(RobotTask::getSubType, DisinfectTaskType.LOCAL_TASK.getType())
                .eq(RobotTask::getMapId, currentMap));
        RobotTask robotTask = null;
        if (CollectionUtil.isNotEmpty(robotTaskList)) {
            robotTask = robotTaskList.get(0);
        }
        if (robotTask != null) {
            String taskId = robotTask.getId();
            Map<String, Object> layPositionIdsTrans = new LinkedHashMap<>();
            List<String> positionIdList = new ArrayList<>();
            for (String s : idArr) {
                RobotDisinfect robotDisinfect = iRobotDisinfectService.getById(s);
                String robotLocationId = robotDisinfect.getRobotLocationId();
                RobotLocation robotLocation = iRobotLocationService.getById(robotLocationId);
                positionIdList.add(robotLocation.getPositionId());
            }
            List<String> sortedList = iRobotPositionService.shortestPaths(positionIdList);
            for (int i = 0; i < sortedList.size(); i++) {
                layPositionIdsTrans.put(i + 1 + "", sortedList.get(i));
            }
            addPointToRedis(layPositionIdsTrans, taskId);
        }
        //清空地图
        clearMap();
        //语音提醒
        sendVoicePrompt(SceneType.START, null);
        publishUtil(ExpressionType.WORKING.getValue());
        //清空手动消毒信息
        cleanManualDisinfectInfo();
        redisTemplate.opsForHash().put(JikeConstants.JIKE, JikeConstants.ORDER_STATE, OrderStateEnum.DOING.getValue().toString());
        // 工作状态预处理结束
        return ResultUtil.data(true);
    }

    /**
     * 开始消毒(任务模式)
     *
     * @param id Disinfect
     * @return result
     */
    public Result<Boolean> startDisinfectService(String id) {
        startFallBeforeTask();
        boolean manualCharge = isManualCharge();
        if (manualCharge) {
            // 发送语音：正在进行手动充电，无法进行消毒工作
            sendVoicePrompt(SceneType.DONT_WORK_OF_DUCK, null);
            return ResultUtil.error("正在进行手动充电，无法进行消毒工作");
        }
        if (!iRobotStatusService.getPositionStatus()) {
            // 发送语音：定位丢失，无法进行消毒工作
            sendVoicePrompt(SceneType.DONT_WORK_OF_LOSE_LOCATION, null);
            return ResultUtil.error("定位丢失，无法进行消毒工作");
        }
        if (StrUtil.isNotEmpty(id)) {
            RobotTask task = iRobotTaskService.getById(id);
            if (!iRobotTaskService.isExecutableTime(task)) {
                // 发送语音：当前任务不在可执行的时间段内
                sendVoicePrompt(SceneType.DONT_WORK_OF_OUT_OF_TIME, null);
                return ResultUtil.error("任务不在可执行的时间段内");
            }
        }
        Float batteryPercentage = iRobotStatusService.getBatteryPercentage();
        if (ObjectUtil.isNotNull(batteryPercentage) && batteryPercentage <= 20) {
            // 发送语音：当前电量不足无法开始工作哦。
            sendVoicePrompt(SceneType.DONT_WORK_OF_LOW_BATTERY, null);
            return ResultUtil.error("当前电量不足，无法进行消毒工作");
        }
        // 判断是否已经执行了充电逻辑
        Object autoChargingStatusObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
        Integer autChargingStatus = Integer.valueOf(autoChargingStatusObj.toString());
        if (autChargingStatus.equals(AutoChargingStatus.DOING.getValue())) {
            log.info("正在对接充电中,请先结束充电");
            // 发送语音：正在对接充电中,请先结束充电
            sendVoicePrompt(SceneType.DONT_WORK_OF_DOCK, null);
            return new ResultUtil<Boolean>().setErrorMsg("正在对接充电中,请先结束充电");
        }
        // 如果急停被按下，则不执行任务
        if (iRobotMotorService.getEmergencyState()) {
            return ResultUtil.error("急停被按下，无法进行消毒工作");
        }
        log.info("准备开始执行消毒任务......");
        RobotTask robotTask = iRobotTaskService.getById(id);
        List<RobotDisinfect> robotDisinfects = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, id).orderByAsc(RobotDisinfect::getUpdateTime));
        // 检测是否需要喷雾消毒，是则检测液位状态，缺液则告警
        if (iRobotDisinfectService.isSprayDisinfect(robotDisinfects) && iRobotStatusService.isSprayLiquidLevelWarning()) {
            // 缺液语音提示
            sendVoicePrompt(SceneType.SPRAY_WATER_WARNING, null);
            return ResultUtil.error("当前水位不足，无法进行消毒工作");
        }
        // 如果为消毒任务下面的子任务巡线任务或者自定义巡线任务
        if (robotTask.getType().equals(TaskType.DISINFECT.getType())
                && (robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType())
                || robotTask.getSubType().equals(DisinfectTaskType.DEFINE_LINE_TASK.getType()))) {
            if (CollectionUtil.isNotEmpty(robotDisinfects)) {
                _UdriveAutoDockState udriveAutoDockState = iRobotChargingService.getAutoDockState();
                if ((ObjectUtil.isNotNull(udriveAutoDockState) && udriveAutoDockState.working)) {
                    RedisUtil.delHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                    iRobotChargingService.cancelDock();
                }
                clearTaskInfo1();
                redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "true");
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, LINE_DISINFECT, "true");
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_TASK_ID, robotTask.getId());
                for (int i = 0; i < robotDisinfects.size(); i++) {
                    String robotRouteId = robotDisinfects.get(i).getRobotLocationId(); // 巡线消毒记录的是路径的id
                    StringBuffer sb = new StringBuffer();
                    sb.append(id).append("#").append(robotRouteId);
                    redisTemplate.opsForList().leftPush(ROUTE_TO_DO_LIST, sb.toString());
                }
            } else {
                sendVoicePrompt(SceneType.NO_ROUTE, null);
                return ResultUtil.error("消毒路径不存在，请先设置消毒路径");
            }
        } else {
            Map<String, Object> layPositionIdsTrans = new LinkedHashMap<>();
            if (CollectionUtil.isNotEmpty(robotDisinfects)) {
                for (int i = 0; i < robotDisinfects.size(); i++) {
                    String robotLocationId = robotDisinfects.get(i).getRobotLocationId();
                    RobotLocation robotLocation = iRobotLocationService.getById(robotLocationId);
                    layPositionIdsTrans.put(i + 1 + "", robotLocation.getPositionId());
                }
                addPointToRedis(layPositionIdsTrans, id);
            }
        }
        //清空地图
        clearMap();
        //语音提醒
        sendVoicePrompt(SceneType.START, null);
        publishUtil(ExpressionType.WORKING.getValue());
        //清空手动消毒信息
        cleanManualDisinfectInfo();
        //工作中
        ControlStatusConstants.EXPRESSION_PRIORITY.working = true;
        return ResultUtil.data(true);
    }

    /**
     * 恢复任务
     */
    public void recoveryTask() {
        List<String> lastToDoList = redisTemplate.opsForList().range(LAST_TO_DO_LIST, 0, -1);
        if (redisTemplate.hasKey(LAST_TO_DO_LIST) && CollectionUtil.isNotEmpty(lastToDoList)) {
            clearTaskInfo1();
            redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "true");
            redisTemplate.opsForList().leftPushAll(TO_DO_LIST, lastToDoList);
            //语音提醒
            sendVoicePrompt(SceneType.START, null);
            publishUtil(ExpressionType.WORKING.getValue());
        }
        redisTemplate.delete(LAST_TO_DO_LIST);
    }

    /**
     * 执行导航失败的任务
     */
    public void recoveryPlanFailTask() {
        log.info("任务恢复执行完毕");
    }

    /**
     * 手动消毒模式控制
     *
     * @param disinfectManualOperationDto 手动消毒模式操作参数
     * @return Result
     */
    public Result<Boolean> manualDisinfect(DisinfectManualOperationDto disinfectManualOperationDto) {
        Jedis jedis = null;
        try {
            boolean stopping = isStopping();
            if (stopping) {
                return ResultUtil.error("急停被摁下，请松开开急停按钮");
            }
            // 检测是否需要喷雾消毒，是则检测液位状态，缺液则告警
            if (disinfectManualOperationDto.isSpray() && iRobotStatusService.isSprayLiquidLevelWarning()) {
                // 缺液语音提示
                sendVoicePrompt(SceneType.SPRAY_WATER_WARNING, null);
                return ResultUtil.error("当前水位不足，无法进行消毒工作");
            }
            jedis = RedisUtil.getJedis();
            // 播放语音---消毒过程中
            sendVoicePrompt(SceneType.DISINFECT_WARNING, null);
            // 先添加任务
            RobotTask robotTask = addRobotTask(TaskType.DISINFECT.getValue(), 1, TaskType.DISINFECT.getType(), DisinfectTaskType.FIXED_POINT_TASK.getType());
            redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_TASK_ID, robotTask.getId());
            // 添加任务记录
            iRobotTaskRecordService.saveRobotTaskRecord(null, robotTask.getId());
            // 添加消毒任务详情
            RobotDisinfectTaskDetailDto robotDisinfectTaskDetailDto = new RobotDisinfectTaskDetailDto();
            robotDisinfectTaskDetailDto.setDisinfectTime(disinfectManualOperationDto.getTime() * 60); //持久化是按秒来算的
            robotDisinfectTaskDetailDto.setDisinfectType(DisinfectTaskType.FIXED_POINT_TASK.getType());
            robotDisinfectTaskDetailDto.setSpray(disinfectManualOperationDto.isSpray() ? SUCCESS : FAIL);
            robotDisinfectTaskDetailDto.setUlray(disinfectManualOperationDto.isUlray() ? SUCCESS : FAIL);
            robotDisinfectTaskDetailDto.setXt(disinfectManualOperationDto.isXt() ? SUCCESS : FAIL);
            iRobotDisinfectTaskDetailService.saveRobotDisinfectTaskDetail(robotDisinfectTaskDetailDto);
            if (disinfectManualOperationDto.getOperation().toString().equals(START)) {
                jedis.hset(ROBOT_SYS_INFO, MANUAL_DISINFECT, "true");
                handleDisinfectDevice(disinfectManualOperationDto);
            }
            // 结束消毒模式
            if (disinfectManualOperationDto.getOperation().toString().equals(END)) {
                // 更新消毒任务记录
                iRobotTaskRecordService.updateRobotTaskRecord(NavType.MANUAL_CANCEL_TYPE.getType());
                // 更新消毒任务详情
                iRobotDisinfectTaskDetailService.updateRobotDisinfectTaskDetail();
                cleanManualDisinfectInfo();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return ResultUtil.data(true);
    }

    /**
     * 手动操作消毒设备
     *
     * @param disinfectManualOperationDto 消毒手动操作
     * @throws Exception
     */
    public void handleDisinfectDevice(DisinfectManualOperationDto disinfectManualOperationDto) throws Exception {
        ControlStatusConstants.DEV_TASK_NEED_STATUS_FAN = true;
        sendDisinfectPrompt(true, DisinfectSwitch.FAN_CTRL);
        if (disinfectManualOperationDto.isSpray()) {
            sendDisinfectPrompt(true, DisinfectSwitch.SPRAY_CTRL);
        }
        if (disinfectManualOperationDto.isUlray()) {
            ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = true;
            ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = true;
            ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = true;
            sendDisinfectPrompt(true, DisinfectSwitch.SHIELDING_CTRL);
            sendDisinfectPrompt(true, DisinfectSwitch.PULSE_CTRL);
            sendDisinfectPrompt(true, DisinfectSwitch.ULRAY_CTRL);
        }
        if (null != disinfectManualOperationDto.getTime()) {
            int count = 0;
            while (true) {
                Object manualDisinfect = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, MANUAL_DISINFECT);
                if (count == disinfectManualOperationDto.getTime() * 60) {
                    break;
                }
                if (manualDisinfect == null || manualDisinfect.toString().equals("false")) {
                    break;
                }
                count++;
                Thread.sleep(1000);
            }
        }
        // 更新消毒任务记录
        iRobotTaskRecordService.updateRobotTaskRecord(NavType.NAV_FINISH_TYPE.getType());
        // 更新消毒任务详情
        iRobotDisinfectTaskDetailService.updateRobotDisinfectTaskDetail();
        // 关闭所有消毒设备
        closeAllDisinfect();
        cleanManualDisinfectInfo();
    }

    /**
     * 清空手动消毒信息
     */
    public void cleanManualDisinfectInfo() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String str = jedis.hget(ROBOT_SYS_INFO, MANUAL_DISINFECT);
            if (StringUtils.isNotBlank(str)) {
                jedis.hset(ROBOT_SYS_INFO, MANUAL_DISINFECT, "false");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 停止任务
     *
     * @return result
     */
    public Result<Boolean> stopTaskService() {
        try {
            Integer ret = null;
            _NavControlReq navControlReq = getStopNav();
            // 停止背景音乐
            sendMusicPrompt(0);
            redisTemplate.opsForHash().put(TASK_INFO, IS_STOPPING, "true");
            redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "false");
            redisTemplate.opsForHash().put(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.FAIL.getValue().toString());
            redisTemplate.opsForHash().put(ROBOT_SYS_INFO, END_CHARGING, "false");
            // 暂停时记录未完成的导航点位
            Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_TASK_ID);
            if (o != null && StrUtil.isNotEmpty(o.toString())) {
                RobotTask robotTask = iRobotTaskService.getById(o.toString());
                if (robotTask != null && robotTask.getType().equals(TaskType.DISINFECT.getType())
                        && robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType())) {
                    boolean res = iRobotDisinfectService.routeDisinfectOperation(null, TaskOperationType.STOP.getOperation());
                    if (res) {
                        log.warn("停止成功");
                        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, LINE_DISINFECT, "false");
                    }
                }
            }
            String s = rosBridgeService.callService(YX_NAV_CONTROL, Message.getMessageType(_NavControlReq.class), JSON.toJSONString(navControlReq));
            if (StrUtil.isNotEmpty(s)) {
                _NavControlRep navControlRep = JSON.parseObject(s, _NavControlRep.class);
                ret = navControlRep.ret;
            }
            if (null != ret && ret == 0) {
                log.warn("停止成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            // 关闭所有消毒设备
            closeAllDisinfect();
        }
        return new ResultUtil<Boolean>().setData(true);
    }

    /**
     * 清空任务信息
     */
    public void clearTaskInfo1() {
        redisTemplate.opsForHash().put(TASK_INFO, NEXT_LOCATION_INFO, "");
        redisTemplate.opsForHash().put(TASK_INFO, NEXT_LOCATION_CODE, "");
        redisTemplate.opsForHash().put(TASK_INFO, NEXT_TARGET, "");
        redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "false");
        redisTemplate.opsForHash().put(TASK_INFO, IS_STOPPING, "false");
        redisTemplate.opsForHash().put(TASK_INFO, TASK_TYPE, "");
        //消毒相关
        redisTemplate.opsForHash().put(TASK_INFO, IS_ULRAY, "false");
        redisTemplate.opsForHash().put(TASK_INFO, IS_SPRAY, "false");
        redisTemplate.opsForHash().put(TASK_INFO, IS_XT, "false");
        redisTemplate.opsForHash().put(TASK_INFO, DISINFECT_TIME, "");
        redisTemplate.delete(ROUTE_TO_DO_LIST);
        redisTemplate.delete(TO_DO_LIST);
        redisTemplate.delete(ENTRANCE_GUARD_ID);
        redisTemplate.delete(ENTRANCE_POSITION_ID);
        // 解决门禁任务执行完后，仍存在当前任务ID
        RedisUtil.delHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        try {
            Thread.sleep(1000);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加机器人任务
     *
     * @param name    任务名称
     * @param level   任务等级 目前无用，全部为1
     * @param type    任务类型 ORIGIN(0,"回到原点"), CHARGING(3,"返回充电"),ENTRANCE_GUARD(4,"门禁服务"),ELEVATOR(8,"梯控模式"),DISINFECT(9,"消毒服务"),DISINFECT_BOX(10,"消毒仓服务");
     * @param subType 任务子类型
     * @return 任务信息
     */
    public RobotTask addRobotTask(String name, Integer level, Integer type, Integer subType) {
        String currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP).toString();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("name", name);
        queryWrapper.eq("map_id", currentMap);
        List<RobotTask> robotTasks = iRobotTaskService.list(queryWrapper);
        RobotTask robotTask = null;
        if (null == robotTasks || robotTasks.isEmpty()) {
            RobotTaskVo robotTaskVo = new RobotTaskVo();
            robotTaskVo.setName(name);
            //属于紧急任务
            robotTaskVo.setLevel(level);
            robotTaskVo.setMapId(currentMap);
            robotTaskVo.setType(type);
            robotTaskVo.setSubType(subType);
            robotTaskVo.setLoops(1);
            iRobotTaskService.add(robotTaskVo);
            robotTasks = iRobotTaskService.list(queryWrapper);
        }
        robotTask = robotTasks.get(robotTasks.size() - 1);
        if (name.equals(TaskType.ORIGIN.getValue())
                || name.equals(TaskType.CHARGING.getValue())) {
            RobotTaskItem robotTaskItem = new RobotTaskItem();
            robotTaskItem.setCmd(1);
            robotTaskItem.setNavMode(0);
            robotTaskItem.setTaskId(robotTask.getId());
            queryWrapper = new QueryWrapper();
            queryWrapper.eq("map_id", currentMap);
            //返回原点
            if (name.equals(TaskType.ORIGIN.getValue())) {
                queryWrapper.eq("type", RobotPositionType.START_POSITION.getType());
            }
            // 返回充电点
            if (name.equals(TaskType.CHARGING.getValue())) {
                queryWrapper.eq("type", RobotPositionType.CHARGING_POSITION.getType());
            }
            List<RobotPosition> robotPositions = iRobotPositionService.list(queryWrapper);
            if (null == robotPositions || robotPositions.isEmpty()) {
                return null;
            } else {
                RobotPosition robotPosition = robotPositions.get(robotPositions.size() - 1);
                robotTaskItem.setPositionId(robotPosition.getId());
                queryWrapper = new QueryWrapper();
                queryWrapper.eq("position_id", robotPosition.getId());
                List list = iRobotTaskItemService.list(queryWrapper);
                if (CollectionUtil.isEmpty(list)) {
                    iRobotTaskItemService.save(robotTaskItem);
                }
            }
        }
        return robotTask;
    }

    /**
     * 返回充电点
     *
     * @return result
     */
    public Result<Boolean> gotoCharging() {
        try {
            RobotInfoVo robotInfo = getRobotInfo();
            if (robotInfo.getIsMapping()) {
                return new ResultUtil<Boolean>().setErrorMsg("正在建图中");
            }
            if (!iRobotStatusService.getPositionStatus()) {
                log.warn("定位丢失，无法返回充电点");
                return new ResultUtil<Boolean>().setErrorMsg("定位丢失无法返回充电");
            }
            if (TaskType.CHARGING.getType().equals(iRobotTaskService.getCurrentTaskType())) {
                log.info("当前正在执行返回充电任务，无需重复执行充电操作");
                return new ResultUtil<Boolean>().setSuccessMsg("当前正在执行返回充电任务，无需重复执行充电操作");
            }
            if (iRobotStatusService.isDock()) {
                log.info("当前正在充电，无法执行返回充电任务");
                return new ResultUtil<Boolean>().setErrorMsg("当前正在充电，无法执行返回充电任务");
            }
            RedisUtil.hRemovePatterns(TASK_INFO, LOOPS);
            Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_DOCK_SETTING);
            if (o == null || Boolean.FALSE.toString().equalsIgnoreCase(o.toString())) {
                log.warn("自动充电功能未开启");
                return new ResultUtil<Boolean>().setErrorMsg("自动充电功能未开启");
            }
            RobotTask robotTask = addRobotTask(TaskType.CHARGING.getValue(), 1, TaskType.CHARGING.getType(), null);
            if (robotTask == null) {
                return new ResultUtil<Boolean>().setErrorMsg("没有设置充电导航点");
            }
            Object endChargingMapId = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, END_CHARGING_MAP_ID);
            Object currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
            if (endChargingMapId != null && !endChargingMapId.toString().equals(ObjectUtil.toString(currentMap))) {
                List<RobotTask> robotTaskList = iRobotTaskService.list(new LambdaQueryWrapper<RobotTask>().eq(RobotTask::getMapId, endChargingMapId).eq(RobotTask::getType, TaskType.CHARGING.getType()));
                if (CollectionUtil.isNotEmpty(robotTaskList)) {
                    robotTask = robotTaskList.get(0);
                }
            }
            boolean isCharging = isCharging();
            if (isCharging) {
                log.warn("正在充电中,无法继续返回充电");
                return new ResultUtil<Boolean>().setErrorMsg("正在充电中,无法继续返回充电");
            }
            // 判断是否已经执行了充电逻辑
            Object autoChargingStatusObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
            Integer autChargingStatus = Integer.valueOf(ObjectUtil.toString(autoChargingStatusObj));
            if (autChargingStatus.equals(AutoChargingStatus.DOING.getValue())
                    || autChargingStatus.equals(AutoChargingStatus.SUCCESS.getValue())) {
                log.warn("正在充电中,无法继续返回充电");
                return new ResultUtil<Boolean>().setErrorMsg("正在充电中,无法继续返回充电");
            }
            //发布寻找充电桩点阵表情话题---寻找充电桩
            ControlStatusConstants.EXPRESSION_PRIORITY.gotoCharing = true;
            publishUtil(ExpressionType.FIND_CHARGING_PILE.getValue());
            cleanManualDisinfectInfo();
            //清空地图
            clearMap();
            //执行的是开始任务操作
            taskControl1(robotTask.getId(), "1");
            //发送语音
            sendVoicePrompt(SceneType.BACK_TO_CHARGING, null);
            return new ResultUtil<Boolean>().setData(true);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultUtil<Boolean>().setErrorMsg("返回充电点异常");
        }
    }


    /**
     * 返回原点
     *
     * @return result
     */
    public Result<Boolean> gotoOrigin() {
        try {
            RobotTask robotTask = addRobotTask(TaskType.ORIGIN.getValue(), 1, TaskType.ORIGIN.getType(), null);
            if (robotTask == null) {
                return new ResultUtil<Boolean>().setErrorMsg("没有设置原点位置点");
            }
            RobotInfoVo robotInfo = getRobotInfo();
            if (robotInfo.getIsMapping()) {
                return new ResultUtil<Boolean>().setErrorMsg("正在建图中");
            }
            //执行的是开始任务操作
            taskControl1(robotTask.getId(), "1");
            //语音
            sendVoicePrompt(SceneType.BACK_TO_ORIGIN, null);
            //清空手动消毒模式信息
            cleanManualDisinfectInfo();
            return new ResultUtil<Boolean>().setData(true);
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultUtil<Boolean>().setErrorMsg("返回原点位置异常");
        }
    }

    /**
     * 开始建图
     *
     * @param mapType 地图类型 cartographer  gmapping 废弃
     * @param mapSize 地图大小  用于建图参数调优
     * @return result
     */
    public Result<Boolean> startCreateMap(String mapType, Integer mapSize) {
        try {
            //如果雷达处于休眠状态
            if (!handleAutoAandleLidar()) {
                log.warn("雷达或深度休眠解锁异常！");
                return new ResultUtil().setErrorMsg("雷达或深度休眠解锁异常！");
            }
            log.info("开始建图！！！！！！！！！！！！！！！！！！！！！！！");
            CREATE_MAP_STATUE = true;
            //解锁电机
            iRobotMotorService.motorControl(false);
            //定位控制关闭
            positionCtrl(true);
            sendVoicePrompt(SceneType.ALERT_OPERATION, null);
            sendRingLightPrompt(RingLightDefine.MAPPING_STATE);
            _MapControlReq mapControlReq = new _MapControlReq();
            mapControlReq.frame_id = UUID.randomUUID().toString();
            //开始建图
            if (mapType.equals(GMAPPING)) {
                // 多房间
                mapControlReq.cmd = 1;
            } else if (mapType.equals(CARTOGRAPHER)) {
                // 默认
                mapControlReq.cmd = 0;
            }
            // mapSize 用于建图参数调优
            mapControlReq.map_name = "";
//            mapControlReq.map_size = mapSize;
            String s = rosBridgeService.callService(YX_MAP_MANAGE, Message.getMessageType(_MapControlReq.class), JSON.toJSONString(mapControlReq));
            Integer ret = null;
            if (StrUtil.isNotEmpty(s)) {
                _MapControlRep mapControlRep = JSON.parseObject(s, _MapControlRep.class);
                ret = mapControlRep.ret;
            }
            if (null != ret && ret == 0) {
                Thread.sleep(2000);
                //标注起始点和充电桩点:包含/save_dock_position服务服务调用
                if (!iRobotPositionService.markStartAndChargingPose()) {
                    throw new Exception();
                }
                //建图提醒
                mapping = true;
                executorService.execute(() -> {
                    while (true) {
                        if (mapping) {
                            try {
                                //建图提醒
                                int random = CommonUtil.getRandom(1, 3);
                                if (random == 1) {
                                    sendVoicePrompt(SceneType.MAPPING_PROCESS_1, null);
                                } else if (random == 2) {
                                    sendVoicePrompt(SceneType.MAPPING_PROCESS_2, null);
                                } else if (random == 3) {
                                    sendVoicePrompt(SceneType.MAPPING_PROCESS_3, null);
                                }
                                Thread.sleep(1000 * 60 * 1);
                            } catch (Exception e) {
                                e.printStackTrace();
                            }
                        } else {
                            break;
                        }
                    }
                });
                return new ResultUtil<Boolean>().setData(true, "开始建图成功");
            } else {
                return new ResultUtil().setErrorMsg(ServiceConstants.YX_MAP_MANAGE + "服务调用异常");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("地图管理服务远程过程调用异常");
            return new ResultUtil().setErrorMsg("地图管理服务远程过程调用异常");
        }
    }

    /**
     * 临时地图保存
     *
     * @return result
     */
    public Result<Boolean> saveTempMap() {
        _MapControlReq mapControlReq = new _MapControlReq();
        mapControlReq.frame_id = UUID.randomUUID().toString();
        //临时保存
        mapControlReq.cmd = 31;
        String mapName = robotMap.concat("-").concat(DateUtil.format(new Date(), "hh-mm-ss"));
        mapControlReq.map_name = mapName;
        String s = rosBridgeService.callService(YX_MAP_MANAGE, Message.getMessageType(_MapControlReq.class), JSON.toJSONString(mapControlReq));
        if (StrUtil.isNotEmpty(s)) {
            _MapControlRep mapControlRep = JSON.parseObject(s, _MapControlRep.class);
            int ret = mapControlRep.ret;
            if (ret == 0) {
                return new ResultUtil<Boolean>().setData(true, "操作成功");
            }
        }
        return new ResultUtil<Boolean>().setData(true, "操作失败");
    }

    /**
     * 继续建图
     *
     * @return
     */
    public Result<Boolean> continueMap() {
        _MapControlReq mapControlReq = new _MapControlReq();
        mapControlReq.frame_id = UUID.randomUUID().toString();
        //继续建图
        mapControlReq.cmd = 5;
        String s = rosBridgeService.callService(YX_MAP_MANAGE, Message.getMessageType(_MapControlReq.class), JSON.toJSONString(mapControlReq));
        if (StrUtil.isNotEmpty(s)) {
            _MapControlRep mapControlRep = JSON.parseObject(s, _MapControlRep.class);
            int ret = mapControlRep.ret;
            if (ret == 0) {
                return new ResultUtil<Boolean>().setData(true, "操作成功");
            }
        }
        return new ResultUtil<Boolean>().setData(true, "操作失败");
    }

    /**
     * 结束建图
     *
     * @return true:结束成功， false:结束失败
     */
    public Result<Boolean> endCreateMap() {
        Result<Boolean> result = new Result<>();
        try {
            sendRingLightPrompt(RingLightDefine.IDLE_STATE);
            _MapControlReq mapControlReq = new _MapControlReq();
            mapControlReq.frame_id = UUID.randomUUID().toString();
            //结束建图
            mapControlReq.cmd = 2;
            mapControlReq.map_name = "";
            String s = rosBridgeService.callService(YX_MAP_MANAGE, Message.getMessageType(_MapControlReq.class), JSON.toJSONString(mapControlReq));
            Integer ret = null;
            if (StrUtil.isNotEmpty(s)) {
                _MapControlRep mapControlRep = JSON.parseObject(s, _MapControlRep.class);
                ret = mapControlRep.ret;
            }
            if (null != ret && ret == 0) {
                log.info("结束建图成功");
                result.setResult(true);
                result.setMessage("结束建图成功");
//              结束建图状态，并发送空闲状态灯光
                CREATE_MAP_STATUE = false;
                sendRingLightPrompt(RingLightDefine.IDLE_STATE);
//              在此添加一个获取当前地图并选择地图的操作；
                Object currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
                if (currentMap != null || StrUtil.isNotBlank(currentMap.toString())) {
                    log.info("结束建图后开始选择地图");
                    selectMap(currentMap.toString());
                } else {
                    log.warn("无法获取当前地图");
                }
                mapping = false;
            } else {
                CREATE_MAP_STATUE = false;
                result.setResult(false);
                result.setMessage("结束建图失败");
            }
        } catch (Exception e) {
            log.error("地图管理服务远程过程调用异常");
            result.setResult(false);
            result.setMessage("地图管理服务远程过程调用异常");
        }
        return result;
    }

    /**
     * 保存地图
     */
    public Result<Boolean> saveMap(RobotMapVo robotMapVo) {
        CREATE_MAP_STATUE = false;
        String[] mapFilesSuffix = {".png", ".yaml", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
        // 语音提示
        mapping = false;
        try {
            if (StrUtil.isBlank(robotMapVo.getMapName())) {
                return new ResultUtil().setErrorMsg("地图名称不能为空");
            }
            //queryRobotMap:根据地图名查找的地图
            RobotMap queryRobotMap = iRobotMapService.getRobotMap(robotMapVo);
            //如果查到了，说明重名，给前端一个反馈：地图名称不能重复
            if (ObjectUtils.isNotNull(queryRobotMap)) {
//              因为check方法会对boolean值取非，所以这里用isNull
                Asserts.check(ObjectUtil.isNull(queryRobotMap), "地图名称不能重复,请重新命名");
            }
            if (StrUtil.isNotBlank(robotMapVo.getStoreName()) && null != robotMapVo.getFloor()) {
                List<RobotMap> robotMapList = iRobotMapService.list(new LambdaQueryWrapper<RobotMap>()
                        .eq(RobotMap::getStoreName, robotMapVo.getStoreName()).eq(RobotMap::getFloor, robotMapVo.getFloor()));
                if (CollectionUtil.isNotEmpty(robotMapList)) {
                    return new ResultUtil().setErrorMsg("同一服务地点下的楼层数不能相同，请重新保存");
                }
            }
            _MapControlReq mapControlReq = new _MapControlReq();
            mapControlReq.frame_id = UUID.randomUUID().toString();
            //保存地图
            mapControlReq.cmd = 3;
            mapControlReq.map_name = robotMapVo.getMapName().replace(".png", "");
            String mapName = mapControlReq.map_name.concat(".png");
            RobotMap robotMap = iRobotMapService.getOne(new LambdaQueryWrapper<RobotMap>().eq(RobotMap::getName, mapName));
            if (robotMap != null) {
                log.warn("数据库存在相同名称的地图，地图会被覆盖");
            }
            Integer ret = null;
            if (robotMapVo.getType() == 1) {
                String s = rosBridgeService.callService(YX_MAP_MANAGE, Message.getMessageType(_MapControlReq.class), JSON.toJSONString(mapControlReq));
                if (StrUtil.isNotEmpty(s)) {
                    _MapControlRep mapControlRep = JSON.parseObject(s, _MapControlRep.class);
                    ret = mapControlRep.ret;
                    //结束建图
                    Thread.sleep(10000);
                }
            }
            if (robotMapVo.getType() == 2) {
                // 将地图的四份文件导入进去
                try {
                    FileUtil.copyFile(tempMapPath + "/" + mapControlReq.map_name + mapFilesSuffix[0], mapPath);
                    FileUtil.copyFile(tempMapPath + "/" + mapControlReq.map_name + mapFilesSuffix[1], mapPath);
                    FileUtil.copyFile(tempMapPath + "/" + mapControlReq.map_name + mapFilesSuffix[2], mapPath);
                    FileUtil.copyFile(tempMapPath + "/" + mapControlReq.map_name + mapFilesSuffix[3], mapPath);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                ret = 0;
            }
            if (null != ret && ret == 0) {
                //将楼层信息设置进去
                initService.initMap();
                robotMap = iRobotMapService.getOne(new LambdaQueryWrapper<RobotMap>().eq(RobotMap::getName, mapName));
                robotMap.setFloor(robotMapVo.getFloor() == null ? 1 : robotMapVo.getFloor());
                robotMap.setStoreName(robotMapVo.getStoreName());
                iRobotMapService.updateById(robotMap);
                // 结束建图
                endCreateMap();
                // 进行当前地图选择
                selectMap(robotMap.getId());
                // 保存地图路线并新建任务
                if (robotMapVo.getType() == 1) {
                    if (robotMapVo.isRouteVisible()) {
                        iRobotPositionService.updateDockPointAndOriginBySaveMap();
                        if (RobotType.X4.getType().equals(RobotBaseInfoConstant.type)) {
                            log.info("X4 无需添加默认巡线消毒任务");
                        } else {
                            iRobotRouteService.saveRouteAndTask(robotMapVo);
                        }
                    }
                }
                if (RobotType.X4.getType().equals(RobotBaseInfoConstant.type) || RobotType.X1.getType().equals(RobotBaseInfoConstant.type)) {
                    log.info("X4,X1专用保存地图语音");
                    sendVoicePrompt(SceneType.SAVE_MAP_X4, null);
                } else {
                    sendVoicePrompt(SceneType.SAVE_MAP, null);
                }
                // 上传地图到云端
                boolean result = iRobotMapService.uploadMapsToYun(robotMap.getId());
                //  将上传失败的结果发送到云端
                if (!result) {
                    rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.UPLOAD_MAP_FAILURE.getType());
                } else {
//                    上传成功不发送,只打印日志；
//                    rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.UPLOAD_MAP_SUCCESS.getType());
                    log.info("建图后的地图上传云端成功");
                }
                // 做地图备份地图
                try {
                    FileUtil.copy(mapPath + "/" + mapControlReq.map_name + mapFilesSuffix[0], mapPath + "/" + "map_bak" + mapFilesSuffix[0], true);
                    FileUtil.copy(mapPath + "/" + mapControlReq.map_name + mapFilesSuffix[1], mapPath + "/" + "map_bak" + mapFilesSuffix[1], true);
                    FileUtil.copy(mapPath + "/" + mapControlReq.map_name + mapFilesSuffix[2], mapPath + "/" + "map_bak" + mapFilesSuffix[2], true);
                    FileUtil.copy(mapPath + "/" + mapControlReq.map_name + mapFilesSuffix[3], mapPath + "/" + "map_bak" + mapFilesSuffix[3], true);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return new ResultUtil<Boolean>().setData(true, "保存地图成功");
            } else {
                rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.SAVE_MAP_ERROR.getType());
                return new ResultUtil().setErrorMsg(ServiceConstants.YX_MAP_MANAGE + "服务调用异常");
            }
        } catch (Exception e) {
            e.printStackTrace();
            rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.MANAGER_MAP.getType());
            return new ResultUtil().setErrorMsg("地图管理服务远程过程调用异常");
        } finally {
            log.warn("清空地图");
            FileUtil.clean(tempMapPath);
        }
    }


    /**
     * 上传地图
     *
     * @param mapData 地图数据
     * @param name    地图名称
     * @return result
     */
    public Result<Boolean> uploadMap(String mapData, String name) {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        try {
            String mapName = name;
            if (StrUtil.isBlank(name)) {
                if (o == null) {
                    return new ResultUtil().setErrorMsg("无法获取当前选中的地图");
                }
                RobotMap robotMap = iRobotMapService.getById(o.toString());
                mapName = robotMap.getName();
            }
            boolean uploadRes = uploadUtils.uploadImage(mapData, mapName, mapPath);
            if (uploadRes) {
                // 若地图上传成功，则选择一下当前的地图
                if (StrUtil.isBlank(name) && o != null) {
                    selectMap(o.toString());
                }
                return new ResultUtil().setSuccessMsg("地图文件上传成功");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("地图文件上传异常");
        }
        return new ResultUtil().setErrorMsg("地图文件上传失败");
    }

    /**
     * 选择地图
     *
     * @param id 地图ID
     * @return result
     */
    public Result<Boolean> selectMap(String id) {
        try {
            _MapControlReq mapControlReq = new _MapControlReq();
            redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_MAP, id);
            // 先同步区域数据
            iRobotAreasService.initRobotAreas(null);
            if (id.isEmpty()) {
                return new ResultUtil<Boolean>().setData(true, "选择地图成功");
            }
            RobotMap rm = iRobotMapService.getById(id);
            Integer floor = rm.getFloor();
            if (floor != null) {
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_FLOOR, floor.toString());
            } else {
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_FLOOR, "");
            }
            mapControlReq.frame_id = UUID.randomUUID().toString();
            //选择地图
            mapControlReq.cmd = 4;
            mapControlReq.map_name = rm.getName().split("\\.")[0];
            long startTime = System.currentTimeMillis();
            RedisUtil.setValue(ROBOT_CHANGE_MAP, Boolean.TRUE.toString(), 60 * 2);
            String s = rosBridgeService.callService(YX_MAP_MANAGE, Message.getMessageType(_MapControlReq.class), JSON.toJSONString(mapControlReq));
            long endTime = System.currentTimeMillis();
            log.info("选择地图耗时：" + (endTime - startTime) + "毫秒");
            Thread.sleep(5000);
            Integer ret = null;
            if (StrUtil.isNotEmpty(s)) {
                _MapControlRep mapControlRep = JSON.parseObject(s, _MapControlRep.class);
                ret = mapControlRep.ret;
            }
            if (null != ret && ret == 0) {
                //更新数据库中的选中的地图
                List<RobotMap> robotMapList = iRobotMapService.list();
                if (null != robotMapList && !robotMapList.isEmpty()) {
                    for (RobotMap robotMap : robotMapList) {
                        if (robotMap.getId().equals(id)) {
                            robotMap.setIsDefault("0");
                        } else {
                            robotMap.setIsDefault("1");
                        }
                        iRobotMapService.updateById(robotMap);
                    }
                }
                // 进行重定位
                initService.initPose(false, null);
                // 更新充电桩位置
                iRobotPositionService.updateDockCheck(id);
                return new ResultUtil<Boolean>().setData(true, "选择地图成功");
            } else {
                return new ResultUtil().setErrorMsg(ServiceConstants.YX_MAP_MANAGE + "服务调用异常");
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("选择地图异常");
            return new ResultUtil().setErrorMsg("地图管理服务远程过程调用异常");
        }
    }

    /**
     * 定位控制
     *
     * @param operation true:关闭 / false：开启
     * @return
     */
    public boolean positionCtrl(boolean operation) {
        try {
            Thread.sleep(1000);
        } catch (Exception e) {
        }
        _NavControlReq navControlReq = new _NavControlReq();
        navControlReq.pose_name = "";
        if (operation) {
            navControlReq.cmd = 7;
        } else {
            navControlReq.cmd = 6;
        }
        navControlReq.frame_id = UUID.randomUUID().toString();
        navControlReq.nav_mode = 0;
        navControlReq.pose_type = 0;
        navControlReq.pose_info = null;
        String s = rosBridgeService.callService(YX_NAV_CONTROL, Message.getMessageType(_NavControlReq.class), JSON.toJSONString(navControlReq));
        try {
            Thread.sleep(1000);
        } catch (Exception e) {
        }
        if (StrUtil.isNotEmpty(s)) {
            _NavControlRep navControlRep = JSON.parseObject(s, _NavControlRep.class);
            int ret = navControlRep.ret;
            if (ret == 0) {
                if (navControlReq.cmd == 7) {
                    log.info("定位控制关闭成功");
                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, POSITION_CTRL_STATUS, "true");
                }
                if (navControlReq.cmd == 6) {
                    log.info("定位控制开启成功");
                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, POSITION_CTRL_STATUS, "false");
                }
                return true;
            }
        }
        return false;
    }

    /**
     * 设置自动充电
     *
     * @param operation true:自动充电/ false:关闭自动充电
     * @return true 充电成功
     */
    public boolean autoDockCtrl(boolean operation) {
        if (operation) {
            log.info("自动充电开启");
            redisTemplate.opsForHash().put(ROBOT_SYS_INFO, AUTO_DOCK_SETTING, "true");
        } else {
            log.warn("自动充电关闭");
            redisTemplate.opsForHash().put(ROBOT_SYS_INFO, AUTO_DOCK_SETTING, "false");
        }
        return true;
    }

    /**
     * 获取自动充电状态
     *
     * @return true
     */
    public boolean autoDockStatus() {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_DOCK_SETTING);
        return "true".equals(ObjectUtil.toString(o));
    }

    /**
     * 播放背景音乐
     *
     * @param operation (0：结束播放背景音乐 1: 开始播放背景音乐)
     */
    public void sendMusicPrompt(Integer operation) {
        _Voice voice = new _Voice();
        voice.cmd = operation;
        voice.type = 1;
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, BACKGROUND_MUSIC);
        if (operation.equals(musicOperation)) {
            return;
        } else {
            musicOperation = operation;
        }
        String data = "";
        if (o != null && StrUtil.isNotBlank(o.toString())) {
            data = o.toString().substring(0, o.toString().lastIndexOf("."));
        }
        voice.data = data;
        String topic = "";
        if (operation == 0) {
            topic = STOP_VOICE_CTRL;
        }
        if (operation == 1) {
            topic = PLAY_VOICE_CTRL;
        }
        rosBridgeService.publish(topic, Message.getMessageType(_Voice.class), JSONObject.toJSONString(voice));
    }

    /**
     * 发送语音提醒
     *
     * @param sceneType 语音类型
     * @param content   语音内容
     */
    public synchronized void sendVoicePrompt(SceneType sceneType, String content) {
        if (shoutdownCtrl == true) {
            return;
        }
        if (TALK_STATUS == true) {
            return;
        }
        executorService.execute(() -> {
            Jedis jedis = null;
            try {
                jedis = RedisUtil.getJedis();
                if (sceneType != null && content == null) {
                    String value = sceneType.getValue();
                    String o = Objects.requireNonNull(jedis).hget(ROBOT_SYS_INFO, SPEECH_LIST);
                    if (o != null) {
                        List<SceneSpeechVo> sceneSpeechVos = JSONObject.parseArray(o, SceneSpeechVo.class);
                        if (CollectionUtil.isNotEmpty(sceneSpeechVos)) {
                            List<SceneSpeechVo> sceneSpeechVos1 = new ArrayList<>();
                            for (SceneSpeechVo sceneSpeechVo : sceneSpeechVos) {
                                if (sceneSpeechVo.getScene().equals(value)) {
                                    sceneSpeechVos1.add(sceneSpeechVo);
                                }
                            }
                            if (CollectionUtil.isNotEmpty(sceneSpeechVos1)) {
                                int random = CommonUtil.getRandom(1, sceneSpeechVos1.size());
                                SceneSpeechVo sceneSpeechVo = sceneSpeechVos1.get(random - 1);
                                String fileName = sceneSpeechVo.getFileName();
                                _Voice voice = new _Voice();
                                voice.type = 0;
                                voice.cmd = 1;
                                // 仅仅只发送不带后缀的文件名称
                                voice.data = fileName.substring(0, fileName.lastIndexOf("."));
//                                if (ControlStatusConstants.TALK_STATUS) {
                                rosBridgeService.publish(TopicConstants.PLAY_VOICE_CTRL, Message.getMessageType(_Voice.class), JSONObject.toJSONString(voice));
//                                }
                                log.warn("发送语音指令:{},语音详情:{}", fileName, sceneType.getValue());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("发送语音提醒异常");
            } finally {
                RedisUtil.closeJedis(jedis);
            }
        });
    }

    /**
     * 对话语音版的“发送语音”
     *
     * @param talkScene
     * @param content
     */
    public void sendVoicePrompt(TalkScene talkScene, String content) {
        if (shoutdownCtrl == true) {
            return;
        }
        executorService.execute(() -> {
            Jedis jedis = null;
            try {
                jedis = RedisUtil.getJedis();
                if (talkScene != null && content == null) {
                    String scene = talkScene.getSence();
                    String o = Objects.requireNonNull(jedis).hget(ROBOT_SYS_INFO, SPEECH_LIST);
                    if (o != null) {
                        List<SceneSpeechVo> sceneSpeechVos = JSONObject.parseArray(o, SceneSpeechVo.class);
                        if (CollectionUtil.isNotEmpty(sceneSpeechVos)) {
                            List<SceneSpeechVo> sceneSpeechVos1 = new ArrayList<>();
                            for (SceneSpeechVo sceneSpeechVo : sceneSpeechVos) {
                                if (sceneSpeechVo.getScene().equals(scene)) {
                                    sceneSpeechVos1.add(sceneSpeechVo);
                                }
                            }
                            if (CollectionUtil.isNotEmpty(sceneSpeechVos1)) {
                                int random = CommonUtil.getRandom(1, sceneSpeechVos1.size());
                                SceneSpeechVo sceneSpeechVo = sceneSpeechVos1.get(random - 1);
                                String fileName = sceneSpeechVo.getFileName();
                                _Voice voice = new _Voice();
                                voice.type = 0;
                                voice.cmd = 1;
                                // 仅仅只发送不带后缀的文件名称
                                voice.data = fileName.substring(0, fileName.lastIndexOf("."));
//                                if (!ControlStatusConstants.TALK_STATUS) {
                                rosBridgeService.publish(TopicConstants.PLAY_VOICE_CTRL, Message.getMessageType(_Voice.class), JSONObject.toJSONString(voice));
//                                }
                                log.warn("发送语音指令:{},语音详情:{}", fileName, talkScene.getSence());
                            }
                        }
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
                log.error("发送语音提醒异常");
            } finally {
                RedisUtil.closeJedis(jedis);
            }
        });
    }

    /**
     * 消毒设备控制
     *
     * @param data            true
     * @param disinfectSwitch 设备开关
     */
    public void sendDisinfectPrompt(boolean data, DisinfectSwitch disinfectSwitch) {
        _Bool bool = new _Bool();
        bool.data = data;
        String topic = "";
        if (disinfectSwitch.getValue().equals(DisinfectSwitch.ULRAY_CTRL.getValue())) {
            topic = TopicConstants.ULRAY_CTRL;
            if (!data && checkUltraviolet) {
                checkUltraviolet = false;
            }
        } else if (disinfectSwitch.getValue().equals(DisinfectSwitch.SPRAY_CTRL.getValue())) {
            topic = TopicConstants.SPRAY_CTRL;
            if (!data && checkSpray) {
                checkSpray = false;
            }
        } else if (disinfectSwitch.getValue().equals(DisinfectSwitch.FAN_CTRL.getValue())) {
            topic = TopicConstants.FAN_CTRL;
        } else if (disinfectSwitch.getValue().equals(DisinfectSwitch.SHIELDING_CTRL.getValue())) {
            topic = TopicConstants.SHIELDING_CTRL;
            if (!data && checkShielding) {
                checkShielding = false;
            }
        } else if (disinfectSwitch.getValue().equals(DisinfectSwitch.PULSE_CTRL.getValue())) {
            topic = TopicConstants.PULSE_CTRL;
            if (!data && checkPulse) {
                checkPulse = false;
            }
        }
        if (StrUtil.isNotEmpty(topic)) {
            Jedis jedis = null;
            try {
                jedis = RedisUtil.getJedis();
                if (topic.equals(TopicConstants.SHIELDING_CTRL)) {
                    // 获取保护罩状态
                    String s = jedis.get(TOPIC + "::" + SHIELDING_STATUS);
                    if (StrUtil.isNotEmpty(s)) {
                        rosBridgeService.publish(topic, Message.getMessageType(_Bool.class), JSON.toJSONString(bool));
                        String deviceType = RDes.decrypt(RDes.getSerialNumber()).split("-")[0];
                        if ("X4".equals(deviceType)) {
                            Thread.sleep(5000);
                        }
                    }
                } else if (topic.equals(TopicConstants.PULSE_CTRL)) {
                    //判断是否有脉冲
                    String pulseStatus = jedis.get(TOPIC + "::" + PULSE_STATUS);
                    if (StrUtil.isNotEmpty(pulseStatus)) {
                        rosBridgeService.publish(topic, Message.getMessageType(_Bool.class), JSON.toJSONString(bool));
                    }
                } else if (topic.equals(TopicConstants.XM_FAN_CTRL)) {
                    _Uint8 uint8 = new _Uint8();
                    if (!data) {
                        uint8.data = 0;
                    } else {
                        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, XM_FAN_LEVEL);
                        if (o != null && StrUtil.isNotEmpty(o.toString())) {
                            uint8.data = Short.valueOf(o.toString());
                        } else {
                            uint8.data = 2;
                        }
                    }
                    rosBridgeService.publish(topic, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
                } else {
                    rosBridgeService.publish(topic, Message.getMessageType(_Bool.class), JSON.toJSONString(bool));
                    Thread.sleep(200);
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                log.warn("发送的指令为：{},值为:{}", topic, data);
                RedisUtil.closeJedis(jedis);
            }
        }
    }

    /**
     * 关闭所有的消毒设备
     */
    public void closeAllDisinfect() {
        ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = false;

        ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = false;

        ControlStatusConstants.DEV_TASK_NEED_STATUS_FAN = false;

        ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = false;

        ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = false;

        sendDisinfectPrompt(false, DisinfectSwitch.SPRAY_CTRL);
        //关闭风扇
        sendDisinfectPrompt(false, DisinfectSwitch.FAN_CTRL);
        //关闭脉冲
        sendDisinfectPrompt(false, DisinfectSwitch.PULSE_CTRL);
        //关闭紫外线保护
        sendDisinfectPrompt(false, DisinfectSwitch.SHIELDING_CTRL);
        //关闭紫外
        sendDisinfectPrompt(false, DisinfectSwitch.ULRAY_CTRL);

        if (ControlStatusConstants.VIDEO_LINE_FINISH_IS_CLOSE) {
            VIDEO_IS_CLOSE = true;
            VIDEO_PATH = null;
            HkUtils.endRecord();
        } else {
            ControlStatusConstants.VIDEO_LINE_FINISH_IS_CLOSE = true;
            VIDEO_IS_CLOSE = false;
        }

    }

    /**
     * 进出水控制
     *
     * @param operation 1 进水 / -1 出水
     * @param action    1 开始 / 0 停止
     */
    public void waterCtrl(String operation, String action) {
        Jedis jedis = null;
        _Uint8 uint8 = new _Uint8();
        try {
            jedis = RedisUtil.getJedis();
            String waterInStatusStr = jedis.get(TOPIC + "::" + TopicConstants.WATER_IN_STATUS);
            String waterOutStatusStr = jedis.get(TOPIC + "::" + TopicConstants.WATER_OUT_STATUS);
            boolean waterInStatus = !StrUtil.isNotEmpty(waterInStatusStr) || JSON.parseObject(waterInStatusStr, _Uint8.class).data != 0;
            boolean waterOutStatus = !StrUtil.isNotEmpty(waterOutStatusStr) || JSON.parseObject(waterOutStatusStr, _Uint8.class).data != 0;
            if (operation.equals(INLET)) {
                if (action.equals(START)) {
                    if (!waterInStatus && !waterOutStatus) {
                        uint8.data = 2;
                    }
                    if (!waterInStatus && waterOutStatus) {
                        uint8.data = 3;
                    }
                    log.warn("进水开始");
                }
                if (action.equals(END)) {
                    if (waterInStatus && waterOutStatus) {
                        uint8.data = 1;
                    }
                    if (waterInStatus && !waterOutStatus) {
                        uint8.data = 0;
                    }
                    log.warn("进水停止");
                }
            }
            if (operation.equals(EFFLUENT)) {
                if (action.equals(START)) {
                    if (!waterOutStatus && !waterInStatus) {
                        uint8.data = 1;
                    }
                    if (!waterOutStatus && waterInStatus) {
                        uint8.data = 3;
                    }
                    log.warn("排水开始");
                }
                if (action.equals(END)) {
                    if (waterOutStatus && waterInStatus) {
                        uint8.data = 2;
                    }
                    if (waterOutStatus && !waterInStatus) {
                        uint8.data = 0;
                    }
                    log.warn("排水停止");
                }
            }
            // 发送的指令
            log.info("进出水指令:" + JSON.toJSONString(uint8));
            rosBridgeService.publish(WATER_CTRL, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
            Thread.sleep(2000);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 判断是否在充电
     *
     * @return true/false
     */
    public boolean isCharging() {
        Jedis jedis = null;
        boolean res = false;
        try {
            jedis = RedisUtil.getJedis();
            String autoDockStateResult = jedis.get(TOPIC + "::" + TopicConstants.AUTO_DOCK_STATE);
            if (StrUtil.isNotEmpty(autoDockStateResult)) {
                _AutoDockState autoDockState = JSONObject.parseObject(autoDockStateResult, _AutoDockState.class);
                short type = autoDockState.type;
                if (type == AutoDockType.MANUAL_DOCK.getType()) {
                    res = true;
                    jedis.hset(ROBOT_SYS_INFO, CHARGING_CONDITION, ChargingCondition.MANUAL.getType().toString());
                    // 保存充电记录
                    iRobotChargingRecordService.saveRobotChargingRecord(1);
                } else if (type == AutoDockType.AUTO_DOCK.getType()
                        || type == AutoDockType.HANDLE_PUSH_DOCK.getType()) {
                    if (type == AutoDockType.HANDLE_PUSH_DOCK.getType()) {
                        jedis.hset(ROBOT_SYS_INFO, CHARGING_CONDITION, ChargingCondition.AUTO_PUSH_CHARGING.getType().toString());
                        iRobotChargingRecordService.saveRobotChargingRecord(1);
                    }
                    jedis.hset(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.SUCCESS.getValue().toString());
//                   !initPose && !isManualCharge()
                    if (!initPose) {
                        initPose = true;
                        // 重定位
                        initService.initPose(false, null);
                        // 重置脱离充电桩控制条件；
                        END_CHARING = false;
                    }
                    jedis.hset(ROBOT_SYS_INFO, POSITION_STATUS, SUCCESS);
                    res = true;
                } else {
                    initPose = false;
                    String udriveAutoState = RedisUtil.getTopicValue(UDRIVE_AUTO_DOCK_STATE);
                    if (org.apache.commons.lang3.StringUtils.isBlank(udriveAutoState) || ObjectUtil.toString(null).equals(udriveAutoState)) {
                        jedis.hset(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.UNDO.getValue().toString());
                        iRobotChargingRecordService.saveRobotChargingRecord(2);
                    } else {
                        jedis.hset(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.DOING.getValue().toString());
                    }
                }
            }

//            if(res) {
//                lowPowerCtrl(true);
//            }else{
//                lowPowerCtrl(false);
//            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return res;
    }


    /**
     * 判断是否触发防撞条
     *
     * @return true:触发防撞条 false:未触发
     */
    public boolean isCollision() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
//            String collision = jedis.get(TOPIC + "::" + TopicConstants.COLLISION);
            String collision = RedisUtil.getTopicValue(COLLISION);
            if (StrUtil.isNotEmpty(collision)) {
                _Bool bool = JSON.parseObject(collision, _Bool.class);
                if (bool.data) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return false;
    }


    /**
     * 到达充电桩导航点开始执行自动充电操作
     */
    public void autoDock() {
        try {
            //电机上电
            Thread.sleep(3000);
            RedisUtil.setTopic(TOPIC + "::" + TopicConstants.UDRIVE_AUTO_DOCK_STATE, "");
            iRobotMotorService.motorControl(true);
            //判断电量值是否低于90，如果低于90则充电
            _RobotDockRep robotDockRep = iRobotChargingService.startDock();
            String autoChargingStatus = RedisUtil.getHash(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
            log.info("==================autoChargingStatus:{},!isDoing:{}", autoChargingStatus, !AutoChargingStatus.DOING.getValue().toString().equals(autoChargingStatus));
            if (ObjectUtil.isNotNull(robotDockRep) && !AutoChargingStatus.DOING.getValue().toString().equals(autoChargingStatus)) {
                if (robotDockRep.ret == RosResult.SUCCESS.getCode()) {
                    log.warn("自动充电设置成功");
                    RedisUtil.hset(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.DOING.getValue().toString());
                } else {
                    log.warn("自动充电设置失败");
                    return;
                }
                executorService.execute(() -> {
                    while (true) {
                        try {
                            _UdriveAutoDockState udriveAutoDockState = iRobotChargingService.getAutoDockState();
                            log.info("udriveAutoDockState:{}", JSON.toJSONString(udriveAutoDockState));
                            if (ObjectUtil.isNotNull(udriveAutoDockState)) {
                                if (!udriveAutoDockState.working && !udriveAutoDockState.charged) {
                                    boolean isChargingTask = TaskType.CHARGING.getType().equals(iRobotTaskService.getCurrentTaskType());
                                    // 删除充电任务ID
                                    if (isChargingTask && !isStopping()) {
                                        RedisUtil.delHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                                    } else {
                                        AUTO_DOCK_ERROR_CURRENT_TIME = 0;
                                    }
                                    log.error("前往充电失败,循环退出");
                                    log.error("失败原因:{}", JSON.toJSONString(udriveAutoDockState));
                                    //短信推送
                                    //解决第三次返回充电成功也会发送短信BUG
                                    if (cycleOutChargingMessage && AUTO_DOCK_ERROR_CURRENT_TIME.equals(AUTO_DOCK_ERROR_TRY_TIME) && !(udriveAutoDockState.error == 0)) {
                                        log.info("第" + AUTO_DOCK_ERROR_CURRENT_TIME + "次返回充电失败消息发送！");
                                        sendMessageUtil.sendShortMessage("前往充电失败-原因ERROR_CODE：" + udriveAutoDockState.error);
                                        rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.GO_TO_CHARING_FAILURE.getType());
                                        cycleOutChargingMessage = false;
                                    }
                                    RedisUtil.delTopicValue(UDRIVE_AUTO_DOCK_STATE);
                                    iRobotChargingService.cancelDock();
                                    if (AUTO_DOCK_ERROR_CURRENT_TIME < AUTO_DOCK_ERROR_TRY_TIME
                                            && isChargingTask && !isStopping()) {
                                        AUTO_DOCK_ERROR_CURRENT_TIME++;
                                        log.info("再次执行返回充电任务:{}", AUTO_DOCK_ERROR_CURRENT_TIME);
                                        if (ObjectUtil.isNull(iRobotTaskService.getCurrentTaskType())) {
                                            gotoCharging();
                                        }
                                    }
                                    RedisUtil.hset(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.FAIL.getValue().toString());
                                    break;
                                }
                                if (udriveAutoDockState.charged) {
                                    AUTO_DOCK_ERROR_CURRENT_TIME = 0;
                                    // 删除充电任务ID
                                    if (TaskType.CHARGING.getType().equals(iRobotTaskService.getCurrentTaskType())) {
                                        RedisUtil.delHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                                    }
                                    RedisUtil.delTopicValue(UDRIVE_AUTO_DOCK_STATE);
                                    log.warn("前往充电成功,循环退出");
                                    RedisUtil.hset(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.SUCCESS.getValue().toString());
                                    // 如果前往充电成功,循环退出，将短信控制条件重置
                                    IRobotChargingRecordServiceImpl.cycleOutChargingMessage = true;
                                    break;
                                }
                            }
                            ThreadUtil.sleep(1000);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                    RedisUtil.hset(JikeConstants.JIKE, JikeConstants.ORDER_STATE, OrderStateEnum.FINISH.getValue().toString());
                    Object o = RedisUtil.getHash(ROBOT_SYS_INFO, CHARGING_CONDITION);
                    if (o == null || StrUtil.isBlank(o.toString())) {
                        RedisUtil.hset(ROBOT_SYS_INFO, CHARGING_CONDITION, ChargingCondition.AUTO_CHARGING.getType().toString());
                    }
                    iRobotChargingRecordService.saveRobotChargingRecord(1);
                });
            }
        } catch (Exception e) {
            log.error("自动充电服务调用异常");
        }
    }

    /**
     * 结束充电 脱离充电桩
     *
     * @return
     */
    public boolean endCharging() {
        try {
            iRobotMotorService.motorControl(true);
            sendVoicePrompt(SceneType.END_CHARGING, null);
            RedisUtil.hset(ROBOT_SYS_INFO, END_CHARGING, "true");
            String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
            if (StrUtil.isNotBlank(currentMap)) {
                RedisUtil.hset(ROBOT_SYS_INFO, END_CHARGING_MAP_ID, currentMap);
            }
            // 在脱离充电桩前进行检测
            if (!handleAutoAandleLidar()) {
                log.warn("雷达或深度休眠解锁异常！");
                return false;
            }
            _RobotDockRep robotDockRep = iRobotChargingService.outDock();
            if (ObjectUtil.isNotNull(robotDockRep)) {
                if (robotDockRep.ret == 0) {
                    log.info("已结束充电");
                    END_CHARING = true;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("结束充电服务调用异常");
        }
        return false;
    }

//    /**
//     * 取消充电
//     *
//     * @return true/false
//     */
//    public boolean cancelCharge() {
//        _RobotDockReq robotDockReq = new _RobotDockReq();
//        robotDockReq.cmd = 4;
//        String s2 = rosBridgeService.callService(AUTO_DOCK, Message.getMessageType(_RobotDockReq.class), JSON.toJSONString(robotDockReq));
//        if (StrUtil.isNotEmpty(s2)) {
//            _RobotDockRep robotDockRep2 = JSON.parseObject(s2, _RobotDockRep.class);
//            int ret2 = robotDockRep2.ret;
//            if (ret2 == 0) {
//                log.info("取消充电成功");
//                return true;
//            }
//        }
//        return false;
//    }

    /**
     * 设置速度等级
     *
     * @param level 1 代表低速 2 中速 3 高速
     */
    public boolean settingSpeedLevel(String level) {
        try {
            _SpeedLevelReq speedLevelReq = new _SpeedLevelReq();
            speedLevelReq.level = level;
            String s = rosBridgeService.callService(SPEED_LEVEL, Message.getMessageType(_SpeedLevelReq.class), JSON.toJSONString(speedLevelReq));
            if (StrUtil.isNotEmpty(s)) {
                _SpeedLevelRep speedLevelRep = JSON.parseObject(s, _SpeedLevelRep.class);
                if (speedLevelRep.ret == 0) {
                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, SPEED_LEVEL, level);
                    return true;
                }
            }
        } catch (Exception e) {
            log.error("设置速度等级失败");
        }
        return false;
    }

    /**
     * 获取机器人位置信息
     *
     * @return
     */
    public String getRobotLocation() {
        String result = "";
        String topicPoseResult = redisTemplate.opsForValue().get(TOPIC + "::" + TopicConstants.ROBOT_POSE_STAMPED);
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (StrUtil.isEmpty(topicPoseResult) || o == null) {
            return result;
        }
        // 机器人位置
        _PoseStamped poseStamped = JSON.parseObject(topicPoseResult, _PoseStamped.class);
        double x1 = poseStamped.pose.position.x;
        double y1 = poseStamped.pose.position.y;
        List<RobotPosition> robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getMapId, o.toString()));
        String shortestDistanceId = null;
        Double shortestDistance = null;
        // 用于计算机器人最靠近哪个点
        for (RobotPosition robotPosition : robotPositionList) {
            if (robotPosition.getType().equals(RobotPositionType.ROOM_POSITION.getType())) {
                RobotWorldPosition robotWorldPosition = iRobotWorldPositionService.getById(robotPosition.getWorldPoseId());
                String id = robotWorldPosition.getId();
                Double x2 = robotWorldPosition.getPositionX();
                Double y2 = robotWorldPosition.getPositionY();
                double tempX = x1 > x2 ? (x1 - x2) : (x2 - x1);
                double tempY = y1 > y2 ? (y1 - y2) : (y2 - y1);
                double distance = Math.sqrt(tempX * tempX + tempY * tempY);
                if (shortestDistanceId == null && shortestDistance == null) {
                    shortestDistanceId = id;
                    shortestDistance = distance;
                } else {
                    if (distance < shortestDistance) {
                        shortestDistanceId = id;
                        shortestDistance = distance;
                    }
                }
            }
        }
        RobotPosition robotPosition = iRobotPositionService.getOne(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getWorldPoseId, shortestDistanceId));
        if (null != robotPosition) {
            RobotLocation robotLocation = iRobotLocationService.getOne(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getPositionId, robotPosition.getId()));
            if (null != robotLocation) {
                result = robotLocation.getLocationCode();
                // 针对酒店房间多点消毒 房间号-点位号（1、2、3）
                if (result.endsWith("-1")
                        || result.endsWith("-2")
                        || result.endsWith("-3")
                        || result.endsWith("-4")
                        || result.endsWith("-5")) {
                    result = result.substring(0, result.lastIndexOf("-"));
                }
            }
        }
        return result;
    }

    /**
     * 设备信息
     *
     * @return
     * @throws Exception
     */
    public RobotDeviceInfoVo getRobotDeviceInfo() throws Exception {
        //主机地址
        Jedis jedis = RedisUtil.getJedis();
        RobotDeviceInfoVo robotDeviceInfoVo = new RobotDeviceInfoVo();
        try {
            robotDeviceInfoVo.setUpdateTime(new Date());
            String host = jedis.hget(ROBOT_SYS_INFO, HOST);
            String hostStr = host == null ? "" : host;
            robotDeviceInfoVo.setDeviceHost(hostStr);
            //当前地图
            String currentMap = jedis.hget(ROBOT_SYS_INFO, CURRENT_MAP);
            robotDeviceInfoVo.setCurrentMap(currentMap);
            String rosStatus = jedis.hget(ROBOT_SYS_INFO, ROS_STATUS);
            String rosStatusStr = rosStatus == null ? "" : rosStatus;
            //设备状态
            if (rosStatusStr.equals(SUCCESS)) {
                robotDeviceInfoVo.setDeviceStatus(1);
            } else {
                robotDeviceInfoVo.setDeviceStatus(0);
            }
            //运行信息
            String workingInfo = "";
            //电池电量
            double battery = 100.00;
            String taskData = jedis.hget(TASK_INFO, TASK_DATA);
            if (StringUtils.isNotEmpty(taskData)) {
                Map<String, Object> map = JSONObject.parseObject(taskData, Map.class);
                workingInfo = map.get(ALERT_MSG).toString();
                battery = Double.valueOf(map.get(REMAIN_BATTERY).toString());
            }
            robotDeviceInfoVo.setWorkingInfo(workingInfo);
            robotDeviceInfoVo.setBattery(battery);
            //获取里程信息
            double mileage = 0.00;
            String mileageObj = jedis.hget(ROBOT_SYS_INFO, MILEAGE);
            String mileageStr = mileageObj == null ? "" : mileageObj;
            if (StringUtils.isNotEmpty(mileageStr)) {
                BigDecimal bd = new BigDecimal(mileageStr);
                mileage = bd.setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue();
            }
            robotDeviceInfoVo.setMileage(mileage);
            //获取运行时间
            double runtime = 0.00;
            String runtimeObj = jedis.hget(ROBOT_SYS_INFO, RUNTIME);
            String runtimeStr = runtimeObj == null ? "" : runtimeObj;
            if (StringUtils.isNotEmpty(runtimeStr)) {
                runtime = Double.valueOf(runtimeStr);
            }
            robotDeviceInfoVo.setRunTime(runtime);
            //获取音量大小
            Integer volumeSize = 60;
            String volumeSizeObj = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.VOLUME_SIZE);
            String volumeSizeStr = volumeSizeObj == null ? "" : volumeSizeObj;
            if (StrUtil.isNotEmpty(volumeSizeStr)) {
                volumeSize = Integer.valueOf(volumeSizeStr);
            }
            robotDeviceInfoVo.setVolumeSize(volumeSize);
            //喷雾告警
            boolean sprayLiquidWarning = iRobotStatusService.isSprayLiquidLevelWarning();
            robotDeviceInfoVo.setSprayWarning(sprayLiquidWarning ? 1 : 0);
            //急停开关
            boolean stopping = isStopping();
            robotDeviceInfoVo.setStop(stopping ? 1 : 0);
            //防撞条
            boolean collision = isCollision();
            robotDeviceInfoVo.setCollision(collision ? 1 : 0);
            // 空闲状态
            boolean idle = isIdle();
            robotDeviceInfoVo.setIdle(idle ? 1 : 0);
            // 建图状态
            robotDeviceInfoVo.setMapping(mapping ? 1 : 0);
            // 充电状态
            boolean charging = isCharging();
            robotDeviceInfoVo.setCharging(charging ? 1 : 0);
            //设备保养时间(单位：年)
            String deviceMaintenanceTimeStr = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.DEVICE_MAINTENANCE_TIME);
            robotDeviceInfoVo.setDeviceMaintenanceTime(Long.valueOf(deviceMaintenanceTimeStr));
            //设备保养日期
            String deviceMaintenanceDateStr = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.DEVICE_MAINTENANCE_DATE);
            robotDeviceInfoVo.setDeviceMaintenanceDate(DateUtil.parseDate(deviceMaintenanceDateStr));
            //脉冲灯保养时间
            String ulrayMaintenanceTime = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_MAINTENANCE_TIME);
            robotDeviceInfoVo.setUlrayMaintenanceTime(Long.valueOf(ulrayMaintenanceTime));
            //脉冲灯使用时间(秒)
            String ulrayUsageTime = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME);
            robotDeviceInfoVo.setUlrayUsageTime(Long.valueOf(ulrayUsageTime));
            //脉冲灯保养日期
            String ulrayMaintenanceDate = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_MAINTENANCE_DATE);
            robotDeviceInfoVo.setUlrayMaintenanceDate(DateUtil.parseDate(ulrayMaintenanceDate));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return robotDeviceInfoVo;
    }

    /**
     * 获取消毒设置信息
     *
     * @return
     */
    public DisinfectSettingVo getDisinfectSettingInfo() {
        DisinfectSettingVo disinfectSettingVo = new DisinfectSettingVo();
        //活物关闭紫外线
        Object livingThingsUlray = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, LIVING_THINGS_ULRAY);
        if (null != livingThingsUlray) {
            disinfectSettingVo.setLivingThingsUlRay(Boolean.valueOf(livingThingsUlray.toString()));
        }
        //语音警告
        Object voiceWarning = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, VOICE_WARNING);
        if (null != voiceWarning) {
            disinfectSettingVo.setVoiceWarning(Boolean.valueOf(voiceWarning.toString()));
        }
        //背景音乐
        Object backgroundMusic = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, BACKGROUND_MUSIC);
        if (null != backgroundMusic) {
            disinfectSettingVo.setBackgroundMusic(backgroundMusic.toString());
        }
        //西铭风扇等级
        Integer xmFanlevel = 2; //默认风速为中速
        Object xmFanLevelObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, XM_FAN_LEVEL);
        if (xmFanLevelObj != null && StrUtil.isNotEmpty(xmFanLevelObj.toString())) {
            xmFanlevel = Integer.valueOf(xmFanLevelObj.toString());
        }
        disinfectSettingVo.setXmFanLevel(xmFanlevel);
        //沿途速度
        Object disinfectSpeed = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, DISINFECT_SPEED);
        if (null != disinfectSpeed) {
            disinfectSettingVo.setDisinfectSpeed(Double.valueOf(disinfectSpeed.toString()));
        }
        //紫外线
        Object ulray = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, ULRAY);
        if (null != ulray) {
            disinfectSettingVo.setUlRay(Boolean.valueOf(ulray.toString()));
        }
        //喷雾
        Object spray = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SPRAY);
        if (null != ulray) {
            disinfectSettingVo.setSpray(Boolean.valueOf(spray.toString()));
        }
        //沿途消毒模块
        Object xt = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, XT);
        if (null != xt) {
            disinfectSettingVo.setXt(Boolean.valueOf(xt.toString()));
        }
        return disinfectSettingVo;
    }

    /**
     * 设置消毒信息
     *
     * @param disinfectSettingVo 消毒设备参数配置
     * @return result
     */
    public Result<Boolean> settingDisinfectInfo(DisinfectSettingVo disinfectSettingVo) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, LIVING_THINGS_ULRAY, disinfectSettingVo.isLivingThingsUlRay() + "");
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, VOICE_WARNING, disinfectSettingVo.isVoiceWarning() + "");
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, BACKGROUND_MUSIC, StrUtil.isEmpty(disinfectSettingVo.getBackgroundMusic()) ? "" : disinfectSettingVo.getBackgroundMusic());
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, DISINFECT_SPEED, disinfectSettingVo.getDisinfectSpeed() + "");
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, XM_FAN_LEVEL, disinfectSettingVo.getXmFanLevel().toString());
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, ULRAY, disinfectSettingVo.isUlRay() + "");
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, SPRAY, disinfectSettingVo.isSpray() + "");
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, XT, disinfectSettingVo.isXt() + "");
        return ResultUtil.data(true);
    }

    /**
     * 速度等级同步
     * 默认等级为0.3f
     */
    public void syncSpeedLevel() {
        float speed;
        String currentSpeed = RedisUtil.getHash(ROBOT_FUNCTION, CURRENT_SPEED);
        if (StrUtil.isNotBlank(currentSpeed) && currentSpeed.equals(HIGH_SPEED)) {
            speed = 0.8f;
        } else if (StrUtil.isNotBlank(currentSpeed) && currentSpeed.equals(SECONDARY_SPEED)) {
            speed = 0.6f;
        } else {
            speed = 0.3f;
        }
        log.info("同步速度区域");
        _SpeedLevel speedLevel = new _SpeedLevel();
        speedLevel.data = speed;
        rosBridgeService.publish(TopicConstants.SPEED_LEVEL, Message.getMessageType(_SpeedLevel.class), JSONObject.toJSONString(speedLevel));
    }

    /**
     * 获取系统设置信息
     *
     * @return 机器人设置信息
     */
    public RobotSettingVo getRobotSettingInfo() {
        RobotSettingVo robotSettingVo = new RobotSettingVo();
        //获取速度等级
        Object speedLevel = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SPEED_LEVEL);
        String speedLevelStr = speedLevel == null ? "" : speedLevel.toString();
        robotSettingVo.setSpeedLevel(speedLevelStr);
        //获取音量大小
        int volumeSize = 20;
        Object volumeSizeObj = redisTemplate.opsForHash().get(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.VOLUME_SIZE);
        String volumeSizeStr = volumeSizeObj == null ? "" : volumeSizeObj.toString();
        if (StrUtil.isNotEmpty(volumeSizeStr)) {
            volumeSize = Integer.parseInt(volumeSizeStr);
        }
        robotSettingVo.setVolumeSize(volumeSize);
        //背景音乐列表
        StringBuffer backgroundMusics = new StringBuffer();
        robotSettingVo.setBackgroundMusicList(backgroundMusics.toString());
        //获取背景音乐
        Object backgroundMusicObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, BACKGROUND_MUSIC);
        String backgroundMusicStr = backgroundMusicObj == null ? "" : backgroundMusicObj.toString();
        robotSettingVo.setBackgroundMusic(backgroundMusicStr);
        String pinCode = redisTemplate.opsForValue().get(SecurityConstant.OPERATION_CODE);
        robotSettingVo.setPinCode(pinCode);
        return robotSettingVo;
    }

    /**
     * 消毒任务列表
     *
     * @return 消毒任务列表
     */
    public List<DisinfectTaskVo> disinfectTaskList() {
        List<DisinfectTaskVo> disinfectTaskVoList = new ArrayList<>();
        String currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP).toString();
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("type", TaskType.DISINFECT.getType());
        List<Integer> subTypes = new ArrayList<>();
        subTypes.add(DisinfectTaskType.FIXED_POINT_TASK.getType());
        subTypes.add(DisinfectTaskType.LOCAL_TASK.getType());
        queryWrapper.notIn("sub_type", subTypes);
        queryWrapper.eq("map_id", currentMap);
        List<RobotTask> robotTasks = iRobotTaskService.list(queryWrapper);
        if (CollectionUtil.isEmpty(robotTasks)) {
            return disinfectTaskVoList;
        }
        for (RobotTask robotTask : robotTasks) {
            DisinfectTaskVo disinfectTaskVo = new DisinfectTaskVo();

            String robotTaskId = robotTask.getId();
            disinfectTaskVo.setId(robotTaskId);
            RobotMap robotMap = iRobotMapService.getById(robotTask.getMapId());
            disinfectTaskVo.setFloor(robotMap.getFloor());
            disinfectTaskVo.setName(robotTask.getName());
            disinfectTaskVo.setType(robotTask.getSubType());
            disinfectTaskVo.setExecutableEndTime(robotTask.getExecutableEndTime());
            disinfectTaskVo.setExecutableStartTime(robotTask.getExecutableStartTime());
            if (robotTask.getIsFixedTime() != null) {
                disinfectTaskVo.setFixedTime(robotTask.getIsFixedTime() == 1);
            } else {
                disinfectTaskVo.setFixedTime(false);
            }
            disinfectTaskVo.setStartTime(robotTask.getStartTime());
            disinfectTaskVo.setPeriod(robotTask.getPeriod());
            disinfectTaskVo.setLoops(robotTask.getLoops());
            disinfectTaskVo.setWeekdays(robotTask.getWeekdays());
            if (StrUtil.isNotBlank(robotTask.getIsDefault())) {
                disinfectTaskVo.setDefaultTask("0".equals(robotTask.getIsDefault()));
            } else {
                disinfectTaskVo.setDefaultTask(false);
            }
            // 设置消毒任务点位
            List<DisinfectPointVo> disinfectTaskInfo = getDisinfectTaskInfo(robotTaskId);
            disinfectTaskVo.setDisinfectPointVos(disinfectTaskInfo);
            // 设置巡线消毒信息
            List<RobotDisinfect> robotDisinfects = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, robotTaskId));
            if (CollectionUtil.isNotEmpty(robotDisinfects) && robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType())) {
                DisinfectRouteVo disinfectRouteVo = new DisinfectRouteVo();
                disinfectRouteVo.setId(robotDisinfects.get(0).getRobotLocationId());
                disinfectRouteVo.setSpray(robotDisinfects.get(0).getSpray().equals(SUCCESS));
                disinfectRouteVo.setUlray(robotDisinfects.get(0).getUlray().equals(SUCCESS));
                disinfectRouteVo.setXt(robotDisinfects.get(0).getXt().equals(SUCCESS));
                if (ObjectUtil.isNotNull(robotDisinfects.get(0)) && StringUtils.isNotBlank(robotDisinfects.get(0).getIsVideo())) {
                    disinfectRouteVo.setVideo(robotDisinfects.get(0).getIsVideo().equals(SUCCESS));
                }
                disinfectTaskVo.setDisinfectRouteVo(disinfectRouteVo);
            }
            // 设置自定义巡线消毒信息
            List<DefineDisinfectRouteVo> defineDisinfectTaskInfoList = getDefineDisinfectTaskInfo(robotTaskId);
            disinfectTaskVo.setDefineDisinfectRouteVos(defineDisinfectTaskInfoList);
            disinfectTaskVoList.add(disinfectTaskVo);
        }
        return disinfectTaskVoList;
    }

    /**
     * 根据任务id获取消毒任务
     *
     * @return 消毒任务
     */
    public DisinfectTaskVo getDisinfectTask(String id) {
        RobotTask robotTask = iRobotTaskService.getById(id);
        if (robotTask != null) {
            DisinfectTaskVo disinfectTaskVo = new DisinfectTaskVo();
            String robotTaskId = robotTask.getId();
            disinfectTaskVo.setId(robotTaskId);
            RobotMap robotMap = iRobotMapService.getById(robotTask.getMapId());
            disinfectTaskVo.setFloor(robotMap.getFloor());
            disinfectTaskVo.setName(robotTask.getName());
            disinfectTaskVo.setType(robotTask.getSubType());
            Integer isFixedTime = robotTask.getIsFixedTime();
            if (isFixedTime == null) {
                disinfectTaskVo.setFixedTime(false);
            } else {
                disinfectTaskVo.setFixedTime(1 == isFixedTime);
            }
            disinfectTaskVo.setStartTime(robotTask.getStartTime());
            disinfectTaskVo.setPeriod(robotTask.getPeriod());
            disinfectTaskVo.setLoops(robotTask.getLoops());
            disinfectTaskVo.setWeekdays(robotTask.getWeekdays());
            if (StrUtil.isNotBlank(robotTask.getIsDefault())) {
                disinfectTaskVo.setDefaultTask("0".equals(robotTask.getIsDefault()));
            } else {
                disinfectTaskVo.setDefaultTask(false);
            }
            // 设置消毒任务点位
            List<DisinfectPointVo> disinfectTaskInfo = getDisinfectTaskInfo(robotTaskId);
            disinfectTaskVo.setDisinfectPointVos(disinfectTaskInfo);
            // 设置巡线消毒信息（业务逻辑待扩展）
//            disinfectTaskVo.setDisinfectRouteVos(null);
            return disinfectTaskVo;
        } else {
            return null;
        }
    }

    /**
     * 所有消毒任务
     *
     * @return list
     */
    public List<DisinfectTaskVo> allDisinfectTaskList() {
        List<DisinfectTaskVo> disinfectTaskVoList = new ArrayList<>();
        List<RobotTask> robotTasks = iRobotTaskService.list(new LambdaQueryWrapper<RobotTask>().eq(RobotTask::getType, TaskType.DISINFECT.getType()));
        if (CollectionUtil.isEmpty(robotTasks)) {
            return disinfectTaskVoList;
        }
        for (RobotTask robotTask : robotTasks) {
            DisinfectTaskVo disinfectTaskVo = new DisinfectTaskVo();
            disinfectTaskVo.setId(robotTask.getId());
            RobotMap robotMap = iRobotMapService.getById(robotTask.getMapId());
            disinfectTaskVo.setFloor(robotMap.getFloor());
            disinfectTaskVo.setName(robotTask.getName());
            disinfectTaskVo.setFixedTime(robotTask.getIsFixedTime() == 1);
            disinfectTaskVo.setStartTime(robotTask.getStartTime());
            disinfectTaskVo.setPeriod(robotTask.getPeriod());
            disinfectTaskVo.setWeekdays(robotTask.getWeekdays());
            if (StrUtil.isNotBlank(robotTask.getIsDefault())) {
                disinfectTaskVo.setDefaultTask("0".equals(robotTask.getIsDefault()));
            } else {
                disinfectTaskVo.setDefaultTask(false);
            }
            disinfectTaskVoList.add(disinfectTaskVo);
        }
        return disinfectTaskVoList;
    }

    /**
     * 添加或更新消毒任务
     *
     * @param disinfectTaskVo 消毒任务
     * @return result
     */
    public Result<Boolean> saveOrUpdateTask(DisinfectTaskVo disinfectTaskVo) {
        log.info("新建或者更新任务");
        if (StrUtil.isEmpty(disinfectTaskVo.getName())) {
            return new ResultUtil<Boolean>().setErrorMsg("请输入正确的任务名称");
        }
        RobotTask robotTask;
        String id = disinfectTaskVo.getId();
        if (StrUtil.isEmpty(id)) {
            robotTask = addRobotTask(disinfectTaskVo.getName(), 1, TaskType.DISINFECT.getType(), null);
        } else {
            RedisUtil.hdel(ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP, id);
            robotTask = iRobotTaskService.getById(id);
            robotTask.setName(disinfectTaskVo.getName());
        }
        robotTask.setUpdateTime(new Date());
        robotTask.setSubType(disinfectTaskVo.getType());
        robotTask.setIsFixedTime(disinfectTaskVo.isFixedTime() ? 1 : 0);
        robotTask.setExecutableEndTime(disinfectTaskVo.getExecutableEndTime());
        robotTask.setExecutableStartTime(disinfectTaskVo.getExecutableStartTime());
        if (disinfectTaskVo.isFixedTime()) {
            sendVoicePrompt(SceneType.SETTING_FIXED_TIME_TASK, null);
        }
        // 如果是默认任务，一个机器人只能有一个
        if (disinfectTaskVo.isDefaultTask()) {
            Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
            if (o != null && StrUtil.isNotEmpty(o.toString())) {
                List<RobotTask> robotTasks = iRobotTaskService.list(new LambdaQueryWrapper<RobotTask>().eq(RobotTask::getMapId, o.toString())
                        .eq(RobotTask::getIsDefault, "0"));
                if (CollectionUtil.isNotEmpty(robotTasks)) {
                    for (RobotTask robotTask1 : robotTasks) {
                        robotTask1.setIsDefault("1");
                        iRobotTaskService.updateById(robotTask1);
                    }
                }
            }
        }
        robotTask.setIsDefault(disinfectTaskVo.isDefaultTask() ? "0" : "1");
        robotTask.setWeekdays(disinfectTaskVo.getWeekdays());
        Object disinfectCounts = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, DISINFECT_COUNTS);

        if (ObjectUtil.isNotNull(disinfectTaskVo.getLoops())) {
            robotTask.setLoops(disinfectTaskVo.getLoops());
        } else if (null != disinfectCounts && StrUtil.isNotBlank(disinfectCounts.toString())) {
            robotTask.setLoops(Integer.valueOf(disinfectCounts.toString()));
        } else {
            robotTask.setLoops(1);
        }

        String startTime = disinfectTaskVo.getStartTime();
        if (StrUtil.isNotEmpty(startTime)) {
            String[] split = startTime.split(":");
            String hours = split[0];
            String minutes = split[1];
            if (hours.length() < 2) {
                hours = "0".concat(hours);
            }
            if (minutes.length() < 2) {
                minutes = "0".concat(minutes);
            }
            startTime = hours.concat(":").concat(minutes);
        }
        robotTask.setStartTime(startTime);
        robotTask.setPeriod(disinfectTaskVo.getPeriod());
        // 定点消毒任务或巡游消毒任务消毒点位列表
        iRobotDisinfectService.remove(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, robotTask.getId()));
        List<DisinfectPointVo> disinfectPointVos = disinfectTaskVo.getDisinfectPointVos();
        if (CollectionUtil.isNotEmpty(disinfectPointVos)) {
            log.info("任务更新时，更新消毒点信息:{}", JSON.toJSONString(disinfectPointVos));
            for (DisinfectPointVo disinfectPointVo : disinfectPointVos) {
                saveOrUpdateDisinfectPoint(disinfectPointVo, robotTask.getId());
            }
        }
        // 巡线任务
        if (DisinfectTaskType.LINE_PATROL_TASK.getType().equals(disinfectTaskVo.getType())
                && disinfectTaskVo.getDisinfectRouteVo() != null) {
            log.info("保存巡线路线:{}", JSON.toJSONString(disinfectTaskVo.getDisinfectRouteVo()));
            saveRouteDisinfect(robotTask.getId(), disinfectTaskVo.getDisinfectRouteVo());
        }
        // 自定义巡线任务
        if (DisinfectTaskType.DEFINE_LINE_TASK.getType().equals(disinfectTaskVo.getType())
                && CollectionUtil.isNotEmpty(disinfectTaskVo.getDefineDisinfectRouteVos())) {
            log.info("自定义巡线:{}", JSON.toJSONString(disinfectTaskVo.getDefineDisinfectRouteVos()));
            saveDefineRouteDisinfect(robotTask.getId(), disinfectTaskVo.getDefineDisinfectRouteVos());
        }
        iRobotTaskService.saveOrUpdate(robotTask);
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }


    /**
     * 添加消毒任务点
     *
     * @param disinfectPointVo 消毒点
     * @return 机器人消毒任务条目
     */
    public RobotDisinfect saveOrUpdateDisinfectPoint(DisinfectPointVo disinfectPointVo, String taskId) {
        log.info("更新消毒点:{},taskId = {}", disinfectPointVo.getId(), taskId);
        RobotDisinfect robotDisinfect = new RobotDisinfect();
        if (StrUtil.isNotEmpty(disinfectPointVo.getRobotLocationId())) {
            if (StrUtil.isNotEmpty(disinfectPointVo.getId())) {
                robotDisinfect.setId(disinfectPointVo.getId());
                robotDisinfect.setCreateTime(new Date());
            }
            robotDisinfect.setRobotLocationId(disinfectPointVo.getRobotLocationId());
            robotDisinfect.setRobotTaskId(taskId);
            robotDisinfect.setDisinfectTime(disinfectPointVo.getDisinfectTime());
            robotDisinfect.setUlray(disinfectPointVo.isUlray() ? SUCCESS : FAIL);
            robotDisinfect.setSpray(disinfectPointVo.isSpray() ? SUCCESS : FAIL);
            robotDisinfect.setXt(disinfectPointVo.isXt() ? SUCCESS : FAIL);
            robotDisinfect.setUpdateTime(new Date());
            robotDisinfect.setWaitTime(disinfectPointVo.getWaitTime());
            RedisUtil.hRemoveFiled(TASK_INFO + ":" + taskId, TASK_IS_CONTAINS_SPRAY);
            iRobotDisinfectService.saveOrUpdate(robotDisinfect);
        }
        return robotDisinfect;
    }

    /**
     * 添加巡线消毒
     *
     * @param taskId           消毒任务ID
     * @param disinfectRouteVo 消毒路线
     */
    public void saveRouteDisinfect(String taskId, DisinfectRouteVo disinfectRouteVo) {
        RobotDisinfect robotDisinfect = new RobotDisinfect();
        robotDisinfect.setRobotLocationId(disinfectRouteVo.getId());
        robotDisinfect.setRobotTaskId(taskId);
        robotDisinfect.setDisinfectTime(null);
        robotDisinfect.setSpray(disinfectRouteVo.isSpray() ? SUCCESS : FAIL);
        robotDisinfect.setUlray(disinfectRouteVo.isUlray() ? SUCCESS : FAIL);
        robotDisinfect.setXt(disinfectRouteVo.isXt() ? SUCCESS : FAIL);
        robotDisinfect.setIsVideo(ObjectUtil.isNotNull(disinfectRouteVo) && disinfectRouteVo.isVideo() ? SUCCESS : FAIL);
        robotDisinfect.setDisinfectTime(0);
        robotDisinfect.setCreateTime(new Date());
        robotDisinfect.setUpdateTime(new Date());
        RedisUtil.hRemoveFiled(TASK_INFO + ":" + taskId, TASK_IS_CONTAINS_SPRAY);
        iRobotDisinfectService.saveOrUpdate(robotDisinfect);
    }

    /**
     * 新增自定义巡线任务
     *
     * @param taskId                  任务ID
     * @param defineDisinfectRouteVos 自定义巡线任务
     */
    public void saveDefineRouteDisinfect(String taskId, List<DefineDisinfectRouteVo> defineDisinfectRouteVos) {
        for (DefineDisinfectRouteVo defineDisinfectRouteVo : defineDisinfectRouteVos) {
            RobotDisinfect robotDisinfect = new RobotDisinfect();
            RobotRouteItem robotRouteItem = new RobotRouteItem();
            RobotDisinfect robotDisinfectTemp = iRobotDisinfectService.getOne(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotLocationId, defineDisinfectRouteVo.getRouteId()));
            if (robotDisinfectTemp != null) {
                robotDisinfect.setId(robotDisinfectTemp.getId());
            }
            robotDisinfect.setRobotLocationId(defineDisinfectRouteVo.getRouteId());
            robotDisinfect.setRobotTaskId(taskId);
            Double area = defineDisinfectRouteVo.getArea();
            Double height = defineDisinfectRouteVo.getHeight();
            Double disinfectTime = (area * height * disinfectStandard / disinfectEfficiency / 1000) * 3600;
            robotDisinfect.setDisinfectTime(disinfectTime.intValue());
            robotDisinfect.setSpray(defineDisinfectRouteVo.isSpray() ? SUCCESS : FAIL);
            robotDisinfect.setUlray(defineDisinfectRouteVo.isUlray() ? SUCCESS : FAIL);
            robotDisinfect.setXt(defineDisinfectRouteVo.isXt() ? SUCCESS : FAIL);
            robotDisinfect.setCreateTime(new Date());
            robotDisinfect.setUpdateTime(new Date());
            iRobotDisinfectService.saveOrUpdate(robotDisinfect);
            RedisUtil.hRemoveFiled(TASK_INFO + ":" + taskId, TASK_IS_CONTAINS_SPRAY);
            RobotRouteItem robotRouteItemTemp = iRobotRouteItemService.getOne(new LambdaQueryWrapper<RobotRouteItem>().eq(RobotRouteItem::getRouteId, defineDisinfectRouteVo.getRouteId()));
            if (robotRouteItemTemp != null) {
                robotRouteItem.setId(robotRouteItemTemp.getId());
            }
            robotRouteItem.setRouteId(defineDisinfectRouteVo.getRouteId());
            robotRouteItem.setArea(defineDisinfectRouteVo.getArea());
            robotRouteItem.setHeight(defineDisinfectRouteVo.getHeight());
            iRobotRouteItemService.saveOrUpdate(robotRouteItem);
        }

    }

    /**
     * 根据任务Id获取常规消毒任务详情
     *
     * @param robotTaskId 机器人任务ID
     * @return 常规消毒任务详情
     */
    public List<DisinfectPointVo> getDisinfectTaskInfo(String robotTaskId) {
        List<DisinfectPointVo> disinfectPointInfoList = new ArrayList<>();
        RobotTask robotTask = iRobotTaskService.getById(robotTaskId);
        String id = robotTask.getId();
        if (robotTask != null && robotTask.getSubType() != null && robotTask.getSubType().equals(DisinfectTaskType.ROUTINE_TASK.getType())) {
            List<RobotDisinfect> robotDisinfectList = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>()
                    .eq(RobotDisinfect::getRobotTaskId, id));
            //查看已经消毒过的点位和未消毒过的点位
            List<String> allToDoList = redisTemplate.opsForList().range(TO_DO_LIST, 0, -1);
            List<String> robotDisinfectIds = new ArrayList<>();
            for (String item : allToDoList) {
                String[] itemArr = item.split("#");
                String positionId = itemArr[1];
                RobotLocation robotLocation = iRobotLocationService.getOne(new LambdaQueryWrapper<RobotLocation>()
                        .eq(RobotLocation::getPositionId, positionId));
                if (robotLocation != null) {
                    List<RobotDisinfect> robotDisinfects = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>()
                            .eq(RobotDisinfect::getRobotLocationId, robotLocation.getId()));
                    if (CollectionUtil.isNotEmpty(robotDisinfects)) {
                        for (RobotDisinfect robotDisinfect : robotDisinfects) {
                            robotDisinfectIds.add(robotDisinfect.getId());
                        }
                    }
                }
            }
            if (CollectionUtil.isNotEmpty(robotDisinfectList)) {
                for (RobotDisinfect robotDisinfect : robotDisinfectList) {
                    DisinfectPointVo disinfectPointVo = new DisinfectPointVo();
                    disinfectPointVo.setId(robotDisinfect.getId());
                    String robotLocationId = robotDisinfect.getRobotLocationId();
                    disinfectPointVo.setRobotLocationId(robotLocationId);
                    if (robotTask.getType().equals(TaskType.DISINFECT.getType())
                            && robotTask.getSubType().equals(DisinfectTaskType.LINE_PATROL_TASK.getType())) {
                        RobotRoute robotRoute = iRobotRouteService.getById(robotLocationId);
                        disinfectPointVo.setLocationInfo(robotRoute.getLocationInfo());
                        disinfectPointVo.setLocationCode(robotRoute.getLocationCode());
                    } else {
                        RobotLocation robotLocation = iRobotLocationService.getById(robotLocationId);
                        if (robotLocation != null) {
                            disinfectPointVo.setLocationInfo(robotLocation.getLocationInfo());
                            disinfectPointVo.setLocationCode(robotLocation.getLocationCode());
                        }
                    }
                    disinfectPointVo.setUlray(robotDisinfect.getUlray().equals(SUCCESS));
                    disinfectPointVo.setSpray(robotDisinfect.getSpray().equals(SUCCESS));
                    disinfectPointVo.setXt(robotDisinfect.getXt().equals(SUCCESS));
                    disinfectPointVo.setDisinfectTime(robotDisinfect.getDisinfectTime());
                    disinfectPointVo.setWaitTime(robotDisinfect.getWaitTime());
                    boolean contains = robotDisinfectIds.contains(robotDisinfect.getId());
                    if (CollectionUtil.isNotEmpty(robotDisinfectIds) && !contains) {
                        disinfectPointVo.setFinish(true);
                    } else {
                        disinfectPointVo.setFinish(false);
                    }
                    disinfectPointInfoList.add(disinfectPointVo);
                }
            }
        }
        return disinfectPointInfoList;
    }

    /**
     * 获取自定义巡线任务列表
     *
     * @param robotTaskId 机器人任务ID
     * @return 自定义巡线任务列表
     */
    public List<DefineDisinfectRouteVo> getDefineDisinfectTaskInfo(String robotTaskId) {
        List<DefineDisinfectRouteVo> disinfectPointInfoList = new ArrayList<>();
        RobotTask robotTask = iRobotTaskService.getById(robotTaskId);
        String id = robotTask.getId();
        if (robotTask.getSubType().equals(DisinfectTaskType.DEFINE_LINE_TASK.getType())) {
            List<RobotDisinfect> robotDisinfectList = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, id));
            if (CollectionUtil.isNotEmpty(robotDisinfectList)) {
                for (RobotDisinfect robotDisinfect : robotDisinfectList) {
                    DefineDisinfectRouteVo defineDisinfectRouteVo = new DefineDisinfectRouteVo();
                    defineDisinfectRouteVo.setSpray(robotDisinfect.getSpray().equals(SUCCESS));
                    defineDisinfectRouteVo.setXt(robotDisinfect.getXt().equals(SUCCESS));
                    defineDisinfectRouteVo.setUlray(robotDisinfect.getUlray().equals(SUCCESS));
                    defineDisinfectRouteVo.setRouteId(robotDisinfect.getRobotLocationId());
                    RobotRoute robotRoute = iRobotRouteService.getById(robotDisinfect.getRobotLocationId());
                    if (robotRoute != null) {
                        defineDisinfectRouteVo.setLocationInfo(robotRoute.getLocationInfo());
                        defineDisinfectRouteVo.setLocationCode(robotRoute.getLocationCode());
                        RobotRouteItem robotRouteItem = iRobotRouteItemService.getOne(new LambdaQueryWrapper<RobotRouteItem>().eq(RobotRouteItem::getRouteId, robotDisinfect.getRobotLocationId()));
                        if (robotRouteItem != null) {
                            defineDisinfectRouteVo.setArea(robotRouteItem.getArea());
                            defineDisinfectRouteVo.setHeight(robotRouteItem.getHeight());
                        }
                    }
                    disinfectPointInfoList.add(defineDisinfectRouteVo);
                }
            }
        }
        return disinfectPointInfoList;
    }


    /**
     * 判断机器人是否处于空闲状态
     *
     * @return true:空闲 / false：不空闲
     */
    public boolean isIdle() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            Boolean hasRouteToDoList = jedis.exists(ROUTE_TO_DO_LIST);
            Boolean hasToDoList = jedis.exists(TO_DO_LIST);
            String autoChargingStatus = jedis.hget(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
            Boolean isAutoCharging = !StrUtil.isBlank(autoChargingStatus) && autoChargingStatus.equals(AutoChargingStatus.DOING.getValue().toString());
            // 新增一个建图过程，如果在建图过程，就不是空闲
            if (!hasRouteToDoList && !hasToDoList && !isAutoCharging && !CREATE_MAP_STATUE) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return false;
    }

    /**
     * 是否为手动充电
     *
     * @return true:手动 / false:自动
     */
    public boolean isManualCharge() {
        Jedis jedis = null;
        boolean res = false;
        try {
            jedis = RedisUtil.getJedis();
            String autoDockStateStr = jedis.get(TOPIC + "::" + TopicConstants.AUTO_DOCK_STATE);
            if (StrUtil.isNotBlank(autoDockStateStr)) {
                _AutoDockState autoDockState = JSON.parseObject(autoDockStateStr, _AutoDockState.class);
                short type = autoDockState.type;
                if (type == AutoDockType.MANUAL_DOCK.getType()) { //手动充电
                    res = true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return res;
    }

    /**
     * 广告屏的控制
     *
     * @param operation false:关掉广告屏 /  true:开启广告屏
     */
    public void adControl(boolean operation) {
        _Bool bool = new _Bool();
        bool.data = operation;
        rosBridgeService.publish(TopicConstants.AD_CONTROL, Message.getMessageType(_Bool.class), JSON.toJSONString(bool));
    }

    /**
     * 初始化数据
     */
    public void initData() {
        // 清空所有的位置区域
        iRobotAreasService.remove(new LambdaQueryWrapper<>());
        // 清空所有充电记录
        iRobotChargingRecordService.remove(new LambdaQueryWrapper<>());
        // 清空所有的检测记录
        iRobotCheckHistoryRecordService.remove(new LambdaQueryWrapper<>());
        // 清空所有的检测记录条目
        iRobotCheckHistoryItemService.remove(new LambdaQueryWrapper<>());
        // 清空所有的消毒液加液记录
        iRobotDisinfectantRecordService.remove(new LambdaQueryWrapper<>());
        // 清空所有的消毒标定记录
        iRobotDisinfectService.remove(new LambdaQueryWrapper<>());
        // 清空所有的消毒详情记录
        iRobotDisinfectTaskDetailService.remove(new LambdaQueryWrapper<>());
        // 清空所有的位置区域
        iRobotLocationService.remove(new LambdaQueryWrapper<>());
        // 清空所有的地图
        iRobotMapService.remove(new LambdaQueryWrapper<>());
        FileUtil.clean(mapPath);
        /*FileUtil.del(mapPath);
        FileUtil.mkdir(mapPath);*/
        // 清空所有的标点
        iRobotPositionService.remove(new LambdaQueryWrapper<>());
        // 清空所有世界坐标
        iRobotWorldPositionService.remove(new LambdaQueryWrapper<>());
        // 清空所有的路径
        iRobotRouteService.remove(new LambdaQueryWrapper<>());
        // 清空所有的开关机记录
        iRobotSwitchRecordService.remove(new LambdaQueryWrapper<>());
        // 清空所有的任务
        iRobotTaskService.remove(new LambdaQueryWrapper<>());
        // 清空任务条目
        iRobotTaskItemService.remove(new LambdaQueryWrapper<>());
        // 清空任务记录
        iRobotTaskRecordService.remove(new LambdaQueryWrapper<>());

        // 清空云端数据
        String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + yxYunInitData;
        try {
            String serialNumber = RDes.getSerialNumber();
            JSONObject variables = new JSONObject();
            variables.put("serialNumber", serialNumber);
            RestUtil.get(url, variables);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 通知ros进行关机
     */
    public void notifyRosShutdown() {
        //电机矢能,防止运行过程中机器人失控
//        motorControl(false);
        _Uint8 uint8 = new _Uint8();
        uint8.data = 5;
//      点阵表情休眠；
        publishUtil(0);
        rosBridgeService.publish(TopicConstants.SYSTEM_CHARGE, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, PRE_SHUTDOWN, "true");
        log.warn("已经通知ROS系统准备进行关机操作......");
    }

    /**
     * 通知ros进行重启
     */
    public void notifyRosRestart() {
        log.warn("在重启之前处理休眠状态......");
        handleAutoAandleLidar();
        iRobotMotorService.motorControl(true);
        _Uint8 uint8 = new _Uint8();
        uint8.data = 6;
        iRobotSwitchRecordService.handleRobotSwitchRecord(0, RobotSwitchType.AUTO.getType());
        rosBridgeService.publish(TopicConstants.SYSTEM_CHARGE, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
        log.warn("已经通知ROS系统准备进行重启操作......");
    }

    /**
     * 环形灯的状态请求处理
     * 老的
     *
     * @param ringLightDefine 环形灯状态定义
     */
    public void sendRingLightPromptOld(RingLightDefine ringLightDefine) {
        Jedis jedis = null;
        _Uint8 uint8 = new _Uint8();
        try {
            jedis = RedisUtil.getJedis();
            String o = jedis.hget(ROBOT_SYS_INFO, RING_LIGHT_STATE);
            if (StrUtil.isNotEmpty(o)) {
                // 获取当前环形灯状态等级
                JSONObject jsonObject = JSONObject.parseObject(o);
                Integer level = Integer.valueOf(jsonObject.get("level").toString());
                Integer value = Integer.valueOf(jsonObject.get("value").toString());
                if (((level <= ringLightDefine.getLevel() && !value.equals(ringLightDefine.getValue())))) { // 当已经为空闲等级 或灯光等级大于或等于已有的等级
                    jedis.hset(ROBOT_SYS_INFO, RING_LIGHT_STATE, ringLightDefine.toString());
                } else {
                    return; // 低等级不发送环形灯状态处理
                }
            } else {
                jedis.hset(ROBOT_SYS_INFO, RING_LIGHT_STATE, ringLightDefine.toString());
            }
            Integer value = ringLightDefine.getValue();
            uint8.data = Short.valueOf(value.toString());
            rosBridgeService.publish(TopicConstants.RING_LIGHT_CTRL, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 环形灯的状态请求处理
     *
     * @param ringLightDefine 环形灯状态定义
     */
    public void sendRingLightPrompt(RingLightDefine ringLightDefine) {
        if(ringLightDefine.equals(LAST_LIGHT_SEND)){
            return;
        }
        if (ringLightDefine.equals(CURRENT_RING_LIGHT)) {
            return;
        }
        if (ObjectUtil.isNull(CURRENT_RING_LIGHT)
                || CURRENT_RING_LIGHT.getLevel() <= ringLightDefine.getLevel()
                && !CURRENT_RING_LIGHT.getValue().equals(ringLightDefine.getValue())) {
            CURRENT_RING_LIGHT = ringLightDefine;
        } else {
            return;
        }

        _Uint8 uint8 = new _Uint8();
        try {
            Integer value = ringLightDefine.getValue();
            uint8.data = Short.parseShort(value.toString());
            rosBridgeService.publish(TopicConstants.RING_LIGHT_CTRL, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
            RedisUtil.hset(ROBOT_SYS_INFO, RING_LIGHT_STATE, ringLightDefine.toString());
            LAST_LIGHT_SEND = ringLightDefine;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 音量控制
     *
     * @param volumeSize 大小
     */
    public void volumeCtrl(Integer volumeSize) {
        _Uint8 uint8 = new _Uint8();
        uint8.data = Short.parseShort(volumeSize.toString());
        redisTemplate.opsForHash().put(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.VOLUME_SIZE, volumeSize.toString());
        rosBridgeService.publish(TopicConstants.SOUNDS_CTRL, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
    }

    /**
     * 地图自检
     *
     * @return true:成功 / false:失败
     */
    public boolean mapCheck() {
        boolean res = false;
        String systemCheckStr = redisTemplate.opsForValue().get(TOPIC + "::" + SYSTEM_CHECK);
        if (StrUtil.isNotBlank(systemCheckStr)) {
            _SystemCheck systemCheck = JSONObject.parseObject(systemCheckStr, _SystemCheck.class);
            if (systemCheck.navigation_nodes.length == 0) {
                res = true;
            }
        }
        return res;
    }

    /**
     * 是否在线
     *
     * @return true/false
     */
    public boolean isOnline() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String rosStatus = jedis.hget(ROBOT_SYS_INFO, ROS_STATUS);
            String rosStatusStr = rosStatus == null ? "" : rosStatus;
            //设备状态
            if (rosStatusStr.equals(SUCCESS)) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return false;
    }

    /**
     * 急停是否触发
     *
     * @return true/false
     */
    public boolean isStopping() {
        Jedis jedis = null;
        boolean result = false;
        try {
            jedis = RedisUtil.getJedis();
            String emergencyButton = jedis.get(TOPIC + "::" + EMERGENCY_BUTTON);
            if (StrUtil.isNotEmpty(emergencyButton)) {
                _Bool bool = JSON.parseObject(emergencyButton, _Bool.class);
                result = bool.data;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return result;
    }

    /**
     * 获取机器人磁盘信息
     *
     * @return
     */
    public Double getDiskInformation() {
        String s = RedisUtil.getValue(MONITOR_INFO);
        Double percent = 0.0;
        if (StrUtil.isNotEmpty(s)) {
            SysMonitor sysMonitor = JSONObject.parseObject(s, SysMonitor.class);
            //总空间
            Double totalSpace = Double.valueOf(sysMonitor.getTotalSpace().split("G")[0]);
            //可用空间
            Double usableSpace = Double.valueOf(sysMonitor.getUsableSpace().split("G")[0]);
            percent = usableSpace / totalSpace;
        }
        return percent;
    }

    /**
     * 获取机器人相关信息
     *
     * @return 机器人信息
     */
    public RobotInfoVo getRobotInfo() {
        RobotInfoVo robotInfoVo = new RobotInfoVo();
        Double diskInformation = getDiskInformation();
        boolean res0 = isIdle();
        boolean res1 = isCollision();
        boolean res2 = iRobotStatusService.isSprayLiquidLevelWarning();
        boolean res3 = isStopping();
        boolean res4 = isCharging();
        boolean res5 = isOnline();
        boolean res6 = mapCheck();
        robotInfoVo.setIsIdle(res0);
        robotInfoVo.setIsCollision(res1);
        robotInfoVo.setIsSprayWarning(res2);
        robotInfoVo.setIsStop(res3);
        robotInfoVo.setIsCharging(res4);
        robotInfoVo.setIsMapping(mapping);
        robotInfoVo.setHasMap(res6);
        robotInfoVo.setWorkingOperationStatus(iRobotStatusService.getOperationStatus().getValue());
        String serialNumber = RDes.getSerialNumber();
        String deviceTypeCode = RDes.decrypt(serialNumber);
//      防止robotBaseInfo.json文件未更改导致机器人编号错误
        RobotBaseInfoConstant.robotNumber = deviceTypeCode;
        robotInfoVo.setDeviceTypeCode(deviceTypeCode);
        Float batteryPercentage = iRobotStatusService.getBatteryPercentage();
        robotInfoVo.setBattery(batteryPercentage);
        robotInfoVo.setStatus(res5);
        //设置雷达休眠状态：true:雷达已休眠，false:雷达已开启
        robotInfoVo.setLidarStatue(ControlStatusConstants.LIDAR_DORMANCY_STATUE);
        //机器人磁盘信息
        robotInfoVo.setDiskInformation(diskInformation);
        //机器人电池使用率
        robotInfoVo.setBatteryPercent(RobotBaseInfoConstant.BATTERY_CYCLE_PERCENT);
        //U3pro脉冲灯使用剩余时间
        robotInfoVo.setPulseSurplusHours(RobotBaseInfoConstant.PULSE_LIGHT_SURPLUS_TIME);
        //机器休眠功能状态
        String isOpen = RedisUtil.getHash(ROBOT_FUNCTION, FUNCTION_RADAR_SLEEP);
        if (Boolean.FALSE.toString().equals(isOpen)) {
            robotInfoVo.setSleepState(false);
        } else {
            robotInfoVo.setSleepState(true);
        }
        return robotInfoVo;
    }

    /**
     * 清空地图
     */
    public void clearMap() {
        _EmptyReq emptyReq = new _EmptyReq();
        rosBridgeService.publish(CLEAR_COSTMAPS, Message.getMessageType(_EmptyRep.class), JSON.toJSONString(emptyReq));
        log.info("清空地图成功");
    }

    /**
     * 创建默认巡线任务
     *
     * @return 创建默认巡线任务
     */
    public RobotTask createDefaultDisinfectLineTask() {
        String taskName = DisinfectTaskType.LINE_PATROL_TASK.getLabel() + TaskType.DISINFECT.getValue();
        RobotTask robotTask = addRobotTask(taskName, 1, TaskType.DISINFECT.getType(), DisinfectTaskType.LINE_PATROL_TASK.getType());
        robotTask.setIsDefault(SUCCESS);
        iRobotTaskService.updateById(robotTask);
        return robotTask;
    }

    /**
     * 开始执行门禁任务
     *
     * @param entranceGuardId 门禁区域ID
     */
    public void startEntranceGuardTask(String entranceGuardId) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String entrancePositionId = jedis.get(ENTRANCE_POSITION_ID);
            // 发送门禁逻辑
            RobotEntranceGuard entranceGuard = iRobotEntranceGuardService.getById(entranceGuardId);
            RobotTask robotTask = addRobotTask(TaskType.ENTRANCE_GUARD.getValue(), 1, TaskType.ENTRANCE_GUARD.getType(), null);
            Map<String, Object> positionIds = new LinkedHashMap<>();
            if (entranceGuard != null && StrUtil.isNotBlank(entrancePositionId)) {
                // 1、2 代表编号，无意义
                if (entranceGuard.getPositionAId().equals(entrancePositionId)) {
                    positionIds.put("1", entranceGuard.getPositionBId());
                }
                if (entranceGuard.getPositionBId().equals(entrancePositionId)) {
                    positionIds.put("1", entranceGuard.getPositionAId());
                }
                positionIds.put("2", entrancePositionId);
                addPointToRedis(positionIds, robotTask.getId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 获取机器人位置
     *
     * @return 机器人位置
     */
    public _PoseStamped getRobotPose() {
        // TODO 后面替换，位置信息放在positionService
        _PoseStamped pose = null;
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String robotPose = jedis.get(TOPIC + "::" + TopicConstants.ROBOT_POSE_STAMPED);
            if (StrUtil.isNotBlank(robotPose)) {
                pose = JSONObject.parseObject(robotPose, _PoseStamped.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return pose;
    }

    /**
     * 获取系统监控信息
     *
     * @return 系统监控信息
     */
    public SysMonitor getMonitorInfo() {
        SysMonitor sysMonitor = null;
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String s = jedis.get(MONITOR_INFO);
            if (StrUtil.isNotBlank(s)) {
                sysMonitor = JSONObject.parseObject(s, SysMonitor.class);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return sysMonitor;
    }

    /**
     * 处理深度异常
     */
    public void handleDepthError() {
        log.info("深度未正常启动");
//        sendVoicePrompt();
    }

    /**
     * 休眠模式控制
     *
     * @param data true/false
     * @return true:成功 / false:失败
     */
    public synchronized boolean lowPowerCtrl(boolean data) {
        _LowPowerReq lowPowerReq = new _LowPowerReq();
        lowPowerReq.data = data;
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, LOW_POWER_STATUS);
        if (o != null && o.toString().equals(SUCCESS) && data) {
            return false;
        }
        if (o != null && o.toString().equals(FAIL) && !data) {
            return false;
        }
        String s = rosBridgeService.callService(LOW_POWER, Message.getMessageType(_LowPowerReq.class), JSONObject.toJSONString(lowPowerReq));
        if (StrUtil.isNotBlank(s)) {
            _LowPowerRep lowPowerRep = JSONObject.parseObject(s, _LowPowerRep.class);
            if (lowPowerRep.success) {
                if (data) {
                    log.info("休眠模式开启成功");
                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, LOW_POWER_STATUS, SUCCESS);
                } else {
                    log.info("休眠模式关闭成功");
                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, LOW_POWER_STATUS, FAIL);
                }
            }
        }
        return false;
    }

    /**
     * 系统自检
     *
     * @param content 检测内容
     * @return true:正常 / false:异常
     */
    public boolean systemCheck(String content) {
        _SystemCheck systemCheck = JSONObject.parseObject(content, _SystemCheck.class);
        //如果雷达没有处于休眠状态检测到雷达报错则返回false
        if (systemCheck.lidar.indexOf("E") != -1 && ControlStatusConstants.LIDAR_DORMANCY_STATUE == false) {
            log.info("系统自检不通过：lidar:" + systemCheck.lidar);
            if (RobotStatusScheduler.systemCheckMessage) {
//                雷达会自动重启导致频繁发送
//                sendMessageUtil.sendShortMessage("雷达故障");
//                rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.LIDAY_ERROR.getType());
                RobotStatusScheduler.systemCheckMessage = false;
            }
            return false;
        }
        if (systemCheck.depth_camera.indexOf("E") != -1 || systemCheck.depth_camera == null) {
            handleDepthError();
            log.info("系统自检不通过：depth_camera:" + systemCheck.depth_camera);
            if (RobotStatusScheduler.systemCheckMessage) {
//                深度相机会自动重启导致频繁发送
//                sendMessageUtil.sendShortMessage("深度异常");
//                rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.DEEP_ERROR.getType());
                RobotStatusScheduler.systemCheckMessage = false;
            }
            return false;
        }
        if (systemCheck.motor.indexOf("E") != -1) {
            return false;
        }
        if (systemCheck.imu.indexOf("E") != -1) {
            log.info("系统自检不通过：imu:" + systemCheck.imu);
            return false;
        }
        if (systemCheck.motor.indexOf("E") != -1) {
            log.info("系统自检不通过：motor:" + systemCheck.motor);
            return false;
        }
        if (systemCheck.bms.indexOf("E") != -1) {
            log.info("系统自检不通过：bms:" + systemCheck.bms);
            return false;
        }
        if (systemCheck.collision.indexOf("E") != -1) {
            log.info("系统自检不通过：collision:" + systemCheck.collision);
            return false;
        }
        if (systemCheck.stop_btn.indexOf("E") != -1) {
            log.info("系统自检不通过：stop_btn:" + systemCheck.stop_btn);
            return false;
        }
        if (systemCheck.auto_charge.indexOf("E") != -1) {
            log.info("系统自检不通过：auto_charge:" + systemCheck.auto_charge);
            return false;
        }
        if (systemCheck.singlechip_bottom.indexOf("E") != -1) {
            log.info("系统自检不通过：singlechip_bottom:" + systemCheck.singlechip_bottom);
            return false;
        }
        if (systemCheck.turn_light.indexOf("E") != -1) {
            log.info("系统自检不通过：turn_light:" + systemCheck.turn_light);
            return false;
        }
        if (systemCheck.power_key.indexOf("E") != -1) {
            log.info("系统自检不通过：power_key:" + systemCheck.power_key);
            return false;
        }
        if (systemCheck.system_charge.indexOf("E") != -1) {
            log.info("系统自检不通过：system_charge:" + systemCheck.system_charge);
            return false;
        }
        if (systemCheck.eye.indexOf("E") != -1) {
            log.info("系统自检不通过：eye:" + systemCheck.eye);
            return false;
        }
        if (systemCheck.ad.indexOf("E") != -1) {
            log.info("系统自检不通过：ad:" + systemCheck.ad);
            return false;
        }
        if (systemCheck.touch.indexOf("E") != -1) {
            log.info("系统自检不通过：touch:" + systemCheck.touch);
            return false;
        }
        if (systemCheck.door.indexOf("E") != -1) {
            log.info("系统自检不通过：door:" + systemCheck.door);
            return false;
        }
        if (systemCheck.led_light.indexOf("E") != -1) {
            log.info("系统自检不通过：led_light:" + systemCheck.led_light);
            return false;
        }
        if (systemCheck.infra_light.indexOf("E") != -1) {
            log.info("系统自检不通过：infra_light:" + systemCheck.infra_light);
            return false;
        }
        if (systemCheck.singlechip_top.indexOf("E") != -1) {
            log.info("系统自检不通过：singlechip_top:" + systemCheck.singlechip_top);
            return false;
        }
        if (systemCheck.pulse.indexOf("E") != -1) {
            log.info("系统自检不通过：pulse:" + systemCheck.pulse);
            return false;
        }
        if (systemCheck.ulray.indexOf("E") != -1) {
            log.info("系统自检不通过：ulray:" + systemCheck.ulray);
            return false;
        }
        if (systemCheck.spray.indexOf("E") != -1) {
            log.info("系统自检不通过：spray:" + systemCheck.spray);
            return false;
        }
        if (systemCheck.fan.indexOf("E") != -1) {
            log.info("系统自检不通过：fan:" + systemCheck.fan);
            return false;
        }
        if (systemCheck.pump.indexOf("E") != -1) {
            log.info("系统自检不通过：pump:" + systemCheck.pump);
            return false;
        }
        if (systemCheck.wiquid.indexOf("E") != -1) {
            log.info("系统自检不通过：wiquid:" + systemCheck.wiquid);
            return false;
        }
        if (systemCheck.doctor.indexOf("E") != -1) {
            log.info(systemCheck.doctor);
            return false;
        }
        if (systemCheck.roll_led.indexOf("E") != -1) {
            log.info("系统自检不通过：roll_led:" + systemCheck.roll_led);
            return false;
        }
        if (systemCheck.sensor_nodes.toString().indexOf("E") != -1) {
            log.info("系统自检不通过：sensor_nodes:" + systemCheck.sensor_nodes.toString());
            return false;
        }
        if (systemCheck.task_nodes.toString().indexOf("E") != -1) {
            log.info("系统自检不通过：task_nodes:" + systemCheck.task_nodes.toString());
            return false;
        }
        return true;
    }

    /**
     * 点阵表情话题发布工具类
     *
     * @param date
     */
    public void publishUtil(Integer date) {
        String s = String.valueOf(date);
        _Uint8 uint8 = new _Uint8();
        uint8.data = Short.valueOf(s);
        rosBridgeService.publishExpression(TopicConstants.EXPRESSION_CONTROL, Message.getMessageType(_Expression.class), JSONObject.toJSONString(uint8));
    }

    /**
     * 获取当前任务
     */
    public RobotTask getCurrentTask() {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        RobotTask robotTask = null;
        if (o != null) {
            robotTask = iRobotTaskService.getById(o.toString());
        }
        return robotTask;
    }

    /**
     * 修改机器人基础信息JSON文件
     *
     * @param newVersion
     * @throws JsonProcessingException
     */
    public void updateRobotBaseInfoJSON(String newVersion, String type) throws JsonProcessingException {
        //在更新时将新的版本写入robotBaseInfo.json文件中；
        cn.hutool.json.JSON json = JSONUtil.readJSON(new File(robotBaseInfoPath), StandardCharsets.UTF_8);
        RobotBaseInfoVo baseInfoVo = JSONObject.parseObject(json.toString(), RobotBaseInfoVo.class);
        if ("ros".equals(type)) {
            baseInfoVo.setRosVersion(newVersion);
        } else if ("web".equals(type)) {
            baseInfoVo.setWebVersion(newVersion);
        }
        String jsonObject = new ObjectMapper().writeValueAsString(baseInfoVo);
        FileUtil.writeBytes(jsonObject.getBytes(StandardCharsets.UTF_8), robotBaseInfoPath);
    }

    /**
     * 根据属性值获取MotorAlarmCode枚举对象
     *
     * @param key 属性值
     * @return
     */
    public MotorAlarmCode getEnumObjByKey(Integer key) {
        Optional<MotorAlarmCode> any = Arrays.stream(MotorAlarmCode.class.getEnumConstants())
//                根据value属性获取枚举对象
                .filter(e -> e.getType().equals(key)).findAny();
        if (any.isPresent()) {
            return any.get();
        }
        return null;
    }

    /**
     * 对于休眠状态进行处理
     * 任务控制：开始任务之前
     * 消毒任务开始之前
     * 开始见图之前
     */
    public Boolean handleAutoAandleLidar() {
        boolean flag = false;
        if (lidarControlUtil.returnDepthStatue().equals(SLEEP) || lidarControlUtil.returnLiadrStatue().equals(SLEEP) || lidarControlUtil.returnUnderlyingStatue() || ControlStatusConstants.LIDAR_DORMANCY_STATUE) {
            try {
                log.info("小乐正处于休眠状态，正在唤醒设备，请稍等三十秒哦");
//                sendVoicePrompt(SceneType.SLEEP_NOTIFY, null);
                flag = lidarControlUtil.handleLidar(true);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            flag = true;
        }
        return flag;
    }

    public void startFallBeforeTask() {
        //开始任务前开启防跌落
        log.warn("开始任务前开启防跌落!");
        String fall = RedisUtil.getHash(ROBOT_FUNCTION, FALL_DOWN_STATE);
        if (StrUtil.isNotBlank(fall) && !Boolean.valueOf(fall)) {
            log.warn("云平台已关闭防跌落功能，不再自动打开");
        } else {
            iRobotAreasService.fallControl(true);
        }
    }


}
