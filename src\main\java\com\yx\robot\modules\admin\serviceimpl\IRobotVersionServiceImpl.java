package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yx.robot.common.enums.BucketType;
import com.yx.robot.common.enums.VersionType;
import com.yx.robot.common.utils.MinioUtils;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dto.RobotBaseInfoVo;
import com.yx.robot.modules.admin.dto.YxRobotVersionDto;
import com.yx.robot.modules.admin.service.IRobotVersionService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import io.minio.MinioClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.yx.robot.common.constant.RobotRedisConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/15 17:02
 */
@Slf4j
@Service
public class IRobotVersionServiceImpl implements IRobotVersionService {

    @Autowired
    private RosWebService rosWebService;

    @Value("${robot.file.install_file}")
    private String rootPath;

    /**
     * jar 安装文件
     */
    @Value("${robot.file.install_jar_path}")
    private String robotJarPath;

    /**
     * 文件备份位置
     */
    @Value("${robot.file.bak}")
    private String bakPath;

    /**
     * 机器人控制系统 更新脚本
     */
    @Value("${robot.file.install_robot_system_shell}")
    private String installShell;

    /**
     * 机器人控制系统 更新脚本
     */
    @Value("${robot.file.install_robot_ros_shell}")
    private String installRosShell;



    private final ExecutorService executorService = Executors.newFixedThreadPool(2);

    /**
     * 版本更新版本
     * <p>
     * 下载需要更新的版本
     * 将版本拷贝指定位置
     * 执行更新命令
     *
     * @param robotVersionDto 版本信息
     * @return true
     */
    @Override
    public boolean updateVersion(YxRobotVersionDto robotVersionDto) {
        if (VersionType.CONTROL.getType().equals(robotVersionDto.getType())) {
            if (UPDATE_STATUS_RUNNING.equals(RedisUtil.getHash(ROBOT_INFO_MAP, IS_UPDATE_ROBOT_INFO_MAP_WEB_VERSION))) {
                throw new IllegalStateException("程序正在更新中");
            }
        }
        if (VersionType.ROS.getType().equals(robotVersionDto.getType())) {
            if (UPDATE_STATUS_RUNNING.equals(RedisUtil.getHash(ROBOT_INFO_MAP, IS_UPDATE_ROBOT_INFO_MAP_ROS_VERSION))) {
                throw new IllegalStateException("程序正在更新中");
            }
        }
        executorService.execute(() -> {
            String path = download(robotVersionDto);
            if (ObjectUtil.isNull(path)) {
                log.info("更新文件下载失败");
            }
            boolean flg;
            if (VersionType.CONTROL.getType().equals(robotVersionDto.getType())) {
                flg = updateRobotSystem(path, robotVersionDto.getCurrentVersionCode(), robotVersionDto.getVersionCode());
                if (!flg) {
                    log.warn("软件更新失败");
                }
            }
            if (VersionType.ROS.getType().equals(robotVersionDto.getType())) {
                flg = updateRos(path, robotVersionDto.getCurrentVersionCode(), robotVersionDto.getVersionCode());
                if (!flg) {
                    log.warn("ROS更新失败");
                }
            }
        });

        return true;
    }

    /**
     * 将需要更新的版本下载本地
     *
     * @param robotVersionDto 版本信息
     * @return 文件下载路劲
     */
    private String download(YxRobotVersionDto robotVersionDto) {
        String bucket = null;
        String localPath;
        if (VersionType.CONTROL.getType().equals(robotVersionDto.getType())) {
            RedisUtil.hset(ROBOT_INFO_MAP, IS_UPDATE_ROBOT_INFO_MAP_WEB_VERSION, UPDATE_STATUS_RUNNING);
            bucket = BucketType.ROBOT.getBucket();
        }
        if (VersionType.ROS.getType().equals(robotVersionDto.getType())) {
            RedisUtil.hset(ROBOT_INFO_MAP, IS_UPDATE_ROBOT_INFO_MAP_ROS_VERSION, UPDATE_STATUS_RUNNING);
            bucket = BucketType.ROS.getBucket();
        }
        if (ObjectUtil.isNotNull(bucket)) {
            localPath = rootPath + File.separator + bucket + File.separator + robotVersionDto.getFilePath();
            boolean isSuccess = MinioUtils.download(bucket, robotVersionDto.getFilePath(), localPath);
            if (isSuccess) {
                return localPath.replace("//", "/");
            }
        }
        return null;
    }

    /**
     * 更新系统
     * 在系统指定路劲上创建一个脚本 startUpdate.sh，并且设置可以执行的权限。
     * 操作系统每个一分钟执行一次，判断脚本是否存在，是则执行脚本
     *
     * @param localPath      本地文件路径
     * @param currentVersion 当前版本
     * @param nextVersion    更新版本
     * @return true:成功， false:失败
     */
    private boolean updateRobotSystem(String localPath, String currentVersion, String nextVersion) {

        // 备份信息
        bakRobotSystem(currentVersion);
        // 将文件拷贝相应路径
        copyRobotSystem(localPath);

        String suffix = FileUtil.getSuffix(localPath);

        String fullPath = FileUtil.getParent(rootPath, 1) + File.separator + "startUpdate.sh";
        FileUtil.file(fullPath);
        String cmd = "#!/bin/bash \n exec " + installShell + " " + currentVersion + " " + nextVersion + " " + suffix + "\n";
        FileUtil.writeBytes(cmd.getBytes(StandardCharsets.UTF_8), fullPath);
        try {
            Runtime.getRuntime().exec("chmod 777 " + fullPath);
            RedisUtil.hset(ROBOT_INFO_MAP, IS_UPDATE_ROBOT_INFO_MAP_WEB_VERSION, UPDATE_STATUS_FINISH);
            //在更新时将新的版本写入robotBaseInfo.json文件中；
            rosWebService.updateRobotBaseInfoJSON(nextVersion,"web");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return true;
    }

    private void copyRobotSystem(String localPath) {
        FileUtil.copy(localPath, robotJarPath, true);
    }

    /**
     * 备份文件
     *
     * @param currentVersion 当前版本信息
     */
    private void bakRobotSystem(String currentVersion) {
        String targetPath = bakPath + File.separator + currentVersion;
        if (!FileUtil.exist(targetPath)) {
            FileUtil.mkParentDirs(targetPath);
        }
        FileUtil.copy(rootPath, targetPath, true);
    }

    /**
     * 更新ros 系统
     *
     * @param localPath      当前文件路径
     * @param currentVersion 当前版本
     * @param nextVersion    更新版本
     * @return true
     */
    private boolean updateRos(String localPath, String currentVersion, String nextVersion) {
        String fullPath = FileUtil.getParent(rootPath, 1) + File.separator + "startUpdateRos.sh";
        FileUtil.file(fullPath);
        String suffix = FileUtil.getSuffix(localPath);
        String cmd = "#!/bin/bash \n exec " + installRosShell + " " + currentVersion + " " + nextVersion + " " + suffix + " " + localPath + "\n ";
        FileUtil.writeBytes(cmd.getBytes(StandardCharsets.UTF_8), fullPath);
        try {
            Process process = Runtime.getRuntime().exec("chmod 777 " + fullPath);
            RedisUtil.hset(ROBOT_INFO_MAP, IS_UPDATE_ROBOT_INFO_MAP_ROS_VERSION, UPDATE_STATUS_FINISH);
            process.getOutputStream();
            BufferedReader bufferedReader = new BufferedReader(
                    new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8));
            String line;
            StringBuilder result = new StringBuilder();
            while ((line = bufferedReader.readLine()) != null) {
                result.append(line).append("\n");
            }
            log.info("更新执行结果:\n {}", result);
            log.info("ros更新完成");
            //在更新时将新的版本写入robotBaseInfo.json文件中；
            rosWebService.updateRobotBaseInfoJSON(nextVersion,"ros");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return true;
    }
}
