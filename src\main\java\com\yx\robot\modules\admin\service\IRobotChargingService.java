package com.yx.robot.modules.admin.service;

import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.message.SaveDockPositionMsg;
import com.yx.robot.modules.admin.message._RobotDockRep;
import com.yx.robot.modules.admin.message._UdriveAutoDockState;

/**
 * 机器人充电服务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/19 14:04
 */
public interface IRobotChargingService {

    /**
     * 保存充电位置，并返回充电桩位置点，和充电导航点
     *
     * @return 保存充电桩位置发牛结果
     */
    SaveDockPositionMsg saveDockPosition();

    /**
     * 对接充电桩
     * 2：对接充电桩 3：脱离充电桩 4：取消充电
     *
     * @param option 操作类型
     * @return 接充电桩返回结果
     */
    _RobotDockRep autoDockControl(Integer option);

    /**
     * 开始对接对接充电桩
     * 2：对接充电桩 3：脱离充电桩 4：取消充电
     *
     * @return 接充电桩返回结果
     */
    _RobotDockRep startDock();

    /**
     * 脱离充电桩
     * 2：对接充电桩 3：脱离充电桩 4：取消充电
     *
     * @return 接充电桩返回结果
     */
    _RobotDockRep outDock();

    /**
     * 取消对接充电桩
     *
     * @return 接充电桩返回结果
     */
    _RobotDockRep cancelDock();


    /**
     * 获取对接充电桩状态
     *
     * @return 充电桩对接状态
     */
    _UdriveAutoDockState getAutoDockState();

}
