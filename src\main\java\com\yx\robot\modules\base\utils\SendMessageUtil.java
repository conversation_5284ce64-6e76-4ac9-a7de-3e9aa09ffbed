package com.yx.robot.modules.base.utils;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.aliyuncs.CommonRequest;
import com.aliyuncs.CommonResponse;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.IAcsClient;
import com.aliyuncs.http.MethodType;
import com.aliyuncs.profile.DefaultProfile;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.modules.admin.entity.RobotUser;
import com.yx.robot.modules.admin.service.IRobotUserService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.RobotInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/9/15
 * description：
 */
@Slf4j
@Component("SendMessageUtil")
public class SendMessageUtil {

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotUserService iRobotUserService;

    /**
     * 发送短信
     *
     * @return
     */
    public boolean sendShortMessage(String failure) {
//      获取手机号,同一时间段只能存在一个可用电
        QueryWrapper<RobotUser> wrapper = new QueryWrapper<>();
        wrapper.eq("del_flag", 0).eq("status", 1);
        RobotUser robotUser = iRobotUserService.getOne(wrapper);
        if (ObjectUtil.isNull(robotUser)) {
            return false;
        }
        String telephoneNumber = robotUser.getMobile();
//      获取机器人类型加编号
        RobotInfoVo robotInfo = rosWebService.getRobotInfo();
        String robotNumber = robotInfo.getDeviceTypeCode();
        if(StrUtil.isBlank(robotNumber)){
            robotNumber = RobotBaseInfoConstant.robotNumber;
        }
        log.info("robotNumber:"+robotNumber);
//      阿里云ID和密钥
        DefaultProfile profile = DefaultProfile.getProfile("default", "LTAI5tCb9PTPzoxFwMVCWh3d", "******************************");
        IAcsClient client = new DefaultAcsClient(profile);
        CommonRequest request = new CommonRequest();
//      构建请求，不能修改
        request.setSysMethod(MethodType.POST);
        request.setSysDomain("dysmsapi.aliyuncs.com");
        request.setSysVersion("2017-05-25");
        request.setSysAction("SendSms");
//      自定义参数
//        telephoneNumber="18336281979";
        //手机号
        request.putQueryParameter("PhoneNumbers", telephoneNumber);
        //申请阿里云 签名名称
        request.putQueryParameter("SignName", "驭领科技");
        //申请阿里云 模板code
        request.putQueryParameter("TemplateCode", "SMS_248815453");
        Map<String, Object> map = new HashMap<>();
        map.put("code", robotNumber + failure);
        //短信验证码
        request.putQueryParameter("TemplateParam", JSONObject.toJSONString(map));
        try {
            CommonResponse response = client.getCommonResponse(request);
            log.info("短信发送结果："+JSONObject.toJSONString(map));
            return response.getHttpResponse().isSuccess();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

}
