package com.yx.robot.modules.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.RobotLocationInfoDto;
import com.yx.robot.modules.admin.entity.RobotLocation;
import com.yx.robot.modules.admin.service.IRobotLocationService;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import com.yx.robot.modules.admin.vo.RobotLocationVo;
import com.yx.robot.modules.admin.vo.RobotPositionVo;
import com.yx.robot.modules.admin.vo.RobotSubPositionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人标注位置管理接口")
@RequestMapping("/yx/api-v1/robotLocation")
@Transactional
public class RobotLocationController {

    @Autowired
    private IRobotLocationService iRobotLocationService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotLocation> get(@PathVariable String id) {
        RobotLocation robotLocation = iRobotLocationService.getById(id);
        return new ResultUtil<RobotLocation>().setData(robotLocation);
    }

    @RequestMapping(value = "/getAllRobotLocationByType")
    @ApiOperation(value = "根据类型获取全部设备标注位置")
    @ApiImplicitParam(name = "type", value = "点位类型:1:原点,2:充电桩，3：定位点，5：位置区域，6：门禁内，7：门禁外，8：电梯内，9：电梯外",
            dataType = "int", paramType = "query", allowableValues = "1,2,3,5,6,7,8,9")
    public Result<List<RobotLocationInfoDto>> getAllRobotLocationByType(@RequestParam Integer type) {
        List<RobotLocationInfoDto> robotLocationInfoDtos = iRobotLocationService.getAllRobotLocationByType(type);
        return new ResultUtil<List<RobotLocationInfoDto>>().setData(robotLocationInfoDtos);
    }

    @RequestMapping(value = "/saveOrUpdateRobotSubLocation")
    @ApiOperation(value = "添加或更新机器人子位置区域")
    public Result<Boolean> addRobotSubLocation(@RequestBody RobotSubPositionVo robotSubPositionVo) {
        RobotPositionVo robotPositionVo = new RobotPositionVo(robotSubPositionVo);
        Result<Boolean> result = null;
        robotPositionVo.setId(robotSubPositionVo.getId());
        if (StrUtil.isEmpty(robotPositionVo.getId())) {
            result = iRobotPositionService.add(robotPositionVo);
        } else {
            result = iRobotPositionService.update(robotPositionVo);
        }
        return result;
    }

    @RequestMapping(value = "/getAllCruiseRobotLocation")
    @ApiOperation(value = "获取巡检模式标注位置")
    public Result<List<RobotLocationVo>> getAllCruiseRobotLocation() {
        List<RobotLocationVo> robotLocationVos = iRobotLocationService.getAllCruiseRobotLocation();
        return new ResultUtil<List<RobotLocationVo>>().setData(robotLocationVos);
    }


    @RequestMapping(value = "/getAllVoiceContent")
    @ApiOperation(value = "获取所有的声音播报内容")
    public Result<List<String>> getAllVoiceContent() {
        List<String> allVoiceContent = iRobotLocationService.getAllVoiceContent();
        return new ResultUtil<List<String>>().setData(allVoiceContent);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotLocation>> getByPage(@ModelAttribute PageVo page, @RequestParam String mapId) {
        QueryWrapper<RobotLocation> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(page.getName())) {
            queryWrapper.like("name", page.getName());
        }
        IPage<RobotLocation> data = iRobotLocationService.page(PageUtil.initMpPage(page), queryWrapper);
        return new ResultUtil<IPage<RobotLocation>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotLocation> saveOrUpdate(@ModelAttribute RobotLocation robotLocation) {
        if (iRobotLocationService.saveOrUpdate(robotLocation)) {
            return new ResultUtil<RobotLocation>().setData(robotLocation);
        }
        return new ResultUtil<RobotLocation>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {

        for (String id : ids) {
            iRobotLocationService.removeById(id);
        }
        return new ResultUtil<>().setSuccessMsg("批量通过id删除数据成功");
    }

    @RequestMapping(value = "/clearAll", method = RequestMethod.DELETE)
    @ApiOperation(value = "清空全部数据")
    public Result<Object> clearAll() {
        iRobotLocationService.remove(new QueryWrapper<>());
        return new ResultUtil<Object>().setSuccessMsg("清空数据成功");
    }

}
