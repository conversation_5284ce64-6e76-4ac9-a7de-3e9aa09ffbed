package com.yx.robot.modules.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotAreas;
import com.yx.robot.modules.admin.service.IRobotAreasService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.modules.admin.vo.RobotAreaVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.RosWebConstants.FUNCTION_RADAR_SLEEP;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人区域管理接口")
@RequestMapping("/yx/api-v1/robotAreas")
@Transactional
public class RobotAreasController {

    @Autowired
    private IRobotAreasService iRobotAreasService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotAreas> get(@PathVariable String id) {
        RobotAreas robotSpeed = iRobotAreasService.getById(id);
        return new ResultUtil<RobotAreas>().setData(robotSpeed);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotAreas>> getAll(@RequestParam String mapId, @RequestParam Integer type) {
        List<RobotAreas> list = iRobotAreasService.list(new LambdaQueryWrapper<RobotAreas>()
                .eq(RobotAreas::getMapId, mapId).eq(RobotAreas::getType, type));
        return new ResultUtil<List<RobotAreas>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotAreas>> getByPage(@ModelAttribute PageVo page,
                                               @RequestParam String mapId,
                                               @RequestParam Integer type) {
        QueryWrapper<RobotAreas> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", mapId);
        queryWrapper.eq("type", type);
        IPage<RobotAreas> data = iRobotAreasService.page(PageUtil.initMpPage(page), queryWrapper);
        return new ResultUtil<IPage<RobotAreas>>().setData(data);
    }

    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @ApiOperation(value = "添加禁行区域数据")
    public Result<Boolean> add(@RequestBody RobotAreaVo robotAreaVo) {
        boolean res = iRobotAreasService.add(robotAreaVo);
        iRobotAreasService.initRobotAreas(robotAreaVo.getType());
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "添加成功");
        }
        return new ResultUtil<Boolean>().setErrorMsg("添加失败");
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "更新禁行区域数据")
    public Result<Boolean> update(@RequestBody RobotAreas robotSpeed) {
        boolean result = iRobotAreasService.updateById(robotSpeed);
        return new ResultUtil<Boolean>().setData(result);
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除",notes = "1,2,3,4")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {
        RobotAreas robotAreas = null;
        for (String id : ids) {
            robotAreas = iRobotAreasService.getById(id);
            iRobotAreasService.removeById(id);
        }
        // 同步区域
        if (null != robotAreas) {
            iRobotAreasService.initRobotAreas(robotAreas.getType());
        }
        return new ResultUtil<>().setSuccessMsg("批量通过id删除数据成功");
    }

    @RequestMapping(value = "/clearAll", method = RequestMethod.DELETE)
    @ApiOperation(value = "清空全部数据")
    public Result<Object> clearAll() {
        Object currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (currentMap != null && StrUtil.isNotBlank(currentMap.toString())) {
            List<RobotAreas> robotSpeeds = iRobotAreasService.list(new LambdaQueryWrapper<RobotAreas>().eq(RobotAreas::getMapId, currentMap.toString()));
            if (null != robotSpeeds && !robotSpeeds.isEmpty()) {
                for (RobotAreas robotSpeed : robotSpeeds) {
                    iRobotAreasService.removeById(robotSpeed.getId());
                }
            }
            iRobotAreasService.initRobotAreas(null);
        }

        return new ResultUtil<>().setSuccessMsg("清空数据成功");
    }

    @RequestMapping(value = "/syncRobotAreas", method = RequestMethod.GET)
    @ApiOperation(value = "同步机器人区域", notes = "1:速度区域 2:禁行区域 3:膨胀区域")
    @ApiImplicitParam(name = "type", value = "区域类型",dataType = "int",paramType = "query", allowableValues = "1,2,3")
    public Result<Boolean> syncSpeedAreas(@RequestParam Integer type) {
        boolean result = iRobotAreasService.syncRobotAreas(type);
        if (result) {
            return new ResultUtil<Boolean>().setData(true, "同步机器人区域成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("同步机器人区域失败");
        }
    }

    @RequestMapping(value = "/fallControl", method = RequestMethod.GET)
    @ApiOperation(value = "机器人防跌落开启或关闭",notes = "true:开启,false:关闭")
    public Result<Boolean> fallControl(@RequestParam Boolean ctrl) {
        boolean result = iRobotAreasService.fallControl(ctrl);
        // 设置防跌落开关状态
        RedisUtil.hset(ROBOT_FUNCTION, FALL_DOWN_STATE,ctrl.toString());
        if (result && ctrl) {
            return new ResultUtil<Boolean>().setData(true, "防跌落开启成功");
        } else if(result && !ctrl){
            return new ResultUtil<Boolean>().setData(true, "防跌落关闭成功");
        }else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/fallState", method = RequestMethod.GET)
    @ApiOperation(value = "获取机器人防跌落开启或关闭状态" , notes = "true:开启状态,false:关闭状态")
    public Result<Boolean> fallState() {
        boolean result = iRobotAreasService.fallState();
        return new ResultUtil<Boolean>().setData(result);
    }
}
