package com.yx.robot.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yx.robot.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "t_robot_device_info")
@TableName("t_robot_device_info")
@ApiModel(value = "设备信息")
public class RobotDeviceInfo extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("设备名称")
    private String devName;

    @ApiModelProperty("设备类型")
    private Integer type;

    @ApiModelProperty("设备子类型")
    private Integer subType;

    @ApiModelProperty("设备型号")
    private String modelNumber;

    @ApiModelProperty("控制方法")
    private Integer controlMethod;

    @ApiModelProperty("地址")
    private String controlAddress;

    @ApiModelProperty("参数")
    private String queryParams;

}