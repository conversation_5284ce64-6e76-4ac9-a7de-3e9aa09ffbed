package ${entity.entityPackage};

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "${entity.tableName}")
@TableName("${entity.tableName}")
@ApiModel(value = "${entity.description}")
public class ${entity.className} extends BaseEntity {

    private static final long serialVersionUID = 1L;

}