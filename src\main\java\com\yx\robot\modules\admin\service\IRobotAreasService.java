package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotAreas;
import com.yx.robot.modules.admin.vo.RobotAreaVo;

/**
 * 机器人区域接口
 * <AUTHOR>
 */
public interface IRobotAreasService extends IService<RobotAreas> {

    /**
     * 添加机器人区域
     * @param robotAreaVo
     * @return
     */
    boolean add(RobotAreaVo robotAreaVo);

    /**
     * 同步机器人区域
     * @return
     */
    boolean syncRobotAreas(Integer type);

    /**
     * 初始化机器人区域
     */
    void initRobotAreas(Integer type);

    /**
     * 开启或关闭防跌落
     * @param ctrl
     * @return
     */
    Boolean fallControl(Boolean ctrl);

    /**
     * 获取防跌落开启或关闭状态
     * @return True: 防跌落处于打开状态 ，False：防跌落处于关闭状态
     */
    Boolean fallState();
}