package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.RobotTaskItemDto;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.vo.RobotTaskVo;

import java.util.List;

/**
 * 机器人列表接口
 *
 * <AUTHOR>
 */
public interface IRobotTaskService extends IService<RobotTask> {
    /**
     * 添加任务
     *
     * @param robotTaskVo 任务实体
     * @return true/false
     */
    Result<Boolean> add(RobotTaskVo robotTaskVo);

    /**
     * 删除任务
     *
     * @param id 任务ID
     * @return true/false
     */
    boolean delete(String id);

    /**
     * 编辑任务
     *
     * @param robotTaskVo 编辑任务实体
     * @return true/false
     */
    Result<Boolean> update(RobotTaskVo robotTaskVo);

    /**
     * 查看任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    List<RobotTaskItemDto> taskDetail(String taskId);

    /**
     * 获取默认任务id
     *
     * @return 任务ID
     */
    String getDefaultTaskId();

    /**
     * 获取消毒循环次数
     *
     * @return 次数
     */
    Integer getDisinfectCounts();

    /**
     * 设置循环消毒次数
     *
     * @param counts 次数
     */
    void setDisinfectCounts(Integer counts);

    /**
     * 当前任务是否为消毒任务
     * 当前任务为空时，false
     * 任务暂停时，false
     *
     * @return true:是消毒任务 / false:不是消毒任务
     */
    boolean isDisinfectCurrentTaskType();

    /**
     * 获取当前任务类型
     * null 当前没有任务
     *
     * @return type
     */
    Integer getCurrentTaskType();

    /**
     * 判断任务是否在可执行时间段内
     *
     * @param taskId 任务ID
     * @return true:可执行/false:不可执行
     */
    boolean isExecutableTime(String taskId);

    /**
     * 判断任务是否在可执行时间段内
     *
     * @param robotTask 任务信息
     * @return true:可执行/false:不可执行
     */
    boolean isExecutableTime(RobotTask robotTask);

    /**
     * 判断机器人当前任务是否可执行
     * 如果不可以执行，则取消任务，返回充电
     *
     * @param taskId 任务ID
     */
    void cancelNotInExecutableTimeTask(String taskId);

    /**
     * 判断当前机器人是否有巡线任务
     *
     * @return true(有)/false(没有)
     */
    boolean isNeedLineTask();
}