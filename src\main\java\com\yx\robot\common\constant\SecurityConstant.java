package com.yx.robot.common.constant;

/**
 * <AUTHOR>
 */
public interface SecurityConstant {

    /**
     * token分割
     */
    String TOKEN_SPLIT = "Bearer ";

    /**
     * JWT签名加密key
     */
    String JWT_SIGN_KEY = "yx";

    /**
     * token参数头
     */
    String HEADER = "accessToken";

    /**
     * 权限参数头
     */
    String AUTHORITIES = "authorities";

    /**
     * 用户选择JWT保存时间参数头
     */
    String SAVE_LOGIN = "saveLogin";

    /**
     * 交互token前缀key
     */
    String TOKEN_PRE = "YX_TOKEN_PRE:";

    /**
     * 用户token前缀key 单点登录使用
     */
    String USER_TOKEN = "YX_USER_TOKEN:";

    /**
     * 密码初始值(操作，取物)
     */
    String CODE_VAL= "123456";

    /**
     * 操作密码验证
     */
    String OPERATION_CODE = "operation_code";

    /**
     * 取物密码验证
     */
    String FETCH_CODE = "fetch_code";

    /**
     * 系统是否启动成功
     */
    String SYS_IS_UP = "sys_is_up";
}
