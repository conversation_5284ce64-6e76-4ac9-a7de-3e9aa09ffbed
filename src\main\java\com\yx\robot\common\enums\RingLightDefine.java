package com.yx.robot.common.enums;

import com.alibaba.fastjson.JSONObject;

/**
 * 环形灯状态定义
 *
 * <AUTHOR>
 * @date 2020/11/5
 */
public enum RingLightDefine {

    /**
     * 0, "空闲状态", "蓝色灯", 1
     */
    IDLE_STATE(0, "空闲状态", "蓝色灯", 1),

    /**
     * 1,"充电状态","绿色呼吸灯", 1
     */
    CHARGING_STATE(1, "充电状态", "绿色呼吸灯", 1),

    /**
     * 2,"满电状态", "绿色灯",1
     */
    FULL_BATTERY_STATE(2, "满电状态", "绿色灯", 1),

    /**
     * 3, "private状态", "蓝色呼吸灯", 1
     */
    MAPPING_STATE(3, "建图状态", "蓝色呼吸灯", 1),

    /**
     * 4, "导航状态", "蓝色流水灯", 1
     */
    NAVING_STATE(4, "导航状态", "蓝色流水灯", 1),

    /**
     * 5, "消毒状态", "红色流水灯", 1
     */
    DISINFECT_STATE(5, "消毒状态", "红色流水灯", 1),

    /**
     * 6, "故障状态", "红色闪烁", 2
     */
    ERROR_STATE(6, "故障状态", "红色闪烁", 2),

    /**
     * 7, "预警状态", "黄色闪烁", 1
     */
    WARNING_STATE(7, "预警状态", "黄色闪烁", 1),

    /**
     * 8, "左转状态", "左转流水灯", 1
     */
    LEFT_TURN_STATE(8, "左转状态", "左转流水灯", 1),

    /**
     * 9, "右转状态", "右转流水灯", 1
     */
    RIGHT_TURN_STATE(9, "右转状态", "右转流水灯", 1),

    /**
     * 10, "系统上电", "蓝色快闪", 1
     */
    SYS_POWER(10, "系统上电", "蓝色快闪", 1),

    /**
     * 11, "ROS启动", "蓝色慢闪", 1
     */
    ROS_SUCCESS(11, "ROS启动", "蓝色慢闪", 1),

    /**
     * 12, "系统启动失败", "红色常亮", 1
     */
    SYS_FAIL(12, "系统启动失败", "红色常亮", 1);

    /**
     * 指令值
     */
    private final Integer value;

    /**
     * 状态场景
     */
    private final String state;

    /**
     * 灯光定义
     */
    private final String light;

    /**
     * 优先等级
     */
    private final Integer level;

    RingLightDefine(Integer value, String state, String light, Integer level) {
        this.value = value;
        this.state = state;
        this.light = light;
        this.level = level;
    }

    public Integer getValue() {
        return value;
    }

    public String getState() {
        return state;
    }

    public String getLight() {
        return light;
    }

    public Integer getLevel() {
        return level;
    }


    /**
     * Returns the name of this enum constant, as contained in the
     * declaration.  This method may be overridden, though it typically
     * isn't necessary or desirable.  An enum type should override this
     * method when a more "programmer-friendly" string form exists.
     *
     * @return the name of this enum constant
     */
    @Override
    public String toString() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("value", this.value);
        jsonObject.put("state", this.state);
        jsonObject.put("light", this.light);
        jsonObject.put("level", this.level);
        return jsonObject.toString();
    }
}
