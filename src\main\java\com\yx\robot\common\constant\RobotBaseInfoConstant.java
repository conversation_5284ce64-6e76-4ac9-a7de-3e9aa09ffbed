package com.yx.robot.common.constant;

/**
 * 机器人基础信息，在系统启动是赋值
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 13:36
 */
public class RobotBaseInfoConstant {

    /**
     * 机器人型号
     */
    public static String type;

    /**
     * 机器人名称
     */
    public static String name;

    /**
     * 机器人编号，唯一标识，不可重复
     */
    public static String robotNumber;

    /**
     * 机器人序列号，规则未定
     */
    public static String serialNumber;

    /**
     * 生产日期
     */
    public static String productDate;

    /**
     * 版本号
     */
    public static String version;

    /**
     * ros 版本号
     */
    public static String rosVersion;

    /**
     * ros 版本号
     */
    public static String webVersion;

    /**
     * 发布日期
     */
    public static String robotWebPublishDate;

    /**
     * 电池充电次数百分比
     */
    public static String BATTERY_CYCLE_PERCENT = "0%";

    /**
     * U3pro紫外灯使用时长倒计时
     * 剩余时间
     */
    public static String PULSE_LIGHT_SURPLUS_TIME = "";
}
