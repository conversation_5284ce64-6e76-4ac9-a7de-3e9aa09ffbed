package com.yx.robot.modules.admin.electrl.enums;

/**
 * 乘电梯阶段
 *
 * <AUTHOR>
 * @date 2021/12/11
 */
public enum StepEnum {

    /**
     * "1","等待到达出发楼层"
     */
    FIRST("1", "等待到达出发楼层"),

    /**
     * "2","到达出发楼层，可以进梯"
     */
    SECOND("2", "到达出发楼层，可以进梯"),

    /**
     * "3"," 等待到达目的楼层"
     */
    THIRD("3", " 等待到达目的楼层"),

    /**
     * "4","到达目的楼层，可以出梯"
     */
    FOURTH("4", "到达目的楼层，可以出梯");

    private final String step;

    private final String label;

    StepEnum(String step, String label) {
        this.step = step;
        this.label = label;
    }

    public String getStep() {
        return step;
    }

    public String getLabel() {
        return label;
    }
}
