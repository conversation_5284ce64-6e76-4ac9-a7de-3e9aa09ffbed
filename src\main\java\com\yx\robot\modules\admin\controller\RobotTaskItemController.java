package com.yx.robot.modules.admin.controller;

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotTaskItem;
import com.yx.robot.modules.admin.service.IRobotTaskItemService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人任务条目管理接口")
@RequestMapping("/yx/api-v1/robotTaskItem")
@Transactional
public class RobotTaskItemController {

    @Autowired
    private IRobotTaskItemService iRobotTaskItemService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotTaskItem> get(@PathVariable String id){

        RobotTaskItem robotTaskItem = iRobotTaskItemService.getById(id);
        return new ResultUtil<RobotTaskItem>().setData(robotTaskItem);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotTaskItem>> getAll(){

        List<RobotTaskItem> list = iRobotTaskItemService.list();
        return new ResultUtil<List<RobotTaskItem>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotTaskItem>> getByPage(@ModelAttribute PageVo page){

        IPage<RobotTaskItem> data = iRobotTaskItemService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotTaskItem>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotTaskItem> saveOrUpdate(@ModelAttribute RobotTaskItem robotTaskItem){

        if(iRobotTaskItemService.saveOrUpdate(robotTaskItem)){
            return new ResultUtil<RobotTaskItem>().setData(robotTaskItem);
        }
        return new ResultUtil<RobotTaskItem>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotTaskItemService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
