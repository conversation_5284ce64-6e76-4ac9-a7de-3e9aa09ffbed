package com.yx.robot.modules.admin.sync.service;

/**
 * 数据同步接口
 * <AUTHOR>
 * @date 2020/05/17
 */
public interface DataSyncService {

    /**
     * 同步设备信息数据
     */
    void syncDeviceInfoData();

    /**
     * 同步任务记录数据
     */
    void syncTaskRecordData();

    /**
     * 同步充电记录
     */
    void syncChargingRecordData();

    /**
     * 同步开关机记录
     */
    void syncSwitchRecordData();

    /**
     * 同步消毒任务详情
     */
    void syncDisinfectTaskDetailData();

    /**
     * 同步机器人设备数据到云端
     * @param mapIds
     * @return
     */
    boolean syncRobotDeviceDataToYun(String mapIds);

    /**
     * 同步机器人设备数据到本地
     * @param mapIds
     * @return
     */
    boolean syncRobotDeviceDataToLocal(String mapIds);

    /**
     * 同步订单状态
     */
    void syncOrdersStateDataToYun();

    /**
     * 处理设备本体数据(保证和其它设备数据id不一样)
     */
    void handleDeviceSqlData(String param1, String param2);
}
