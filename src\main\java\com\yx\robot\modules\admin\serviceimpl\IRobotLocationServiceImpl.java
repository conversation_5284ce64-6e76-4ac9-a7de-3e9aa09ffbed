package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.utils.SecurityUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotLocationMapper;
import com.yx.robot.modules.admin.dto.RobotLocationInfoDto;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.entity.RobotLocation;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import com.yx.robot.modules.admin.service.IRobotLocationService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.vo.RobotLocationVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.yx.robot.common.constant.RosWebConstants.CURRENT_MAP;
import static com.yx.robot.common.constant.RosWebConstants.ROBOT_SYS_INFO;

/**
 * 机器人位置信息接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotLocationServiceImpl extends ServiceImpl<RobotLocationMapper, RobotLocation> implements IRobotLocationService {

    @Autowired
    private RobotLocationMapper robotLocationMapper;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;


    @Override
    public List<RobotLocationInfoDto> getAllRobotLocationByType(Integer type) {
        List<RobotLocationInfoDto> robotLocationInfoDtos = new ArrayList<>();
        try {
            Object mapId = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO,CURRENT_MAP);
            if(mapId == null) {
                return robotLocationInfoDtos;
            }
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("map_id",mapId.toString());
            if(null != type) {
                queryWrapper.eq("type",type);
            }
            queryWrapper.isNull("parent_id");
            List<RobotPosition> robotPositionList = iRobotPositionService.list(queryWrapper);
            if(CollectionUtil.isEmpty(robotPositionList)) {
                return robotLocationInfoDtos;
            }
            queryWrapper = new QueryWrapper();
            queryWrapper.in("position_id",robotPositionList.stream().map(RobotPosition::getId).collect(Collectors.toList()));
            queryWrapper.orderByAsc("sort_order");
            List<RobotLocation> robotLocationList = robotLocationMapper.selectList(queryWrapper);
            if (null != robotLocationList && !robotLocationList.isEmpty()) {
                Set<String> locationInfos = new LinkedHashSet<>();
                for (RobotLocation robotLocation : robotLocationList) {
                    locationInfos.add(robotLocation.getLocationInfo());
                }
                for (String locationInfo : locationInfos) {
                    RobotLocationInfoDto robotLocationInfoDto = new RobotLocationInfoDto();
                    robotLocationInfoDto.setLocationInfo(locationInfo);
                    List<Map<String, Object>> locationCodes = new ArrayList<>();
                    for (RobotLocation robotLocation : robotLocationList) {
                        if (robotLocation.getLocationInfo().equals(locationInfo)) {
                            Map<String, Object> locationCode = new HashMap<>();
                            locationCode.put("id", robotLocation.getId());
                            locationCode.put("locationCode", robotLocation.getLocationCode());
                            locationCodes.add(locationCode);
                        }
                    }
                    robotLocationInfoDto.setLocationCodes(locationCodes);
                    robotLocationInfoDtos.add(robotLocationInfoDto);
                }
            }
        } catch (Exception e) {
            log.error("获取机器人标注位置异常");
            e.printStackTrace();
        }
        return robotLocationInfoDtos;
    }

    /**
     * 获取机器人巡检标注位置
     */
    @Override
    public List<RobotLocationVo> getAllCruiseRobotLocation() {
        List<RobotLocationVo> robotLocationVos = new ArrayList<>();
        try {
            Object mapId = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO,CURRENT_MAP);
            if(mapId == null) {
                return robotLocationVos;
            }
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("map_id",mapId.toString());
            List<RobotPosition> robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                    .eq(RobotPosition::getMapId,mapId.toString())
                    .eq(RobotPosition::getType, RobotPositionType.CRUISE_POSITION.getType()));
            if(CollectionUtil.isEmpty(robotPositionList)) {
                return robotLocationVos;
            }
            queryWrapper = new QueryWrapper();
            queryWrapper.in("position_id",robotPositionList.stream().map(RobotPosition::getId).collect(Collectors.toList()));
            queryWrapper.orderByAsc("sort_order");
            List<RobotLocation> robotLocationList = robotLocationMapper.selectList(queryWrapper);
            for (RobotLocation robotLocation : robotLocationList) {
                RobotLocationVo robotLocationVo = new RobotLocationVo(robotLocation);
                robotLocationVos.add(robotLocationVo);
            }
        } catch (Exception e) {
            log.error("获取及爱人巡检标注位置异常");
        }
        return robotLocationVos;
    }

    @Override
    public boolean isRepeat(String locationInfo, String locationCode) {
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("location_info",locationInfo);
        queryWrapper.eq("location_code",locationCode);
        List<RobotLocation> robotLocationList = this.list(queryWrapper);
        if(robotLocationList.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 获取所有声音播报文件
     *
     * @return
     */
    @Override
    public List<String> getAllVoiceContent() {
        List<String> allVoiceContent = new ArrayList<>();
        Object mapId = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO,CURRENT_MAP);
        if(mapId == null) {
            return allVoiceContent;
        }
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("map_id",mapId.toString());
        List<RobotPosition> robotPositionList = iRobotPositionService.list(queryWrapper);
        if(CollectionUtil.isEmpty(robotPositionList)) {
            return allVoiceContent;
        }
        queryWrapper = new QueryWrapper();
        queryWrapper.in("position_id",robotPositionList.stream().map(RobotPosition::getId).collect(Collectors.toList()));
        queryWrapper.orderByAsc("sort_order");
        List<RobotLocation> robotLocationList = robotLocationMapper.selectList(queryWrapper);
        if(CollectionUtil.isNotEmpty(robotLocationList)) {
            for (RobotLocation robotLocation : robotLocationList) {
                String voiceContent = robotLocation.getLocationInfo() + robotLocation.getLocationCode();
                allVoiceContent.add(voiceContent);
            }
        }
        return allVoiceContent;
    }
}