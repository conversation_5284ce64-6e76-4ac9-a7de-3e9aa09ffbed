package com.yx.robot.modules.admin.sync.util;

import com.yx.robot.modules.base.utils.SendMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.PostConstruct;

@Slf4j
@Component
public class SyncRedisUtil {

    private static JedisPool jedisPool = null;
    private static String host;
    private static int port;
    private static String password;

    @Value("${yx-yun.host}")
    public void setHost(String host) {
        SyncRedisUtil.host = host;
    }

    @Value("${yx-yun.redis.port}")
    public void setPort(int port) {
        SyncRedisUtil.port = port;
    }

    @Value("${yx-yun.redis.password}")
    public void setPassword(String password) {
        SyncRedisUtil.password = "".equals(password) ? null : password;
    }

    @Autowired
    private SendMessageUtil sendMessageUtil;

    /**
     * 初始化Redis连接池
     */
    @PostConstruct
    public void init() {
        try {
            // 创建jedis池配置实例
            JedisPoolConfig config = new JedisPoolConfig();
            //连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
            config.setBlockWhenExhausted(true);
            //设置的逐出策略类名, 默认DefaultEvictionPolicy(当连接超过最大空闲时间,或连接数超过最大空闲连接数)
            config.setEvictionPolicyClassName("org.apache.commons.pool2.impl.DefaultEvictionPolicy");
            //是否启用pool的jmx管理功能, 默认true
            config.setJmxEnabled(true);
            //MBean ObjectName = new ObjectName("org.apache.commons.pool2:type=GenericObjectPool,name=" + "pool" + i); 默 认为"pool", JMX不熟,具体不知道是干啥的...默认就好.
            config.setJmxNamePrefix("pool");
            //是否启用后进先出, 默认true
            config.setLifo(true);
            //最大空闲连接数, 默认8个
            config.setMaxIdle(8);
            //最大连接数, 默认8个
            config.setMaxTotal(8);
            //获取连接时的最大等待毫秒数(如果设置为阻塞时BlockWhenExhausted),如果超时就抛异常, 小于零:阻塞不确定的时间,  默认-1
            config.setMaxWaitMillis(-1);
            //逐出连接的最小空闲时间 默认1800000毫秒(30分钟)
            config.setMinEvictableIdleTimeMillis(1800000);
            //最小空闲连接数, 默认0
            config.setMinIdle(0);
            //每次逐出检查时 逐出的最大数目 如果为负数就是 : 1/abs(n), 默认3
            config.setNumTestsPerEvictionRun(3);
            //对象空闲多久后逐出, 当空闲时间>该值 且 空闲连接>最大空闲数 时直接逐出,不再根据MinEvictableIdleTimeMillis判断  (默认逐出策略)
            config.setSoftMinEvictableIdleTimeMillis(1800000);
            //在获取连接的时候检查有效性, 默认false
            config.setTestOnBorrow(false);
            //在空闲时检查有效性, 默认false
            config.setTestWhileIdle(false);
            //逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
            config.setTimeBetweenEvictionRunsMillis(-1);
            // 设置池配置项值
            jedisPool = new JedisPool(config, host,
                    port, 30000, password);
            log.info("初始化Redis连接池success");
        } catch (Exception e) {
            log.error("初始化Redis连接池出错！", e);
            sendMessageUtil.sendShortMessage("初始化远程Redis连接池出错！");
        }
    }

    /**
     * 获取Jedis实例
     *
     * @return
     */
    public synchronized static Jedis getJedis() {
        try {
            if (jedisPool != null) {
                Jedis resource = jedisPool.getResource();
                return resource;
            } else {
                log.error("jedisPool is null");
                return null;
            }
        } catch (Exception e) {
            log.error("获取jedis失败:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 关闭 jedis
     *
     * @param jedis jedis
     */
    public static void closeJedis(Jedis jedis) {
        try {
            if (jedis != null) {
                jedis.close();
            }
        } catch (Exception e) {
            closeBrokenResource(jedis);
        }
    }

    /**
     * Return jedis connection to the pool, call different return methods depends on whether the connection is broken
     */
    public static void closeBrokenResource(Jedis jedis) {
        try {
            jedisPool.returnBrokenResource(jedis);
        } catch (Exception e) {
            destroyJedis(jedis);
        }
    }

    /**
     * 在 Jedis Pool 以外强行销毁 Jedis
     */
    public static void destroyJedis(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.quit();
            } catch (Exception e) {
                log.error(">>> RedisUtil-jedis.quit() : " + e);
                //e.printStackTrace();
            }

            try {
                jedis.disconnect();
            } catch (Exception e) {
                log.error(">>> RedisUtil-jedis.disconnect() : " + e);
                //e.printStackTrace();
            }
        }
    }
}
