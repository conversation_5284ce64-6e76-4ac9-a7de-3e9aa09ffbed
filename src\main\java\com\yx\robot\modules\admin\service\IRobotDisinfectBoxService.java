package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotDisinfectBox;
import com.yx.robot.modules.admin.entity.RobotPosition;

import java.util.List;

/**
 * 机器人消毒仓条目    接口
 * <AUTHOR>
 */
public interface IRobotDisinfectBoxService extends IService<RobotDisinfectBox> {

    /**
     * 开始消毒仓任务
     */
    void startDisinfectBoxTask();

    /**
     * 标记门禁点处理类
     * @param robotPosition
     */
    void handleMarkDisinfectBoxPosition(RobotPosition robotPosition);

    /**
     * 处理到达消毒仓点位
     * @param taskId
     * @param positionId
     * @return
     */
    boolean handleArriveDisinfectBoxPosition(String taskId,String positionId);

}