package com.yx.robot.modules.admin.service.core;

import com.yx.robot.common.utils.YmlUtils;
import com.yx.robot.modules.admin.scheduler.WebsocketRosScheduler;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

import static com.yx.robot.common.constant.RosWebConstants.FAIL;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/25 16:27
 */
@Component
@Slf4j
public class AfterRunner implements ApplicationRunner {

    private final WebsocketRosScheduler websocketRosScheduler;

    private final IRobotPositionService iRobotPositionService;

    @Autowired
    public AfterRunner(WebsocketRosScheduler websocketRosScheduler,
                       IRobotPositionService iRobotPositionService, Environment environment) {
        this.websocketRosScheduler = websocketRosScheduler;
        this.iRobotPositionService = iRobotPositionService;
        YmlUtils.setEnvironment(environment);
    }

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("ApplicationRunner.....");
        try {
            websocketRosScheduler.schedulerInfo();
        } catch (Exception e) {
            e.printStackTrace();
        }

        try {
            log.info("应用启动默认定位丢失");
            iRobotPositionService.setPositionStatus(FAIL);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
