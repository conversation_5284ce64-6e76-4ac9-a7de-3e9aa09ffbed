package com.yx.robot.modules.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotCheckDefine;
import com.yx.robot.modules.admin.service.IRobotCheckDefineService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人检测状态定义管理接口")
@RequestMapping("/yx/api-v1/robotCheckDefine")
@Transactional
public class RobotCheckDefineController {

    @Autowired
    private IRobotCheckDefineService iRobotCheckDefineService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotCheckDefine> get(@PathVariable String id){

        RobotCheckDefine robotCheckDefine = iRobotCheckDefineService.getById(id);
        return new ResultUtil<RobotCheckDefine>().setData(robotCheckDefine);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotCheckDefine>> getAll(){

        List<RobotCheckDefine> list = iRobotCheckDefineService.list();
        return new ResultUtil<List<RobotCheckDefine>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotCheckDefine>> getByPage(@ModelAttribute PageVo page,
                                                     @RequestParam(required = false) String code,
                                                     @RequestParam(required = false) String type){
        LambdaQueryWrapper<RobotCheckDefine> robotCheckDefineLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if(StrUtil.isNotBlank(code)) {
            robotCheckDefineLambdaQueryWrapper.eq(RobotCheckDefine::getCode,code);
        }
        if(StrUtil.isNotBlank(type)) {
            robotCheckDefineLambdaQueryWrapper.eq(RobotCheckDefine::getType,type);
        }
        IPage<RobotCheckDefine> data = iRobotCheckDefineService.page(PageUtil.initMpPage(page),robotCheckDefineLambdaQueryWrapper);
        return new ResultUtil<IPage<RobotCheckDefine>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotCheckDefine> saveOrUpdate(@ModelAttribute RobotCheckDefine robotCheckDefine){
        robotCheckDefine.setCreateTime(new Date());
        if(iRobotCheckDefineService.saveOrUpdate(robotCheckDefine)){
            return new ResultUtil<RobotCheckDefine>().setData(robotCheckDefine);
        }
        return new ResultUtil<RobotCheckDefine>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotCheckDefineService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
