package com.yx.robot.common.enums;

/**
 * <AUTHOR>
 */

public enum RobotCheckResponse {

    /**
     * true,"成功"
     */
    SUCCESS(true, "成功"),

    /**
     * false,"失败"
     */
    ERROR(false, "失败"),

    /**
     * false,"设备故障"
     */
    DEVICE_ERROR(false, "设备故障"),

    /**
     * false,"数据异常"
     */
    DATA_ERROR(false, "数据异常");

    private final boolean status;

    private final String msg;

    RobotCheckResponse(boolean status, String msg) {
        this.status = status;
        this.msg = msg;
    }

    public String getMsg() {
        return this.msg;
    }

    public boolean isStatus() {
        return this.status;
    }
}
