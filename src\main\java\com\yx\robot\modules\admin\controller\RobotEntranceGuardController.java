package com.yx.robot.modules.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotEntranceGuard;
import com.yx.robot.modules.admin.service.IRobotEntranceGuardService;
import com.yx.robot.modules.admin.vo.RobotEntranceGuardVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人门禁控制管理接口")
@RequestMapping("/yx/api-v1/robotEntranceGuard")
@Transactional
public class RobotEntranceGuardController {

    @Autowired
    private IRobotEntranceGuardService iRobotEntranceGuardService;


    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotEntranceGuard> get(@PathVariable String id) {
        RobotEntranceGuard robotEntranceGuard = iRobotEntranceGuardService.getById(id);
        return new ResultUtil<RobotEntranceGuard>().setData(robotEntranceGuard);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotEntranceGuardVo>> getAll() {
        List<RobotEntranceGuardVo> robotEntranceGuardVoList = iRobotEntranceGuardService.getAll();
        return new ResultUtil<List<RobotEntranceGuardVo>>().setData(robotEntranceGuardVoList);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotEntranceGuard>> getByPage(@ModelAttribute PageVo page) {

        IPage<RobotEntranceGuard> data = iRobotEntranceGuardService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotEntranceGuard>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotEntranceGuard> saveOrUpdate(@ModelAttribute RobotEntranceGuard robotEntranceGuard) {

        if (iRobotEntranceGuardService.saveOrUpdate(robotEntranceGuard)) {
            return new ResultUtil<RobotEntranceGuard>().setData(robotEntranceGuard);
        }
        return new ResultUtil<RobotEntranceGuard>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {
        for (String id : ids) {
            iRobotEntranceGuardService.delete(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
