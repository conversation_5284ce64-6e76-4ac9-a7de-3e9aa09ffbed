package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.ControlStatusConstants;
import com.yx.robot.common.constant.DeviceInfoConstants;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.RabbitMqMessageType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.config.wesocket.WebSocketServer;
import com.yx.robot.modules.admin.message._Bool;
import com.yx.robot.modules.admin.service.IRobotMotorService;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.RobotInfoVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.WebSocketConstants.DEVICE_INFO;

/**
 * 机器人设备信息收集
 *
 * <AUTHOR>
 * @date 2021/04/29
 */
@Component
@Slf4j
@Order(3)
public class RobotDeviceInfoSchedule implements CommandLineRunner {

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1, new BasicThreadFactory.Builder().namingPattern("scheduled-RobotDeviceInfo-%d").daemon(true).build());

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private WebSocketServer webSocketServer;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private RabbitMQScheduler rabbitMQScheduler;

    private static boolean deviceMaintenanceFlag = false;

    private static boolean ulrayMaintenanceFlag = false;

    private static boolean pulseNotifyControl = true;

    private static final int PULSE_USE_TIME = 3000;

    @Override
    public void run(String... args) throws Exception {
        executorService.scheduleWithFixedDelay(() -> {
            sumUlrayUsageTime();
            sendNotifyExpirationOfLamp();
            handleMaintenanceAlert();
            handleRobotDeviceInfo();
//            setMotorSleep();
        }, 0, 1, TimeUnit.SECONDS);
        log.info("任务状态检测中......");
    }

    /**
     * 计算灯管使用时间
     */
    private void sumUlrayUsageTime() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String ulrayStatus = jedis.get(TOPIC + "::" + TopicConstants.ULRAY_STATUS);
            if (StrUtil.isNotBlank(ulrayStatus)) {
                _Bool bool = JSONObject.parseObject(ulrayStatus, _Bool.class);
                if (!bool.data) {
                    // 单位为秒
                    String ulrayUsageTimeStr = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME);
                    if (StrUtil.isNotEmpty(ulrayUsageTimeStr)) {
                        Long ulrayUsageTime = Long.valueOf(ulrayUsageTimeStr);
                        ulrayUsageTime++;
                        jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME, ulrayUsageTime.toString());
                    }
                    // 单位为小时
                    String ulrayMaintenanceTime = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_MAINTENANCE_TIME);
                    ulrayUsageTimeStr = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME);
                    Long ulrayUsageTime = Long.valueOf(ulrayUsageTimeStr);
                    DecimalFormat df = new DecimalFormat("#.00");
                    Double ulrayUsageTimeHour = Double.valueOf(df.format(ulrayUsageTime / (60 * 60)));
                    Double lessMaintenanceTime = Double.valueOf(ulrayMaintenanceTime) - ulrayUsageTimeHour;
                    Double v = lessMaintenanceTime * 60 * 60 * 1000;
                    long i = v.longValue();
                    Date date = new Date();
                    long time = date.getTime();
                    if (v > 0) {
                        time = time + i;
                    } else {
                        time = time - i;
                    }
                    date.setTime(time);
                    SimpleDateFormat bjSdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String format = bjSdf.format(date);
                    jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_MAINTENANCE_DATE, format);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 灯管使用超时发送小程序提醒
     * 测试场景：U2-106，其紫外状态话题无数据，紫外控制话题无反应；
     * 使用的是脉冲控制话题和脉冲状态话题；
     * sumUlrayUsageTime（）方法因为紫外状态话题没数据，所以不走，如果走了的话会对此方法造成影响；
     */
    private void sendNotifyExpirationOfLamp() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String pulseStatus = jedis.get(TOPIC + "::" + TopicConstants.PULSE_STATUS);
            if (StrUtil.isNotBlank(pulseStatus)) {
                _Bool bool = JSONObject.parseObject(pulseStatus, _Bool.class);
                if (bool.data) {
                    String ulrayUsageTimeStr = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME);
                    if (StrUtil.isNotEmpty(ulrayUsageTimeStr)) {
                        Long ulrayUsageTime = Long.valueOf(ulrayUsageTimeStr);
                        ulrayUsageTime++;
                        jedis.hset(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME, ulrayUsageTime.toString());
                    }
                }
            }
            String ulrayUsageTimeStr = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_USAGE_TIME);
            if (StrUtil.isNotEmpty(ulrayUsageTimeStr)) {
                Double ulrayUsageTimeSeconds = Double.valueOf(ulrayUsageTimeStr);
                Double doubleSeconds = new Double(ulrayUsageTimeSeconds);
                Integer integerSeconds = doubleSeconds.intValue();
                Integer ulrayUsageTimeHour = integerSeconds / (60 * 60);
                RobotBaseInfoConstant.PULSE_LIGHT_SURPLUS_TIME = String.valueOf(PULSE_USE_TIME - ulrayUsageTimeHour);
                //当使用总时长达到3000小时的时候，每次开机都会通知到期
                if(ulrayUsageTimeHour > PULSE_USE_TIME  && pulseNotifyControl){
                    rabbitMQScheduler.meaasgeNotify(RabbitMqMessageType.EXPIRATION_OF_LAMP.getType());
                    pulseNotifyControl = false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 处理机器人设备信息
     */
    private void handleRobotDeviceInfo() {
        try {
            RobotInfoVo robotInfo = rosWebService.getRobotInfo();
            webSocketServer.sendAllMessage(JSON.toJSONString(robotInfo), DEVICE_INFO);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 计算设备保养日期
     */
    private void handleMaintenanceAlert() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String deviceMaintenanceDate = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.DEVICE_MAINTENANCE_DATE);
            String ulrayMaintenanceDate = jedis.hget(DeviceInfoConstants.DEVICE_INFO, DeviceInfoConstants.ULRAY_MAINTENANCE_DATE);
            // 设备保养日期，计算设备保养日期和当今日期进行对比
            DateTime deviceMaintenanceTime = DateUtil.parseDate(deviceMaintenanceDate);
            DateTime ulrayMaintenanceTime = DateUtil.parseDate(ulrayMaintenanceDate);
            DateTime currentDateTime = DateUtil.parseDate(DateUtil.now());
            int i1 = deviceMaintenanceTime.compareTo(currentDateTime);
            int i2 = ulrayMaintenanceTime.compareTo(currentDateTime);
            // 每天10点发送一条短信
            if (i1 > 0) {
                if (currentDateTime.hour(true) == 10) {
                    if (!deviceMaintenanceFlag) {
                        deviceMaintenanceFlag = true;
                    }
                } else {
                    deviceMaintenanceFlag = false;
                }
            }
            if (i2 > 0) {
                if (currentDateTime.hour(true) == 10) {
                    if (!ulrayMaintenanceFlag) {
                        ulrayMaintenanceFlag = true;
                    }
                } else {
                    ulrayMaintenanceFlag = false;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 定时检测机器人的充电状态
     * <p>
     * 正在充电则休眠电机, 否则唤醒电机
     */
    public void setMotorSleep() {
        iRobotMotorService.setMotorSleep(!iRobotStatusService.isDock());
    }

}
