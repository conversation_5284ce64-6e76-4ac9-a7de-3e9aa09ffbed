package com.yx.robot.modules.admin.vo;

import com.yx.robot.base.BaseEntity;
import lombok.Data;

@Data
public class RobotSubPositionVo extends BaseEntity {

    /**
     * 父级标定点id
     */
    private String parentRobotPositionId;

    /**
     * 父级位置区域id
     */
    private String parentRobotLocationId;

    /**
     * 类型
     */
    private Integer type;

    /**
     * 位置区域
     */
    private String locationInfo;

    /**
     * 位置编号
     */
    private String locationCode;

    /**
     * 地图id
     */
    private String mapId;

    /**
     * 是否重标定
     */
    private boolean recalibration;
}
