package com.yx.robot.modules.admin.electrl.enums;

/**
 * <AUTHOR>
 * @date 2022/03/16
 */
public enum ElevatorTypeEnum {

    /**
     * 0,"单云电梯"
     */
    SINGLE_ELEVATOR(0, "单云电梯"),

    /**
     * 1,"无感电梯"
     */
    Noninductive_ELEVATOR(1, "无感电梯");

    private final Integer type;

    private final String label;

    ElevatorTypeEnum(Integer type, String label) {
        this.type = type;
        this.label = label;
    }

    public Integer getType() {
        return type;
    }

    public String getLabel() {
        return label;
    }
}
