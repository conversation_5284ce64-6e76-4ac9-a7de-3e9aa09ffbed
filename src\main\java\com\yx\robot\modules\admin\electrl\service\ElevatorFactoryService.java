package com.yx.robot.modules.admin.electrl.service;

import com.yx.robot.modules.admin.electrl.msg.*;
import redis.clients.jedis.Jedis;

/**
 * 梯控厂家服务
 *
 * <AUTHOR>
 * @date 2021/12/11
 */
public interface ElevatorFactoryService {

    /**
     * 开发者认证
     *
     * @param developerLoginReq 登录请求参数
     * @return 登录响应参数
     */
    DeveloperLoginRep developerLogin(DeveloperLoginReq developerLoginReq);

    /**
     * 预约单云电梯
     *
     * @param jedis           jedis
     * @param callElevatorReq 呼叫预约单云电梯
     * @return 响应参数
     */
    CallElevatorRep callElevator(Jed<PERSON> jedis, CallElevatorReq callElevatorReq);

    /**
     * 预约无感电梯
     *
     * @param callNoninductiveElevatorReq 预约无感电梯请求参数
     * @return 预约无感电梯响应参数
     */
    CallNoninductiveElevatorRep callNoninductiveElevator(Jed<PERSON> jedis, CallNoninductiveElevatorReq callNoninductiveElevatorReq);

    /**
     * 发送开门指令
     *
     * @param jedis           jedis
     * @param sendOpenDoorReq 发送开门指令请求参数
     * @return 发送开门指令响应参数
     */
    SendOpenDoorRep sendOpenDoor(Jedis jedis, SendOpenDoorReq sendOpenDoorReq);

    /**
     * 获取任务信息
     *
     * @param jedis       jedis
     * @param taskInfoReq 任务请求参数
     * @return 任务响应参数
     */
    TaskInfoRep getTaskInfo(Jedis jedis, TaskInfoReq taskInfoReq);
}
