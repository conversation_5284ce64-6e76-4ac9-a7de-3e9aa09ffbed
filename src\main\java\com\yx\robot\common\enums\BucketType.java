package com.yx.robot.common.enums;

/**
 * minio bucket 枚举类型
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/15 17:11
 */
public enum BucketType {

    /**
     * 存放机器人智能控制平台文件
     */
    ROBOT("robot"),

    /**
     * 存放机器人ros系统文件
     */
    ROS("ros");

    private final String bucket;

    BucketType(String bucket) {
        this.bucket = bucket;
    }

    public String getBucket() {
        return bucket;
    }
}
