package com.yx.robot.modules.base.service;


import com.yx.robot.base.BaseService;
import com.yx.robot.modules.base.entity.UserRole;

import java.util.List;

/**
 * 用户角色接口
 * <AUTHOR>
 */
public interface UserRoleService extends BaseService<UserRole,String> {

    /**
     * 通过roleId查找
     * @param roleId
     * @return
     */
    List<UserRole> findByRoleId(String roleId);

    /**
     * 删除用户角色
     * @param userId
     */
    void deleteByUserId(String userId);
}
