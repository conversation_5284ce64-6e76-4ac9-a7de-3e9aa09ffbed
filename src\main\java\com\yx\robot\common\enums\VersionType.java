package com.yx.robot.common.enums;

/**
 * <AUTHOR>
 * @date 2022/5/25
 * @description: 版本类型常量
 */
public enum VersionType {

    /**
     * 机器人运行控制
     */
    CONTROL(1, "机器人运行控制平台"),

    /**
     * ROS系统
     */
    ROS(2, "ROS"),

    /**
     * 电气版本
     */
    ELECTRICAL(3, "电气版本"),

    /**
     * 机械版本
     */
    HARDWARE(4, "机械版本");

    private final Integer type;

    private final String label;

    VersionType(Integer type, String label) {
        this.type = type;
        this.label = label;
    }

    public Integer getType() {
        return type;
    }

    public String getLabel() {
        return label;
    }
}
