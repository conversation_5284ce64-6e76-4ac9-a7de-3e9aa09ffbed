package com.yx.robot.modules.admin.message;

@MessageType(string = "visualization_msgs/Marker")
public class _Marker extends Message {
    public short ARROW = 0;
    public short CUBE = 1;
    public short SPHERE = 2;
    public short CYLINDER = 3;
    public short LINE_STRIP = 4;
    public short LINE_LIST = 5;
    public short CUBE_LIST = 6;
    public short SPHERE_LIST = 7;
    public short POINTS = 8;
    public short TEXT_VIEW_FACING = 9;
    public short MESH_RESOURCE = 10;
    public short TRIANGLE_LIST = 11;
    public short ADD = 0;
    public short MODIFY = 0;
    public short DELETE = 2;
    public short DELETEALL = 3;
    public Header header;
    public String ns;

    /**
     * 10 脱离充电桩成功 15 脱离充电桩异常终止
     */
    public int id;
    public int type;
    public int action;
    public _Pose pose;
    public _Vector3 scale;
    public _ColorRGBA color;
    public Duration lifetime;
    public boolean frame_locked;
    public _Point[] points;
    public _ColorRGBA[] colors;
    public String text;
    public String mesh_resource;
    public boolean mesh_use_embedded_materials;
}
