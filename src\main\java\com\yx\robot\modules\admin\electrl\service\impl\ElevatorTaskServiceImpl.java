package com.yx.robot.modules.admin.electrl.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.utils.HttpSendUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dto.ElevatorPromiseDto;
import com.yx.robot.modules.admin.electrl.enums.ArriveElevatorStatusEnum;
import com.yx.robot.modules.admin.electrl.enums.ElectrlCodeEnum;
import com.yx.robot.modules.admin.electrl.enums.ElevatorTypeEnum;
import com.yx.robot.modules.admin.electrl.msg.*;
import com.yx.robot.modules.admin.electrl.scheduler.ElevatorTaskInfoScheduler;
import com.yx.robot.modules.admin.electrl.service.ElevatorFactoryService;
import com.yx.robot.modules.admin.electrl.service.ElevatorTaskService;
import com.yx.robot.modules.admin.entity.RobotMap;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.entity.RobotWorldPosition;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.util.*;

import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 梯控服务实现类
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
@Slf4j
@Service
public class ElevatorTaskServiceImpl implements ElevatorTaskService {

    private final IRobotMapService iRobotMapService;

    private final IRobotPositionService iRobotPositionService;

    private final RosWebService rosWebService;

    private final RosBridgeService rosBridgeService;

    private final IRobotWorldPositionService robotWorldPositionService;

    private final ElevatorFactoryService elevatorFactoryService;

    private final IRobotTaskService iRobotTaskService;

    private final HttpSendUtil httpSendUtil;

    private final IRobotStatusService iRobotStatusService;

    @Autowired
    public ElevatorTaskServiceImpl(IRobotMapService iRobotMapService, IRobotPositionService iRobotPositionService,
                                   RosWebService rosWebService, RosBridgeService rosBridgeService,
                                   IRobotWorldPositionService robotWorldPositionService,
                                   ElevatorFactoryService elevatorFactoryService, IRobotTaskService iRobotTaskService,
                                   HttpSendUtil httpSendUtil, IRobotStatusService iRobotStatusService) {
        this.iRobotMapService = iRobotMapService;
        this.iRobotPositionService = iRobotPositionService;
        this.rosWebService = rosWebService;
        this.rosBridgeService = rosBridgeService;
        this.robotWorldPositionService = robotWorldPositionService;
        this.elevatorFactoryService = elevatorFactoryService;
        this.iRobotTaskService = iRobotTaskService;
        this.httpSendUtil = httpSendUtil;
        this.iRobotStatusService = iRobotStatusService;
    }

    /**
     * 是否完成梯控任务
     */
    public static boolean hasFinishElevator = false;

    /**
     * 0:"单云电梯",1:"无感电梯"
     */
    @Value("${platform.electrl.elevator-type}")
    private Integer elevatorType;


    /**
     * 获取电梯请求信息
     *
     * @param positionId 位置ID
     */
    @Override
    public void hasElevatorPromise(String positionId) {
        Jedis jedis = null;
        ElevatorPromiseDto elevatorPromiseDto = new ElevatorPromiseDto();
        try {
            jedis = RedisUtil.getJedis();
            String currentMapId = Objects.requireNonNull(jedis).hget(ROBOT_SYS_INFO, CURRENT_MAP);
            RobotMap currentMap = iRobotMapService.getById(currentMapId);
            RobotPosition nextPosition = iRobotPositionService.getById(positionId);
            RobotMap nextMap = iRobotMapService.getById(nextPosition.getMapId());
            if (currentMap != null && nextMap != null && !currentMap.getFloor().equals(nextMap.getFloor())) {
                // 存储未执行的点位
                List<String> toDoContent = jedis.lrange(TO_DO_LIST, 0, -1);
                jedis.del(HAS_TO_DO_LIST);
                if (CollectionUtil.isNotEmpty(toDoContent)) {
                    for (String s : toDoContent) {
                        jedis.lpush(HAS_TO_DO_LIST, s);
                    }
                }
                jedis.hset(ROBOT_SYS_INFO, NEXT_MAP, nextMap.getId());
                elevatorPromiseDto.setCmd(ElevatorPromise.CALL_ELEVATOR.getCmd());
                elevatorPromiseDto.setCurrentFloor(currentMap.getFloor());
                elevatorPromiseDto.setTargetFloor(nextMap.getFloor());
                log.info("处于不同楼层，当前楼层{},目标楼层{},需要执行梯控操作", currentMap.getFloor(), nextMap.getFloor());
                // 上一次充电地图ID
                String endChargingMapId = jedis.hget(ROBOT_SYS_INFO, END_CHARGING_MAP_ID);
                if (StrUtil.isBlank(endChargingMapId)) {
                    jedis.hset(ROBOT_SYS_INFO, END_CHARGING_MAP_ID, currentMap.getId());
                }
                // 当前地图电梯外的机器人位置列表
                List<RobotPosition> outsideElevatorList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                        .eq(RobotPosition::getType, RobotPositionType.OUTSIDE_ELEVATOR_POSITION.getType())
                        .eq(RobotPosition::getMapId, currentMap.getId()));
                if (CollectionUtil.isNotEmpty(outsideElevatorList)) {
                    String ids = "";
                    String names = "";
                    for (RobotPosition robotPosition : outsideElevatorList) {
                        names = names.concat(robotPosition.getName());
                        ids = ids.concat(robotPosition.getId()).concat(",");
                    }
                    log.info("准备前往{}电梯外停靠点", names);
                    if (ids.endsWith(",")) {
                        ids = ids.substring(0, ids.length() - 1);
                    }
                    jedis.set(ELEVATOR_POSITION_ID, ids);
                }
                jedis.set(ELEVATOR_PROMISE, JSONObject.toJSONString(elevatorPromiseDto));
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 开始执行梯控任务
     *
     * @param elevatorPositionIds 梯控停考点
     */
    @Override
    public void startElevatorTask(String elevatorPositionIds) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            RobotTask robotTask = rosWebService.addRobotTask(TaskType.ELEVATOR.getValue(), 1, TaskType.ELEVATOR.getType(), null);
            Map<String, Object> positionIds = new LinkedHashMap<>();
            if (StrUtil.isNotBlank(elevatorPositionIds)) {
                String[] split = elevatorPositionIds.split(",");
                for (int i = 0; i < split.length; i++) {
                    positionIds.put(i + 1 + "", split[i]);
                }
                rosWebService.addPointToRedis(positionIds, robotTask.getId());
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 处理到达梯控点（0:异常 1：当前楼层梯控点 2：到达目标楼层梯控点）
     *
     * @param robotPosition 机器人位置
     * @return 0:异常 1：当前楼层梯控点 2：到达目标楼层梯控点
     */
    @Override
    public Integer handleArriveElevatorPosition(RobotPosition robotPosition) {
        Jedis jedis = null;
        try {
            hasFinishElevator = false;
            jedis = RedisUtil.getJedis();
            httpSendUtil.handleRestartNetWork();
            // 获取设备信息
            String s = Objects.requireNonNull(jedis).get(ELEVATOR_PROMISE);
            if (StrUtil.isBlank(s)) {
                log.info("无法获取电梯信息");
                return ArriveElevatorStatusEnum.ERROR.getStatus();
            }
            ElevatorPromiseDto elevatorPromiseDto = JSONObject.parseObject(s, ElevatorPromiseDto.class);
            boolean taskCancel = false;
            while (true) {
                boolean res = false;
                // 任务正在运行时，执行梯控操作
                if (iRobotStatusService.checkTaskStatus()) {
                    res = elevatorPromise(jedis, elevatorPromiseDto);
                }
                if (res) {
                    break;
                }
                // 如果当前任务ID为空，则当前任务被取消
                String currentTaskId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                if (StringUtil.isBlank(currentTaskId)) {
                    taskCancel = true;
                    break;
                }
                ThreadUtil.sleep(5000);
                log.info("呼叫电梯失败");
            }
            if (taskCancel) {
                return ArriveElevatorStatusEnum.ERROR.getStatus();
            }
            ElevatorTaskInfoScheduler.robotPosition = robotPosition;
            ElevatorTaskInfoScheduler.hasCallElevator = true;
            while (true) {
                log.info("等待电梯任务执行完成....");
                // 如果当前任务ID为空，则当前任务被取消
                String currentTaskId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                if (StringUtil.isBlank(currentTaskId)) {
                    log.info("梯控任务被取消");
                    break;
                }
                if (hasFinishElevator) {
                    log.info("电梯任务执行完成....");
                    break;
                }
                ThreadUtil.sleep(1000);
            }
            return ArriveElevatorStatusEnum.ARRIVE_TARGET_FLOOR_POINT.getStatus();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return ArriveElevatorStatusEnum.ERROR.getStatus();
    }

    @Override
    public void handleOutElevator() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            List<String> idArr = new ArrayList<>();
            List<String> toDoContent = Objects.requireNonNull(jedis).lrange(HAS_TO_DO_LIST, 0, -1);
            String taskId = "";
            if (CollectionUtil.isNotEmpty(toDoContent)) {
                for (String s : toDoContent) {
                    String[] split = s.split("#");
                    taskId = split[0];
                    String positionId = split[1];
                    idArr.add(positionId);
                }
            }
            RobotTask robotTask = iRobotTaskService.getById(taskId);
            log.info("准备开始执行未完成的任务,任务名称{},任务类型{}", robotTask.getName(), robotTask.getType());
            if (StrUtil.isNotBlank(taskId)) {
                Map<String, Object> layPositionIdsTrans = new LinkedHashMap<>();
                for (int i = 0; i < idArr.size(); i++) {
                    layPositionIdsTrans.put(i + 1 + "", idArr.get(i));
                }
                rosWebService.addPointToRedis(layPositionIdsTrans, taskId);
            }
            //清空地图
            rosWebService.clearMap();
            //语音提醒
            rosWebService.sendVoicePrompt(SceneType.START, null);
            rosWebService.publishUtil(ExpressionType.WORKING.getValue());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 电梯召唤
     *
     * @param jedis              jedis
     * @param elevatorPromiseDto 召唤参数
     * @return true 召唤成功 / false 召唤失败
     */
    @Override
    public boolean elevatorPromise(Jedis jedis, ElevatorPromiseDto elevatorPromiseDto) {
        log.info("执行电梯召唤,当前楼层{},目标楼层{}", elevatorPromiseDto.getCurrentFloor(), elevatorPromiseDto.getTargetFloor());
        if (elevatorType.equals(ElevatorTypeEnum.SINGLE_ELEVATOR.getType())) {
            CallElevatorReq callElevatorReq = new CallElevatorReq();
            callElevatorReq.setFromFloor(elevatorPromiseDto.getCurrentFloor() + "");
            callElevatorReq.setToFloor(elevatorPromiseDto.getTargetFloor() + "");
            CallElevatorRep callElevatorRep = elevatorFactoryService.callElevator(jedis, callElevatorReq);
            if (callElevatorRep != null && callElevatorRep.getMsgCode().equals(ElectrlCodeEnum.SUCCESS.getValue())) {
                log.info("预约单云电梯成功");
                return true;
            } else {
                if (callElevatorRep == null) {
                    log.info("预约单云电梯失败，无反馈");
                } else {
                    log.info("预约单云电梯失败，无反馈");
                }
            }
        } else if (elevatorType.equals(ElevatorTypeEnum.Noninductive_ELEVATOR.getType())) {
            CallNoninductiveElevatorReq callNoninductiveElevatorReq = new CallNoninductiveElevatorReq();
            callNoninductiveElevatorReq.setFromFloor(elevatorPromiseDto.getCurrentFloor() + "");
            callNoninductiveElevatorReq.setToFloor(elevatorPromiseDto.getTargetFloor() + "");
            CallNoninductiveElevatorRep callNoninductiveElevatorRep = elevatorFactoryService.callNoninductiveElevator(jedis, callNoninductiveElevatorReq);
            if (callNoninductiveElevatorRep != null && callNoninductiveElevatorRep.getMsgCode().equals(ElectrlCodeEnum.SUCCESS.getValue())) {
                log.info("预约无感电梯成功");
                return true;
            } else {
                if (callNoninductiveElevatorRep == null) {
                    log.info("预约无感电梯失败,无反馈");
                } else {
                    log.info("预约无感电梯失败,错误码{}", callNoninductiveElevatorRep.getMsgCode());
                }
            }
        }

        return false;
    }

    /**
     * 梯控任务反馈
     *
     * @param jedis jedis
     * @return 云控电梯响应结果
     */
    @Override
    public _ElevatorTaskFeedback elevatorTaskFeedback(Jedis jedis) {
        _ElevatorTaskFeedback elevatorTaskFeedback = null;
        String elevatorTaskFeedbackStr = jedis.get(TOPIC + "::" + TopicConstants.ELEVATOR_TASK_FEEDBACK);
        log.info("云控电梯响应结果:{}", elevatorTaskFeedback);
        if (StrUtil.isNotBlank(elevatorTaskFeedbackStr)) {
            elevatorTaskFeedback = JSONObject.parseObject(elevatorTaskFeedbackStr, _ElevatorTaskFeedback.class);
        }
        return elevatorTaskFeedback;
    }

    /**
     * 清空电梯任务反馈
     *
     * @param jedis jedis
     */
    @Override
    public void clearElevatorTaskFeedback(Jedis jedis) {
        jedis.del(TOPIC + "::" + TopicConstants.ELEVATOR_TASK_FEEDBACK);
    }

    /**
     * 点位转换
     *
     * @param robotPosition 机器人位置   position 代表后台定义的,Pose 是机器人底层
     * @return 机器人位置信息
     */
    @Override
    public _Pose position2Pose(RobotPosition robotPosition) {
        RobotWorldPosition robotWorldPosition = robotWorldPositionService.getById(robotPosition.getWorldPoseId());
        _Pose pose = new _Pose();
        pose.position = new _Point();
        pose.orientation = new _Quaternion();
        pose.position.x = robotWorldPosition.getPositionX();
        pose.position.y = robotWorldPosition.getPositionY();
        pose.position.z = robotWorldPosition.getPositionZ();
        pose.orientation.w = robotWorldPosition.getOrientationW();
        pose.orientation.x = robotWorldPosition.getOrientationX();
        pose.orientation.y = robotWorldPosition.getOrientationY();
        pose.orientation.z = robotWorldPosition.getOrientationZ();
        return pose;
    }

    /**
     * 进电梯
     *
     * @param elevatorPromiseDto 转换电梯的请求参数
     * @param robotPosition      机器人位置信息
     */
    @Override
    public void intoElevator(ElevatorPromiseDto elevatorPromiseDto, RobotPosition robotPosition) {
        log.info("执行进电梯,当前楼层{}", elevatorPromiseDto.getCurrentFloor());
        _ElevatorTaskReq elevatorTaskReq = new _ElevatorTaskReq();
        elevatorTaskReq.cmd = elevatorPromiseDto.getCmd();
        elevatorTaskReq.instruction = new _CallElevator();
        elevatorTaskReq.instruction.door_control = 0;
        elevatorTaskReq.instruction.current_floor = elevatorPromiseDto.getCurrentFloor();
        elevatorTaskReq.instruction.target_floor = elevatorPromiseDto.getTargetFloor();
        elevatorTaskReq.instruction.master_id = "1";
        elevatorTaskReq.instruction.motion_direction = "into";
        //需要告诉是那个电梯
        _Pose pose = position2Pose(robotPosition);
        // 获取内部停靠点
        elevatorTaskReq.elevator_goal = pose;
        // 执行梯控服务
        if (elevatorTaskReq.cmd == ElevatorPromise.INTO_ELEVATOR.getCmd()) {
            log.info("进来" + JSONObject.toJSONString(elevatorTaskReq));
            String s = rosBridgeService.callService(ServiceConstants.ELEVATOR_TASK, Message.getMessageType(_ElevatorTaskReq.class), JSONObject.toJSONString(elevatorTaskReq));
            if (StringUtil.isNotBlank(s)) {
                _ElevatorTaskRep elevatorTaskRep = JSONObject.parseObject(s, _ElevatorTaskRep.class);
                if (elevatorTaskRep.ret == 0) {
                    log.info("进电梯服务调用成功");
                }
            }
        }
    }

    /**
     * 出电梯
     *
     * @param elevatorPromiseDto 电梯请求dto
     * @param robotPosition      机器人位置信息
     */
    @Override
    public void outElevator(ElevatorPromiseDto elevatorPromiseDto, RobotPosition robotPosition) {
        log.info("执行出电梯,当前楼层{}", elevatorPromiseDto.getTargetFloor());
        _ElevatorTaskReq elevatorTaskReq = new _ElevatorTaskReq();
        elevatorTaskReq.cmd = elevatorPromiseDto.getCmd();
        elevatorTaskReq.instruction = new _CallElevator();
        elevatorTaskReq.instruction.door_control = 0;
        elevatorTaskReq.instruction.current_floor = elevatorPromiseDto.getCurrentFloor();
        elevatorTaskReq.instruction.target_floor = elevatorPromiseDto.getTargetFloor();
        elevatorTaskReq.instruction.master_id = "1";
        elevatorTaskReq.instruction.motion_direction = "out";
        _Pose pose = position2Pose(robotPosition);
        // 获取外部停靠点
        elevatorTaskReq.elevator_goal = pose;
        // 执行梯控服务
        if (elevatorTaskReq.cmd == ElevatorPromise.OUT_ELEVATOR.getCmd()) {
            String s = rosBridgeService.callService(ServiceConstants.ELEVATOR_TASK, Message.getMessageType(_ElevatorTaskReq.class), JSONObject.toJSONString(elevatorTaskReq));
            if (StringUtil.isNotBlank(s)) {
                _ElevatorTaskRep elevatorTaskRep = JSONObject.parseObject(s, _ElevatorTaskRep.class);
                if (elevatorTaskRep.ret == 0) {
                    log.info("出电梯服务调用成功");
                }
            }
        }
    }

    /**
     * 延长门开的时间
     *
     * @param jedis         jedis
     * @param position      位置信息
     * @param effectiveTime 0 松开按钮，即关门  1-99 开门时长，单位秒
     * @param deviceUnique  设备唯一标识
     */
    @Override
    public void delayElevator(Jedis jedis, String position, String effectiveTime, String deviceUnique) {
        SendOpenDoorReq sendOpenDoorReq = new SendOpenDoorReq();
        sendOpenDoorReq.setPosition(position);
        sendOpenDoorReq.setEffectiveTime(effectiveTime);
        sendOpenDoorReq.setDeviceUnique(deviceUnique);
        SendOpenDoorRep sendOpenDoorRep = elevatorFactoryService.sendOpenDoor(jedis, sendOpenDoorReq);
        log.info("sendOpenDoorRep:{}", JSON.toJSONString(sendOpenDoorRep));
        if (null != sendOpenDoorRep && sendOpenDoorRep.getMsgCode().equals(ElectrlCodeEnum.SUCCESS.getValue())) {
            log.info("延长开门成功");
        } else {
            if (sendOpenDoorRep == null) {
                log.info("延长开门失败,无反馈");
            } else {
//                ThreadUtil.sleep(3000);
//                delayElevator(jedis, position, effectiveTime, deviceUnique);
                log.info("延长开门失败,错误码{}", sendOpenDoorRep.getMsgCode());
            }
        }
    }

    /**
     * 关门指令
     *
     * @param jedis        jedis
     * @param deviceUnique 设备唯一标识
     * @param position     位置详细信息
     */
    @Override
    public void closeElevatorDoor(Jedis jedis, String position, String deviceUnique) {
        SendOpenDoorReq sendOpenDoorReq = new SendOpenDoorReq();
        sendOpenDoorReq.setPosition(position);
        sendOpenDoorReq.setEffectiveTime("0");
        sendOpenDoorReq.setDeviceUnique(deviceUnique);
        SendOpenDoorRep sendOpenDoorRep = elevatorFactoryService.sendOpenDoor(jedis, sendOpenDoorReq);
        log.info(JSONObject.toJSONString(sendOpenDoorRep));
        if (null != sendOpenDoorRep && sendOpenDoorRep.getMsgCode().equals(ElectrlCodeEnum.SUCCESS.getValue())) {
            log.info("关门成功");
        } else {
            if (sendOpenDoorRep == null) {
                log.info("关门失败,无反馈");
            } else {
                log.info("关门失败,错误码{}", sendOpenDoorRep.getMsgCode());
            }
        }
    }
}
