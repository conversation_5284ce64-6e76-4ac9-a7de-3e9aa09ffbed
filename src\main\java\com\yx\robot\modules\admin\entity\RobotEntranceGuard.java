package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_entrance_guard")
@ApiModel(value = "机器人门禁控制")
public class RobotEntranceGuard extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "地图id")
    private String mapId;

    @ApiModelProperty(value = "A点位关联id")
    private String positionAId;

    @ApiModelProperty(value = "B点位关联id")
    private String positionBId;

    @ApiModelProperty(value = "点中心x轴坐标")
    private Double centerX;

    @ApiModelProperty(value = "点中心y轴坐标")
    private Double centerY;

    @ApiModelProperty(value = "半径")
    private Double radius;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}