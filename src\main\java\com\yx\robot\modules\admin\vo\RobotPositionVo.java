package com.yx.robot.modules.admin.vo;

import com.yx.robot.base.BaseEntity;
import com.yx.robot.modules.admin.entity.RobotWorldPosition;
import lombok.Data;

@Data
public class RobotPositionVo extends BaseEntity {

    public RobotPositionVo() {
    }

    public RobotPositionVo(RobotSubPositionVo robotSubPositionVo) {
        this.robotPositionParentId = robotSubPositionVo.getParentRobotPositionId();
        this.robotLocationParentId = robotSubPositionVo.getParentRobotLocationId();
        this.name = robotSubPositionVo.getLocationInfo() + robotSubPositionVo.getLocationCode();
        this.type = robotSubPositionVo.getType();
        this.locationInfo = robotSubPositionVo.getLocationInfo();
        this.locationCode = robotSubPositionVo.getLocationCode();
        this.mapId = robotSubPositionVo.getMapId();
        this.recalibration = robotSubPositionVo.isRecalibration();
    }

    /**
     * 标定点父级id
     */
    private String robotPositionParentId;

    /**
     * 位置区域id
     */
    private String robotLocationId;

    /**
     * 位置区域父级id
     */
    private String robotLocationParentId;

    /**
     * 名称
     */
    private String name;

    /**
     * 点位类型
     */
    private Integer type;


    /**
     * 位置区域
     */
    private String locationInfo;

    /**
     * 位置编号
     */
    private String locationCode;

    /**
     * 当前地图id
     */
    private String mapId;

    /**
     * 是否重标定
     */
    private boolean recalibration;

    /**
     * 世界点位坐标
     */
    private RobotWorldPosition robotWorldPosition;

    /**
     * 设备ID
     */
    private String devId;

}
