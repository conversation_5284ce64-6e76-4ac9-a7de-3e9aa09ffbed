package com.yx.robot.common.enums;

/**
 * 电梯请求
 *
 * <AUTHOR>
 * @date 2021-11-24
 */
public enum ElevatorPromise {

    /**
     * 1,"召唤电梯"
     */
    CALL_ELEVATOR(1, "召唤电梯"),

    /**
     * 2,"进电梯"
     */
    INTO_ELEVATOR(2, "进电梯"),

    /**
     * 3,"出电梯"
     */
    OUT_ELEVATOR(3, "出电梯");

    private final int cmd;

    private final String label;

    ElevatorPromise(int cmd, String label) {
        this.cmd = cmd;
        this.label = label;
    }

    public int getCmd() {
        return cmd;
    }

    public String getLabel() {
        return label;
    }
}
