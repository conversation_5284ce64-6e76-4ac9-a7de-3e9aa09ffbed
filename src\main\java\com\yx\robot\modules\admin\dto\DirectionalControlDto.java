package com.yx.robot.modules.admin.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/19 21:48
 */
@Data
@ApiModel(value = "方向控制")
public class DirectionalControlDto {
    /**
     * 前后，x起作用，x>0 向前，x<0向后
     */
    @ApiModelProperty("前后，x>0 向前，x<0向后")
    private float x;

    /**
     * z 左右，z>0 向右，z<0 向左
     */
    @ApiModelProperty("左右，z>0 向右，z<0 向左")
    private float z;

    /**
     * 速度
     */
    @ApiModelProperty("速度")
    private float speed;
}