package com.yx.robot.modules.admin.electrl.enums;

/**
 * 梯控返回码定义
 *
 * <AUTHOR>
 * @date 2021/12/18
 */
public enum ElectrlCodeEnum {

    /**
     * 0,"成功"
     */
    SUCCESS(0, "成功");

    private final Integer value;

    private final String label;

    ElectrlCodeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
