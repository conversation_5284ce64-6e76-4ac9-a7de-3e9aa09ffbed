package com.yx.robot.modules.base.dao;

import com.yx.robot.base.BaseDao;
import com.yx.robot.modules.base.entity.RolePermission;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色权限数据处理层
 * <AUTHOR>
 */
@Repository
public interface RolePermissionDao extends BaseDao<RolePermission,String> {

    /**
     * 通过permissionId获取
     * @param permissionId
     * @return
     */
    List<RolePermission> findByPermissionId(String permissionId);

    /**
     * 通过roleId获取
     * @param roleId
     */
    List<RolePermission> findByRoleId(String roleId);

    /**
     * 通过roleId删除
     * @param roleId
     */
    void deleteByRoleId(String roleId);
}