package com.yx.robot.modules.admin.dto;

import lombok.Data;

/**
 * 电梯请求dto
 * <AUTHOR>
 * @date 2021-11-24
 */
@Data
public class ElevatorPromiseDto {

    /**
     * 执行操作（0：无操作 1：召唤电梯 2：进电梯 3：出电梯）
     */
    private int cmd;

    /**
     * 召唤类型(内部召唤和外部召唤)
     */
    private String type;

    /**
     * 当前楼层
     */
    private int currentFloor;

    /**
     * 目标楼层
     */
    private int targetFloor;
}
