package com.yx.robot.modules.admin.vo;

import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/4/7
 */
@Data
public class DisinfectSettingVo {

    /**
     * 活物关闭紫外线
     */
    private boolean livingThingsUlRay;

    /**
     * 语音警告
     */
    private boolean voiceWarning;

    /**
     * 背景音乐
     */
    private String backgroundMusic;

    /**
     * 沿途速度等级
     */
    private double disinfectSpeed = 0.2;

    /**
     * 西铭风速等级
     */
    private Integer xmFanLevel = 2;

    /**
     * 紫外线
     */
    private boolean ulRay;

    /**
     * 喷雾
     */
    private boolean spray;

    /**
     * 消毒模块
     */
    private boolean xt;

}
