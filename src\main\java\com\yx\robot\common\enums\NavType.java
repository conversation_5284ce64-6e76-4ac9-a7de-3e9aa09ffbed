package com.yx.robot.common.enums;

/**
 * 导航相关常量
 *
 * <AUTHOR>
 * @date 2020/07/20
 */
public enum NavType {

    /**
     * 导航行走中
     */
    NAV_DOING_TYPE(405, "导航行走中"),

    /**
     * 到达目标点
     */
    NAV_FINISH_TYPE(407, "到达目标点"),

    /**
     * 到达目标点附近
     */
    NAV_FINISH_NEARBY_TYPE(4071, "到达目标点附近"),

    /**
     * 急停开关摁下
     */
    EMERGENCY_MAKE_TYPE(4080, "急停开关摁下"),

    /**
     * 防撞条触碰
     */
    COLLISION_MAKE_TYPE(4081, "防撞条触碰"),

    /**
     * 定位问题
     */
//    LOCATION_MAKE_TYPE(4082, "定位问题"),

    /**
     * 节点错误
     */
    NODES_MAKE_TYPE(4083, "节点错误"),

    /**
     * 电机未使能
     */
    MOTOR_MAKE(4084, "电机未使能"),

    /**
     * 靠近障碍物导致局部路径规划失败
     */
    CLOSE_OBSTACLE_MAKE(4085, "靠近障碍物导致局部路径规划失败"),

    /**
     * 深度问题
     */
    DEPTH_MAKE_TYPE(4086, "深度问题"),

    /**
     * 路径规划失败
     */
    PLAN_MAKE_TYPE(4087, "路径规划失败"),

    /**
     * 手动取消
     */
    MANUAL_CANCEL_TYPE(4088, "手动取消");

    private final Integer type;

    private final String value;

    NavType(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return type;
    }

    public String getValue() {
        return value;
    }
}
