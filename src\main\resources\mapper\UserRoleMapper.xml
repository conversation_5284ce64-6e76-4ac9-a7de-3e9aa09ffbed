<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yx.robot.modules.base.dao.mapper.UserRoleMapper">

    <select id="findByUserId" resultType="com.yx.robot.modules.base.entity.Role">
      SELECT r.id id, name, r.data_type
      FROM t_user_role ur
      LEFT JOIN t_role r
      ON ur.role_id = r.id
      WHERE user_Id = #{userId}
    </select>

    <select id="findDepIdsByUserId" resultType="java.lang.String">
        SELECT DISTINCT rd.department_id
        FROM t_role_department rd
        WHERE role_id IN
        ( SELECT ur.role_id FROM t_user_role ur WHERE ur.user_id = #{userId} )
    </select>
</mapper>