robot:
  factory-info: 南京驭领科技有限公司
  hostname: yx-host
  host: *************
  port2: 9090
  type: 2 #机器人类型 1运输机器人 2消毒机器人
  config-path: /home/<USER>/app/config/yx-robot.properties
  baseInfo: /home/<USER>/udrive_v1_1_1/robotBaseInfo.json
  path:
    map: /home/<USER>/
  file:
    map-back-suffix: _amcl
    map-path: /home/<USER>/udrive_v1_1_1/ws/maps
    temp-map: map_tmp
    robot-map: robot-map
    temp-map-path: /home/<USER>/udrive_v1_1_1/ws/tmp
    move-base-path: /home/<USER>/udrive_v1_1_1/ws/install/share/nav_manage/launch
    move-base: move_base_R1_1_1_200316_a.launch
    image-path: /home/<USER>/udrive_v1_1_1/ws/image/
    deep-image-path: /home/<USER>/udrive_v1_1_1/ws/compressImage/

    bak: /home/<USER>/auto_install/bak
    install_file: /home/<USER>/auto_install/install_file
    install_jar_path: /home/<USER>/app/sys
    install_robot_system_shell: /home/<USER>/auto_install/auto_build_robot_system_server.sh
    install_robot_ros_shell: /home/<USER>/auto_install/auto_build_robot_ros_server.sh

  #极推配置
  jpush:
    app-key: 7df7930cdd26a496d211d511
    master-secret: 9acadc177c3ac22c20662d25
    alertTitle: 消安宝
    alertContent: 收到一条新消息
  time:
    start: 00:00:00
    end: 23:59:59

yx-yun:
  host: **************
  port: 6781
  context-path: /yx-yun
  url:
    sceneSpeech: /yxRobotSpeech/getSceneSpeechList
    mapList: /yxRobotDevice/mapList
    deviceList: /yxRobotDevice/deviceList
    sysActive: /yxRobotDevice/sysActive
    initData: /yxRobotDevice/initData
    syncRobotDeviceDataToYun: /yxRobotDeviceData/syncRobotDeviceDataToYun
    syncRobotDeviceDataToLocal: /yxRobotDeviceData/syncRobotDeviceDataToLocal
    serverId: /yxServerDeviceItem/getServerId
  username: root
  password: jAO6hSyG9MhGpNXkp+NPOA==
  redis:
    port: 9736
    password: jAO6hSyG9MhGpNXkp+NPOA==

spring:
  # 数据源
  datasource:
    url: jdbc:mysql://${yx-host:*************}:3306/yx?useUnicode=true&characterEncoding=utf-8&useSSL=false
    username: ENC(jAO6hSyG9MhGpNXkp+NPOA==)
    # Jasypt加密 可到common-utils中找到JasyptUtil加解密工具类生成加密结果 格式为ENC(加密结果)
    password: ENC(jAO6hSyG9MhGpNXkp+NPOA==)
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.jdbc.Driver
    # Druid StatViewServlet配置
    druid:
      stat-view-servlet:
        # 默认true 内置监控页面首页/druid/index.html
        enabled: true
        url-pattern: /druid/*
        # 允许清空统计数据
        reset-enable: true
        login-username: ENC(jAO6hSyG9MhGpNXkp+NPOA==)
        login-password: ENC(jAO6hSyG9MhGpNXkp+NPOA==)
        # IP白名单 多个逗号分隔
        allow:
        # IP黑名单
        deny:
  jpa:
    show-sql: true
    # 自动生成表结构
    generate-ddl: true
    hibernate:
      ddl-auto: update
  # Redis
  redis:
    host: ${yx-host:*************}
    password: udrive2019@
    # 数据库索引 默认0
    database: 0
    port: 6379
    # 超时时间 Duration类型 3秒
    timeout: 3S
  # 文件大小上传配置
  servlet:
    multipart:
      max-file-size: 5MB
      max-request-size: 5MB
  jackson:
    time-zone: GMT+8
    serialization:
      fail-on-empty-beans: false
  boot:
    admin:
      # 修改上下文路径
      context-path: /yx/admin
      client:
        url: http://${yx-host:*************}:${server.port}/yx/admin
  mqtt:
    username: admin
    password: password
    url: tcp://127.0.0.1:61613
    client:
      id: mqttId
    completionTimeout: 3000
#  rabbitmq:
#    host: *************
#    port: 5672
#    username: admin
#    password: admin
#    virtual-host: /
#    # 手动提交消息
#    listener:
#      simple:
#        acknowledge-mode: manual
#      direct:
#        acknowledge-mode: manual

yx:
  # 全局限流
  ratelimit:
    enable: true
    # 每1秒内
    timeout: 1000
    # 总限制100个请求
    limit: 100
  # IP限流
  iplimit:
    enable: true
    # 每1秒内
    timeout: 1000
    # 每个ip限制30个请求
    limit: 30
  # token交互方式
  token:
    # 设置为true后，token将存入redis，并具有单点登录功能 默认false使用JWT交互
    redis: true
    # 是否开启单设备登陆 仅当token交互方式为redis时生效
    sdl: true
    # token中存储用户权限数据 设为true开启后可避免每次请求再获取用户权限，但有可能导致编辑权限菜单后无法读取到最新权限数据（需用户重新登录）
    storePerms: true
    # token过期时间（分钟）
    tokenExpireTime: 1440
    # 用户选择保存登录状态对应token过期时间（天）
    saveLoginTime: 7
    # 限制用户登陆错误次数（次）
    loginTimeLimit: 10
    # 错误超过次数后多少分钟后才能继续登录（分钟）
    loginAfterTime: 10
  # 日志记录方式 true使用Elasticsearch记录 false记录至数据库中
  logRecord:
    es: false
  # 七牛云配置
  qiniu:
    accessKey: 5bCkKTZaYoKoZ_U3NNKSGxgciTjNsCwVaaVCi8qR
    secretKey: gi-kd9C6l5Fzi-u2kPqrzT_EtEAi8aOPY4_6B5Su
    bucket: daxi520
    domain: daxi520.s3-cn-south-1.qiniucs.com
    # 存储区域 -1自动判断 0华东 1华北 2华南 3北美 4东南亚
    zone: -1
  # mob api配置 mob官网注册申请即可
  mob:
    appKey: 270c4d225bcf0

  #配置语音类型 默认小乐版
  speechType: scene-speech.json

  minio:
    host: **************
    port: 9000
    secretKey: S84T19ZCA5ADY2QEQH3H
    accessKey: robot_client

# Swagger界面内容配置
swagger:
  title: 驭领机器人API接口文档
  description: Driving robot API interface document
  version: 1.0.0
  termsOfServiceUrl:
  contact:
    name: Darcy
    url:
    email: <EMAIL>

# Mybatis-plus
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
# 日志
logging:
  config: classpath:logback-spring.xml

