package com.yx.robot.common.constant;

/**
 * 机器人信息，保存的redis中的键值
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 13:40
 */
public interface RobotRedisConstants {

    /**
     * 机器人基本信息MAP
     */
    String ROBOT_INFO_MAP = "ROBOT:BASE_INFO";

    /**
     * 机器人型号
     */
    String ROBOT_INFO_MAP_TYPE = "type";

    /**
     * 机器人名称
     */
    String ROBOT_INFO_MAP_NAME = "name";

    /**
     * 机器人版本号
     */
    String ROBOT_INFO_MAP_VERSION = "version";

    /**
     * 生产日期
     */
    String ROBOT_INFO_MAP_PRODUCT_DATE = "product_date";

    /**
     * 序列号
     */
    String ROBOT_INFO_MAP_SERIAL_NUMBER = "serial_number";

    /**
     * 机器人编号
     */
    String ROBOT_INFO_MAP_ROBOT_NUMBER = "robot_number";

    /**
     * 机器人ROS版本,每次版本更新时，更新。开机启动不更新
     */
    String ROBOT_INFO_MAP_ROS_VERSION = "ros_version";

    /**
     * 机器人WEB版本,每次版本更新时，更新。开机启动不更新
     */
    String ROBOT_INFO_MAP_WEB_VERSION = "web_version";

    /**
     * 机器人web版本发布日期
     */
    String ROBOT_INFO_MAP_WEB_PUBLISH = "web_publish";

    /**
     * 机器人任务可执行时间段
     */
    String ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP = "ROBOT_TASK_EXECUTABLE_TIME_PERIODS";

    /**
     * 是否正在更新中
     */
    String IS_UPDATE_ROBOT_INFO_MAP_WEB_VERSION = "IS_UPDATE_WEB_VERSION";

    /**
     * 是否正在更新中
     */
    String IS_UPDATE_ROBOT_INFO_MAP_ROS_VERSION = "IS_UPDATE_ROS_VERSION";
    /**
     * 正在运行
     */
    String UPDATE_STATUS_RUNNING = "running";

    /**
     * 更新状态
     */
    String UPDATE_STATUS_FINISH = "finish";

    /**
     * 当前正在开着的门禁设备ID
     */
    String ENTRANCE_CURRENT_OPEN_DEV_ID = "ENTRANCE_CURRENT_OPEN_DEV_ID";

    /**
     * 门禁穿越状态
     */
    String ENTRANCE_ACCESS_STATUS_MAP = "ENTRANCE:ACCESS_STATUS";

    /**
     * 门禁ID
     */
    String ENTRANCE_ACCESS_STATUS_ID = "id";

    /**
     * 门禁上
     */
    String ENTRANCE_ACCESS_STATUS_UP = "up";

    /**
     * 门禁下
     */
    String ENTRANCE_ACCESS_STATUS_DOWN = "down";
}
