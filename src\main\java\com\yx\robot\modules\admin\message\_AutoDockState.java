package com.yx.robot.modules.admin.message;

@MessageType(string = "riki_msgs/robot_auto_dock_state")
public class _AutoDockState extends Message{
    /**
     * 0:未充电 1：手动充电 2：自动充电 3：手推充电
     */
    public short type;
    /**
     * 是否在对接充电桩
     */
    public boolean working;
    /**
     * 是否充上电
     */
    public boolean charged;
    /**
     * 尝试次数
     */
    public short try_times;
    /**
     * 总共耗时
     */
    public float total_time;
    /**
     * 错误码 -1:寻找充电桩失败 -2:充电头对接充电桩失败
     */
    public short error;
}
