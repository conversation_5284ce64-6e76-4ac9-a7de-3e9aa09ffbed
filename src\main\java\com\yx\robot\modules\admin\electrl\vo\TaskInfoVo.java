package com.yx.robot.modules.admin.electrl.vo;

import lombok.Data;

/**
 * 任务信息视图层
 *
 * <AUTHOR>
 * @date 2021/12/11
 */
@Data
public class TaskInfoVo {

    /**
     * 机器人编号
     */
    private String robotId;

    /**
     * 设备编号(该值可能为云群控器或者云电梯)
     */
    private String deviceUnique;

    /**
     * 云群控器预约电梯时，当 step=2 时机器人应该进
     * 入的电梯，请以此值确定机器人进入哪一步电
     * 梯，并且发送开关门指令必须是向云电梯发送，
     * step=1 时值为空
     */
    private String entryDeviceUnique;

    /**
     * 乘梯阶段
     * 1 等待到达出发楼层
     * 2 到达出发楼层，可以进梯
     * 3 等待到达目的楼层
     * 4 到达目的楼层，可以出梯
     * 只有为 2|4 时才能发送开关门指令
     */
    private String step;

    /**
     * 开始楼层
     */
    private String fromFloor;

    /**
     * 到达楼层
     */
    private String toFloor;

    /**
     * 开门剩余时长，单位秒，需要机器人自己扣除网
     * 络延时时间
     * 机器人得到接口响应后，doorOpenTime-(当前毫
     * 秒时间戳-doorOpenTimeBaseTimestamp)/1000
     */
    private String doorOpenTime;

    /**
     * 剩余开门时长基于的时间戳，单位毫秒
     */
    private String doorOpenTimeBaseTimestamp;

    /**
     * 电梯当前所在楼层
     * 0 或者-表示未知
     * (云群控器的情况下机器人只关注来接它的那部云
     * 电梯的状态)
     */
    private String floor;
}
