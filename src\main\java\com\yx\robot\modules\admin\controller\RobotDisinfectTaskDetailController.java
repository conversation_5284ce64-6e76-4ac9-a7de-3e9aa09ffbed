package com.yx.robot.modules.admin.controller;

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotDisinfectTaskDetail;
import com.yx.robot.modules.admin.service.IRobotDisinfectTaskDetailService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人消毒任务详情管理接口")
@RequestMapping("/yx/api-v1/robotDisinfectTaskDetail")
@Transactional
public class RobotDisinfectTaskDetailController {

    @Autowired
    private IRobotDisinfectTaskDetailService iRobotDisinfectTaskDetailService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotDisinfectTaskDetail> get(@PathVariable String id){

        RobotDisinfectTaskDetail robotDisinfectTaskDetail = iRobotDisinfectTaskDetailService.getById(id);
        return new ResultUtil<RobotDisinfectTaskDetail>().setData(robotDisinfectTaskDetail);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotDisinfectTaskDetail>> getAll(){
        List<RobotDisinfectTaskDetail> list = iRobotDisinfectTaskDetailService.list();
        return new ResultUtil<List<RobotDisinfectTaskDetail>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotDisinfectTaskDetail>> getByPage(@ModelAttribute PageVo page){
        if (StringUtils.isBlank(page.getSortOrigin())){
            page.setSort("start_time");
        }
        IPage<RobotDisinfectTaskDetail> data = iRobotDisinfectTaskDetailService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotDisinfectTaskDetail>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotDisinfectTaskDetail> saveOrUpdate(@ModelAttribute RobotDisinfectTaskDetail robotDisinfectTaskDetail){

        if(iRobotDisinfectTaskDetailService.saveOrUpdate(robotDisinfectTaskDetail)){
            return new ResultUtil<RobotDisinfectTaskDetail>().setData(robotDisinfectTaskDetail);
        }
        return new ResultUtil<RobotDisinfectTaskDetail>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotDisinfectTaskDetailService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
