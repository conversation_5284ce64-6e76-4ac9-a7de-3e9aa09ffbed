package com.yx.robot.common.enums;

/**
 * <AUTHOR>
 * @date 2020/4/15
 */
public enum DisinfectSwitch {
    /**
     * 紫外线
     */
    ULRAY_CTRL(2),

    /**
     * 喷雾
     */
    SPRAY_CTRL(3),

    /**
     * 风扇
     */
    FAN_CTRL(4),

    /**
     * 升降杆
     */
    SHIELDING_CTRL(5),

    /**
     * 脉冲灯
     */
    PULSE_CTRL(7);

    private final Integer value;

    DisinfectSwitch(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

}
