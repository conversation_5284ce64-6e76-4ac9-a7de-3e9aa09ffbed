package com.yx.robot.common.enums;

/**
 * 任务类型
 *
 * <AUTHOR>
 * @date 2020/3/14
 */
public enum TaskType {
    /**
     * 回到原点
     */
    ORIGIN(0, "回到原点"),

    /**
     * 返回充电
     */
    CHARGING(3, "返回充电"),

    /**
     * 门禁服务
     */
    ENTRANCE_GUARD(4, "门禁服务"),

    /**
     * 梯控模式
     */
    ELEVATOR(8, "梯控模式"),

    /**
     * 消毒任务
     */
    DISINFECT(9, "消毒服务"),

    /**
     * 消毒仓
     */
    DISINFECT_BOX(10, "消毒仓服务");

    private final Integer type;

    private final String value;

    TaskType(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return type;
    }

    public String getValue() {
        return value;
    }

}
