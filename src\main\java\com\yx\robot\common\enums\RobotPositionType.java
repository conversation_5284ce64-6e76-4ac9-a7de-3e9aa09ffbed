package com.yx.robot.common.enums;

/**
 * 点位类型
 *
 * <AUTHOR>
 * @date 2020/02/19
 */
public enum RobotPositionType {

    /**
     * 原点
     */
    START_POSITION(1, "原点"),

    /**
     * 充电桩
     */
    CHARGING_POSITION(2, "充电桩"),

    /**
     * 定位点
     */
    INIT_POSITION(3, "定位点"),

    /**
     * 位置区域
     */
    ROOM_POSITION(5, "位置区域"),

    /**
     * 6 门禁内
     */
    INSIDE_ENTRANCE_GUARD(6, "门禁内"),

    /**
     * 门禁外
     */
    OUTSIDE_ENTRANCE_GUARD(7, "门禁外"),

    /**
     * 电梯内
     */
    INSIDE_ELEVATOR_POSITION(8, "电梯内"),

    /**
     * 电梯外
     */
    OUTSIDE_ELEVATOR_POSITION(9, "电梯外"),

    /**
     * 巡游点
     */
    CRUISE_POSITION(11, "巡游点"),

    /**
     * 消毒仓外1
     */
    OUTSIDE_DISINFECT_BOX_POSITION_1(12, "消毒仓外1"),

    /**
     * 消毒仓内
     */
    INSIDE_DISINFECT_BOX_POSITION(13, "消毒仓内"),

    /**
     * 消毒仓外2
     */
    OUTSIDE_DISINFECT_BOX_POSITION_2(14, "消毒仓外2");

    private final Integer type;

    private final String value;

    RobotPositionType(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return type;
    }

    public String getValue() {
        return value;
    }
}
