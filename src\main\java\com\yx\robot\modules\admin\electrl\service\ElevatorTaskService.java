package com.yx.robot.modules.admin.electrl.service;

import com.yx.robot.modules.admin.dto.ElevatorPromiseDto;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.message._ElevatorTaskFeedback;
import com.yx.robot.modules.admin.message._Pose;
import redis.clients.jedis.Jedis;

/**
 * 梯控服务
 *
 * <AUTHOR>
 * @date 2021-11-23
 */
public interface ElevatorTaskService {

    /**
     * 获取电梯请求信息
     *
     * @param positionId 位置ID
     */
    void hasElevatorPromise(String positionId);

    /**
     * 开始执行梯控任务
     *
     * @param elevatorPositionIds 梯控停考点
     */
    void startElevatorTask(String elevatorPositionIds);

    /**
     * 处理到达梯控点（0:异常 1：当前楼层梯控点 2：到达目标楼层梯控点）
     *
     * @param robotPosition 机器人位置
     * @return 0:异常 1：当前楼层梯控点 2：到达目标楼层梯控点
     */
    Integer handleArriveElevatorPosition(RobotPosition robotPosition);

    /**
     * 处理离开电梯业务逻辑
     */
    void handleOutElevator();

    /**
     * 电梯召唤
     *
     * @param jedis              jedis
     * @param elevatorPromiseDto 召唤参数
     * @return true 召唤成功 / false 召唤失败
     */
    boolean elevatorPromise(Jedis jedis, ElevatorPromiseDto elevatorPromiseDto);

    /**
     * 清空电梯任务反馈
     *
     * @param jedis jedis
     */
    void clearElevatorTaskFeedback(Jedis jedis);

    /**
     * 点位转换
     *
     * @param robotPosition 机器人位置   position 代表后台定义的,Pose 是机器人底层
     * @return 机器人位置信息
     */
    _Pose position2Pose(RobotPosition robotPosition);

    /**
     * 梯控任务反馈
     *
     * @param jedis jedis
     * @return 云控电梯响应结果
     */
    _ElevatorTaskFeedback elevatorTaskFeedback(Jedis jedis);

    /**
     * 进电梯
     *
     * @param elevatorPromiseDto 转换电梯的请求参数
     * @param robotPosition      机器人位置信息
     */
    void intoElevator(ElevatorPromiseDto elevatorPromiseDto, RobotPosition robotPosition);

    /**
     * 出电梯
     *
     * @param elevatorPromiseDto 电梯请求dto
     * @param robotPosition      机器人位置信息
     */
    void outElevator(ElevatorPromiseDto elevatorPromiseDto, RobotPosition robotPosition);

    /**
     * 延长门开的时间
     *
     * @param jedis         jedis
     * @param position      位置信息
     * @param effectiveTime 0 松开按钮，即关门  1-99 开门时长，单位秒
     * @param deviceUnique  设备唯一标识
     */
    void delayElevator(Jedis jedis, String position, String effectiveTime, String deviceUnique);

    /**
     * 关门指令
     *
     * @param jedis        jedis
     * @param deviceUnique 设备唯一标识
     * @param position     位置详细信息
     */
    void closeElevatorDoor(Jedis jedis, String position, String deviceUnique);

}
