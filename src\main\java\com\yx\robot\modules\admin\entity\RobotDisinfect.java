package com.yx.robot.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yx.robot.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_disinfect")
@ApiModel(value = "机器人消毒任务条目")
public class RobotDisinfect extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务id")
    private String robotTaskId;

    @ApiModelProperty(value = "位置区域id")
    private String robotLocationId;

    @ApiModelProperty(value = "消毒时间")
    private Integer disinfectTime;

    @ApiModelProperty(value = "定点消毒后等待时间")
    private Integer waitTime;

    @ApiModelProperty(value = "紫外线")
    private String ulray;

    @ApiModelProperty(value = "喷雾")
    private String spray;

    @ApiModelProperty(value = "消毒模块")
    private String xt;

    @ApiModelProperty(value = "摄像头")
    private String isVideo;
}