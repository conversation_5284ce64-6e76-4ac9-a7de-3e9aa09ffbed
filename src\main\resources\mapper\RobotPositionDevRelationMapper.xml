<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.yx.robot.modules.admin.dao.mapper.RobotPositionDevRelationMapper">

    <select id="getDeviceIdByPositionAndDeviceType" resultType="java.lang.String">
        SELECT dev_id as devId
        FROM t_robot_position_dev_relation re
                 LEFT JOIN t_robot_device_info dev ON re.dev_id = dev.id
        WHERE re.position_id = #{params.positionId}
          AND dev.type = #{params.type}
          AND dev.sub_type = #{params.subType}
    </select>
</mapper>