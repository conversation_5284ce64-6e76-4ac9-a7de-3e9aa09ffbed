package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_task_item")
@ApiModel(value = "机器人列表")
public class RobotTaskItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务id")
    private String taskId;

    @ApiModelProperty(value = "点位id")
    private String positionId;

    @ApiModelProperty(value = "导航指令(1 开始导航 2取消导航)")
    private Integer cmd;

    @ApiModelProperty(value = "导航模式(0 自主规划 1固定路线)")
    private Integer navMode;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;

}