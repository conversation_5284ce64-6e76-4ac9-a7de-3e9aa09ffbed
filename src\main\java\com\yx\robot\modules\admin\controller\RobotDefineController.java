package com.yx.robot.modules.admin.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.yx.robot.common.constant.ControlStatusConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.DisinfectSwitch;
import com.yx.robot.common.enums.RouteAvoidType;
import com.yx.robot.common.enums.SceneType;
import com.yx.robot.common.utils.*;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.DirectionalControlDto;
import com.yx.robot.modules.admin.dto.RobotBaseInfoVo;
import com.yx.robot.modules.admin.dto.YxRobotDeviceDto;
import com.yx.robot.modules.admin.entity.RobotUser;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.dto.YxRobotVersionDto;
import com.yx.robot.modules.admin.message._LaserScan;
import com.yx.robot.modules.admin.message._Pose;
import com.yx.robot.modules.admin.message._SystemCheck;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.sync.service.DataSyncService;
import com.yx.robot.modules.admin.vo.*;
import com.yx.robot.modules.base.utils.LidarControlUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.yx.robot.common.constant.ControlStatusConstants.CONTROL_SLEEP;
import static com.yx.robot.common.constant.ControlStatusConstants.FIXED_TIME_RESTART_ROBOT;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.RosWebConstants.WATER_LEVEL_OPEN_STATE;
import static com.yx.robot.common.constant.TopicConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人列表管理接口")
@RequestMapping("/yx/api-v1/robotDefine")
@Transactional
public class RobotDefineController {

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private LidarControlUtil lidarControlUtil;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IpInfoUtil ipInfoUtil;

    @Autowired
    private DataSyncService dataSyncService;

    @Autowired
    private IRobotDefineService iRobotDefineService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private IRobotMapService iRobotMapService;

    @Autowired
    private IRobotUserService iRobotUserService;

    @Value("${server.port}")
    private Integer port;

    @Value("${robot.port2}")
    private Integer port2;

    @Value("${yx-yun.host}")
    private String yxYunHost;

    @Value("${yx-yun.port}")
    private Integer yxYunPort;

    @Value("${yx-yun.context-path}")
    private String yxYunContextPath;

    @Value("${yx-yun.url.deviceList}")
    private String yxYunDeviceList;

    @Autowired
    private IRobotVersionService iRobotVersionService;

    @Autowired
    private IRobotBaseInfoService iRobotBaseInfoService;

    @RequestMapping(value = "getRemoteDeviceList", method = RequestMethod.GET)
    @ApiOperation(value = "获取云端机器人列表")
    public Result<List<YxRobotDeviceDto>> getRemoteDeviceList() {
        List<YxRobotDeviceDto> data = new ArrayList<>();
        String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + yxYunDeviceList;
        try {
            JSONObject result = RestUtil.get(url);
            if (result != null && null != result.get("result")) {
                Object result1 = result.get("result");
                if (null != result1) {
                    data = JSON.parseObject(JSON.toJSONString(result1), List.class);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return new ResultUtil<List<YxRobotDeviceDto>>().setData(data);
    }

    @RequestMapping(value = "getRobotTypeAndCode", method = RequestMethod.GET)
    @ApiOperation(value = "获取机器人类型和编号")
    public Result<String> getRobotTypeAndCode() {
        String serialNumber = RDes.getSerialNumber();
        String robotTypeAndCode = RDes.decrypt(serialNumber);
        if (robotTypeAndCode.contains("-")) {
            return new ResultUtil<String>().setData(robotTypeAndCode);
        } else {
            return new ResultUtil<String>().setErrorMsg("获取机器人类型和编号异常");
        }
    }

    @RequestMapping(value = "appIsConnected", method = RequestMethod.GET)
    @ApiOperation(value = "app已经建立连接")
    public Result<Boolean> appIsConnected() {
        rosWebService.sendVoicePrompt(SceneType.CONNECTED, null);
        boolean res = rosWebService.mapCheck();
        if (!res) {
            rosWebService.sendVoicePrompt(SceneType.ALERT_MAP, null);
        }
        return new ResultUtil<Boolean>().setData(true);
    }

    @RequestMapping(value = "getRobotHostAndPort", method = RequestMethod.GET)
    @ApiOperation(value = "获取机器人主机地址和端口")
    public Result<String> getRobotHostAndPort() {
        String result = ipInfoUtil.getIp() + ":" + port;
        return new ResultUtil<String>().setData(result);
    }

    @RequestMapping(value = "getRobotHostAndPort2", method = RequestMethod.GET)
    @ApiOperation(value = "获取机器人主机地址和端口2")
    public Result<String> getRobotHostAndPort2() {
        String result = ipInfoUtil.getIp() + ":" + port2;
        return new ResultUtil<String>().setData(result);
    }

    @RequestMapping(value = "shutdownCtrl", method = RequestMethod.GET)
    @ApiOperation("关机控制")
    public Result<Boolean> shutdownCtrl() {
        rosWebService.notifyRosShutdown();
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/voice", method = RequestMethod.GET)
    @ApiOperation("语音控制")
    public Result<Boolean> voiceCtrl(Integer operation) {
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/positionCtrl", method = RequestMethod.GET)
    @ApiOperation(value = "定位控制")
    @ApiImplicitParam(name = "operation", value = "是否定位控制:true:关闭 / false：开启", dataType = "boolean",
            paramType = "query", allowableValues = "true,false")
    public Result<Boolean> positionCtrl(@RequestParam Boolean operation) {
        boolean res = rosWebService.positionCtrl(operation);
        return new ResultUtil<Boolean>().setData(res);
    }

    @RequestMapping(value = "/robotInfo", method = RequestMethod.GET)
    @ApiOperation("机器人相关信息")
    public Result<RobotInfoVo> robotInfo() {
        RobotInfoVo robotInfoVo = rosWebService.getRobotInfo();
        return new ResultUtil<RobotInfoVo>().setData(robotInfoVo);
    }

    @RequestMapping(value = "/positionCtrlStatus", method = RequestMethod.GET)
    @ApiOperation("获取定位控制状态")
    public Result<Boolean> getPositionCtrlStatus() {
        boolean res = false;
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, POSITION_CTRL_STATUS);
        if (o != null && StrUtil.isNotBlank(o.toString())) {
            res = Boolean.valueOf(o.toString());
        }
        return new ResultUtil<Boolean>().setData(res);
    }

    @RequestMapping(value = "/autoDockCtrl", method = RequestMethod.GET)
    @ApiOperation("自充控制")
    @ApiImplicitParam(name = "operation", value = "自充控制:true:关闭 / false：开启", dataType = "boolean",
            paramType = "query", allowableValues = "true,false")
    public Result<Boolean> handleAutoDockCtrl(@RequestParam Boolean operation) {
        boolean res = rosWebService.autoDockCtrl(operation);
        return new ResultUtil<Boolean>().setData(res);
    }

    @RequestMapping(value = "/adCtrl", method = RequestMethod.GET)
//    @ApiOperation(value = "广告屏控制")
    public Result<Boolean> adControl(@RequestParam boolean operation) {
        rosWebService.adControl(operation);
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/initData", method = RequestMethod.GET)
    @ApiOperation(value = "初始化数据")
    public Result<Boolean> initData() {
        Integer value = iRobotStatusService.getOperationStatus().getValue();
        if(value == 1 || value == 2 || value==3){
            log.info("机器人正在工作，无法恢复出厂设置!");
            return new ResultUtil<Boolean>().setData(true, "机器人正在工作，无法恢复出厂设置");
        }
        rosWebService.initData();
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "robotBattery", method = RequestMethod.GET)
    @ApiOperation("获取机器人电量")
    public Result<Float> getRobotBattery() {
        Float batteryRemain = iRobotStatusService.getBatteryPercentage();
        if (ObjectUtil.isNull(batteryRemain)) {
            batteryRemain = 0.0f;
        }
        return new ResultUtil<Float>().setData(batteryRemain);
    }

    @RequestMapping(value = "/autoDockStatus", method = RequestMethod.GET)
    @ApiOperation("获取自充控制状态")
    public Result<Boolean> getAutoDockStatus() {
        boolean res = rosWebService.autoDockStatus();
        return new ResultUtil<Boolean>().setData(res);
    }

    @RequestMapping(value = "/settingResume", method = RequestMethod.GET)
    @ApiOperation(value = "设置恢复运行")
    public Result<Boolean> settingResume() {
        //需要扩展业务代码
        return new ResultUtil<Boolean>().setData(true, "设置恢复运行成功");
    }

    @RequestMapping(value = "/getSpecialModeInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取特殊模式信息")
    public Result<SpecialModeVo> getSpecialModeInfo() {
        SpecialModeVo specialModeVo = new SpecialModeVo();
        Object festival = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, FESTIVAL);
        specialModeVo.setFestival(festival == null ? null : festival.toString());
        Object backgroundMusic = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, BACKGROUND_MUSIC);
        specialModeVo.setBackgroundMusic(backgroundMusic == null ? null : backgroundMusic.toString());
        Object screenText = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SCREEN_TEXT);
        specialModeVo.setText(screenText == null ? null : screenText.toString());
        return new ResultUtil<SpecialModeVo>().setData(specialModeVo);
    }

    @RequestMapping(value = "/getFestivalInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取选择的节日信息")
    public Result<String> chooseFestival() {
        String festival = null;
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, FESTIVAL);
        if (o != null) {
            festival = o.toString();
        }
        return new ResultUtil<String>().setData(festival, "操作成功");
    }

    @RequestMapping(value = "/chooseMusic", method = RequestMethod.GET)
    @ApiOperation(value = "选择音乐")
    public Result<Boolean> chooseMusic(@RequestParam String name) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, BACKGROUND_MUSIC, name);
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/settingVolumeSize", method = RequestMethod.GET)
    @ApiOperation(value = "设置音量大小")
    @ApiImplicitParam(name = "size", value = "音量大小:0~100", dataType = "int")
    public Result<Boolean> settingVolumeSize(@RequestParam Integer size) {
        rosWebService.volumeCtrl(size);
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/settingNavBar", method = RequestMethod.GET)
    @ApiOperation(value = "设置导航栏")
    public Result<Boolean> settingNavBar(@RequestParam Boolean status) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, NAV_BAR, status.toString());
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/extractMode", method = RequestMethod.GET)
    @ApiOperation(value = "设置取物模式")
    public Result<Boolean> extractMode(@RequestParam Boolean status) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, EXTRACT_MODE, status.toString());
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/settingLockScreenPwd", method = RequestMethod.GET)
    @ApiOperation(value = "设置锁屏密码")
    public Result<Boolean> settingLockScreenPwd(@RequestParam Boolean status) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, LOCK_SCREEN_PWD, status.toString());
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/settingSpeedLevel", method = RequestMethod.GET)
    @ApiOperation(value = "设置速度等级(1 代表低速 2 中速 3 高速)")
    @ApiImplicitParam(name = "level", value = "速度等级", dataType = "String",
            paramType = "query", allowableValues = "1,2,3")
    public Result<Boolean> settingSpeedLevel(@RequestParam String level) {
        RedisUtil.hset(ROBOT_FUNCTION,CURRENT_SPEED,level);
        rosWebService.syncSpeedLevel();
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }


    @RequestMapping(value = "/getRobotSettingInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取系统设置信息")
    public Result<RobotSettingVo> getRobotSettingInfo() {
        RobotSettingVo robotSettingVo = rosWebService.getRobotSettingInfo();
        return new ResultUtil<RobotSettingVo>().setData(robotSettingVo, "操作成功");
    }

    @RequestMapping(value = "/getDisinfectSettingInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取消毒设置信息")
    public Result<DisinfectSettingVo> getDisinfectSettingInfo() {
        DisinfectSettingVo disinfectSettingVo = rosWebService.getDisinfectSettingInfo();
        return new ResultUtil<DisinfectSettingVo>().setData(disinfectSettingVo, "操作成功");
    }

    @RequestMapping(value = "/settingDisinfectInfo", method = RequestMethod.POST)
    @ApiOperation(value = "修改消毒设置信息")
    public Result<Boolean> settingDisinfectInfo(@RequestBody DisinfectSettingVo disinfectSettingVo) {
        Result<Boolean> result = rosWebService.settingDisinfectInfo(disinfectSettingVo);
        return result;
    }

    @RequestMapping(value = "/syncRobotDeviceDataToYun", method = RequestMethod.GET)
    @ApiOperation(value = "同步机器人设备数据到云端")
    @ApiImplicitParam(name = "mapIds", value = "地图ID", dataType = "String",
            paramType = "query")
    public Result<Boolean> syncRobotDeviceDataToYun(@RequestParam String mapIds) {
        boolean res = dataSyncService.syncRobotDeviceDataToYun(mapIds);
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/syncRobotDeviceDataToLocal", method = RequestMethod.GET)
    @ApiOperation(value = "同步机器人设备数据到本地")
    @ApiImplicitParam(name = "mapIds", value = "地图ID", dataType = "String",
            paramType = "query")
    public Result<Boolean> syncRobotDeviceDataToLocal(@RequestParam String mapIds) {
        boolean res = dataSyncService.syncRobotDeviceDataToLocal(mapIds);
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/updateRobotDeviceInfo", method = RequestMethod.GET)
    @ApiOperation(value = "更新机器人信息")
    public Result<Boolean> updateRobtDeviceInfo(@RequestParam String key, @RequestParam String value) {
        boolean res = iRobotDefineService.updateDeviceInfo(key, value);
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/getRobotDeviceInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取机器人信息")
    public Result<Map<String, Object>> getRobotDeviceInfo() {
        Map<String, Object> deviceInfo = iRobotDefineService.getDeviceInfo();
        return new ResultUtil<Map<String, Object>>().setData(deviceInfo);
    }

    @RequestMapping(value = "/disinfectCtrl", method = RequestMethod.GET)
    @ApiOperation(value = "消毒设备控制")
    public Result<Boolean> disinfectCtrl(@RequestParam boolean data, @RequestParam Integer type) {
        Float batteryPercentage = iRobotStatusService.getBatteryPercentage();
        if (ObjectUtil.isNotNull(batteryPercentage) && batteryPercentage <= 20) {
            return ResultUtil.error("当前电量不足，无法进行消毒工作");
        }
        if (type.equals(DisinfectSwitch.SPRAY_CTRL.getValue())) {
            ControlStatusConstants.DEV_TASK_NEED_STATUS_SPRAY = data;
            rosWebService.sendDisinfectPrompt(data, DisinfectSwitch.SPRAY_CTRL);
        }
        if (type.equals(DisinfectSwitch.ULRAY_CTRL.getValue())) {
            ControlStatusConstants.DEV_TASK_NEED_STATUS_ULTRAVIOLET = data;
            rosWebService.sendDisinfectPrompt(data, DisinfectSwitch.ULRAY_CTRL);
        }
        if (type.equals(DisinfectSwitch.FAN_CTRL.getValue())) {
            ControlStatusConstants.DEV_TASK_NEED_STATUS_FAN = data;
            rosWebService.sendDisinfectPrompt(data, DisinfectSwitch.FAN_CTRL);
        }
        if (type.equals(DisinfectSwitch.PULSE_CTRL.getValue())) {
            ControlStatusConstants.DEV_TASK_NEED_STATUS_PULSE = data;
            rosWebService.sendDisinfectPrompt(data, DisinfectSwitch.PULSE_CTRL);
        }
        if (type.equals(DisinfectSwitch.SHIELDING_CTRL.getValue())) {
            ControlStatusConstants.DEV_TASK_NEED_STATUS_SHIELDING = data;
            rosWebService.sendDisinfectPrompt(data, DisinfectSwitch.SHIELDING_CTRL);
        }
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/getRobotPose", method = RequestMethod.GET)
    @ApiOperation(value = "获取机器人位置")
    public Result<_Pose> getRobotPose() {
        _Pose robotPose = rosWebService.getRobotPose().pose;
        return new ResultUtil<_Pose>().setData(robotPose, "操作成功");
    }

    @RequestMapping(value = "/getRobotScan", method = RequestMethod.GET)
    @ApiOperation(value = "获取雷达消息")
    public Result<_LaserScan> getRobotScan() {
        _LaserScan robotScan = iRobotMapService.getRobotScan();
        return new ResultUtil<_LaserScan>().setData(robotScan, "操作成功");
    }

    @RequestMapping(value = "/getAutoStartTime", method = RequestMethod.GET)
    @ApiOperation(value = "获取自动重启时间")
    public Result<String> getAutoStartTime() {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AUTO_START_TIME);
        if (o != null && StrUtil.isNotBlank(o.toString())) {
            return new ResultUtil<String>().setData(o.toString(), "操作成功");
        } else {
            return new ResultUtil<String>().setData("", "操作成功");
        }
    }

    @RequestMapping(value = "/settingAutoStartTime", method = RequestMethod.GET)
    @ApiOperation(value = "设置自动重启时间")
    public Result<Boolean> settingAutoStartTime(@RequestParam String time) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, AUTO_START_TIME, time);
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/getAvoidType", method = RequestMethod.GET)
    @ApiOperation(value = "获取避障类型")
    public Result<Integer> getAvoidType() {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AVOID_TYPE);
        if (o != null && StrUtil.isNotBlank(o.toString())) {
            return new ResultUtil<Integer>().setData(Integer.valueOf(o.toString()), "操作成功");
        } else {
            return new ResultUtil<Integer>().setData(RouteAvoidType.NOT_AUTO_AVOID.getType(), "操作成功");
        }
    }

    @RequestMapping(value = "/settingAvoidType", method = RequestMethod.GET)
    @ApiOperation(value = "设置避障类型")
    public Result<Boolean> settingAvoidType(@RequestParam Integer type) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, AVOID_TYPE, type.toString());
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @RequestMapping(value = "/getMonitorInfo", method = RequestMethod.GET)
    @ApiOperation(value = "获取监控信息")
    public Result<SysMonitor> getMonitoryInfo() {
        SysMonitor monitorInfo = rosWebService.getMonitorInfo();
        if (monitorInfo != null) {
            return new ResultUtil<SysMonitor>().setData(monitorInfo, "操作成功");
        } else {
            return new ResultUtil<SysMonitor>().setErrorMsg("操作失败");
        }
    }

//    @RequestMapping(value = "/getBatteryInfo", method = RequestMethod.GET)
//    @ApiOperation(value = "获取电池信息")
//    public Result<_BatteryHealthState> getBatteryInfo() {
//        String s = redisTemplate.opsForValue().get(TOPIC + "::" + BATTERY_HEALTH_STATE);
//        if(StrUtil.isNotBlank(s)) {
//            _BatteryHealthState batteryHealthState = JSONObject.parseObject(s, _BatteryHealthState.class);
//            return new ResultUtil<_BatteryHealthState>().setData(batteryHealthState);
//        } else {
//            return new ResultUtil<_BatteryHealthState>().setErrorMsg("无法获取电池信息");
//        }
//    }

    @RequestMapping(value = "/getRobotLocation", method = RequestMethod.GET)
    @ApiOperation(value = "获取机器人位置信息")
    public Result<String> getRobotLocation() {
        String robotLocation = rosWebService.getRobotLocation();
        return new ResultUtil<String>().setData(robotLocation);
    }

    @RequestMapping(value = "/getSystemCheck", method = RequestMethod.GET)
    @ApiOperation(value = "获取系统检测信息")
    public Result<_SystemCheck> getSystemCheck() {
        String s = redisTemplate.opsForValue().get(TOPIC + "::" + SYSTEM_CHECK);
        if (StrUtil.isNotBlank(s)) {
            _SystemCheck systemCheck = JSONObject.parseObject(s, _SystemCheck.class);
            return new ResultUtil<_SystemCheck>().setData(systemCheck);
        } else {
            return new ResultUtil<_SystemCheck>().setErrorMsg("无法获取自检信息");
        }
    }

    @RequestMapping(value = "/handleSqlData", method = RequestMethod.GET)
    @ApiOperation(value = "处理sql数据")
    public Result<Boolean> handleSqlData(@RequestParam(required = false) String param1, @RequestParam(required = false) String param2) {
        dataSyncService.handleDeviceSqlData(param1, param2);
        return new ResultUtil<Boolean>().setData(true, "操作成功");
    }

    @PostMapping("/directionControl")
    @ApiOperation("机器人方向控制")
    public Result<Boolean> directionControl(@RequestBody DirectionalControlDto directionalControlDto) {
        // 控制机器人运动，必须让机器人使能
        iRobotMotorService.motorControl(true);
        if (iRobotDefineService.directionControl(directionalControlDto)) {
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } else {
            return new ResultUtil<Boolean>().setData(false, "操作失败");
        }
    }

    @GetMapping("/motorControl")
    @ApiOperation("机器人电机控制")
    @ApiImplicitParam(name = "operate", value = "电机操作值", allowableValues = "true,false")
    public Result<Boolean> motorControl(@RequestParam boolean operate) {

        if (iRobotMotorService.motorControl(operate)) {
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } else {
            return new ResultUtil<Boolean>().setData(false, "操作失败");
        }
    }

    @GetMapping("/getVersion")
    @ApiOperation("获取机器人版本信息")
    public Result<RobotBaseInfoVo> getVersion() {
        RobotBaseInfoVo robotBaseInfoVo = iRobotBaseInfoService.getBaseInfoFromRobot();
        Result<RobotBaseInfoVo> result = new Result<>();
        result.setResult(robotBaseInfoVo);
        return result;
    }

    @PostMapping("/updateVersion")
    @ApiOperation("更新脚本")
    public Result<Boolean> updateVersion(@RequestBody YxRobotVersionDto yxRobotVersionDto) {
        log.info("更新版本");
        Result<Boolean> result = new Result<>();
        result.setResult(iRobotVersionService.updateVersion(yxRobotVersionDto));
        return result;
    }

    /**
     * 接受并保存电话号码，默认最新的电话为可用
     * @param telephone
     * @return
     */
    @PostMapping("/receiveTelephoneNumber")
    public Result<Boolean> receiveTelephoneNumber(@RequestBody String telephone){
        log.info("接收电话号码");
        Result<Boolean> result = new Result<>();
        QueryWrapper<RobotUser> wrapper = new QueryWrapper<>();
        wrapper.eq("del_flag",0).eq("status",1);
        List<RobotUser> list = iRobotUserService.list(wrapper);
        if(CollectionUtil.isNotEmpty(list)){
            for (RobotUser user:list) {
                if(user.getMobile().equals(telephone)){
                    result.setResult(true);
                    return result;
                }else {
                    //将账号的可用状态改为0：不可用
                    user.setStatus(0);
                    iRobotUserService.updateById(user);
                }
            }
        }
        RobotUser robotUser = new RobotUser();
        robotUser.setMobile(telephone);
        robotUser.setDelFlag(0);
        robotUser.setStatus(1);
        iRobotUserService.save(robotUser);
        result.setResult(true);
        return result;
    }

    /**
     * 雷达控制接口
     *
     * @param control：true开启雷达,结束休眠，false关闭雷达,开始休眠
     * @return
     * @throws InterruptedException
     */
    @GetMapping("/lidarDormancryControl")
    public Result<Boolean> lidarDormancryControl(@RequestParam boolean control) throws InterruptedException {
        log.info("手动操控雷达：" + control);
        CONTROL_SLEEP = true;
        log.info("手动控制休眠状态已解锁！");
        boolean handleLidar = lidarControlUtil.handleLidar(control);
        Result<Boolean> result = new Result<>();
        if (handleLidar) {
            result.setMessage("操作成功！");
        } else {
            result.setMessage("操作失败！");
        }
        //服务调用结果
        result.setResult(handleLidar);
        //请求成功
        result.setSuccess(true);
        return result;
    }

    /**
     * 获取休眠状态接口
     * true 一休眠；false 未休眠
     */
    @GetMapping("/sleepStatue")
    public Result<Boolean> sleepStatue(){
        if (LidarControlUtil.SLEEP.equals(lidarControlUtil.returnLiadrStatue())
                && LidarControlUtil.SLEEP.equals(lidarControlUtil.returnDepthStatue())
                && lidarControlUtil.returnUnderlyingStatue()) {
            log.info("机器已休眠");
            return ResultUtil.data(true,"机器已休眠");
        }else {
            log.info("机器未休眠");
            return ResultUtil.data(false,"机器未休眠");
        }
    }


    /**
     * 休眠功能开启或关闭接口
     * true 开启；false 关闭
     * @param control
     * @return
     */
    @GetMapping("/sleepCtrl")
    public Result<Boolean> sleepCtrl(@RequestParam boolean control){
        RedisUtil.hset(ROBOT_FUNCTION, FUNCTION_RADAR_SLEEP,String.valueOf(control));
        return ResultUtil.success("操作成功");
    }

    /**
     * 获取休眠功能开启或关闭
     * true 开启；false 关闭
     */
    @GetMapping("/getSleepState")
    public Result<Boolean> getSleepState() {
        String isOpen = RedisUtil.getHash(ROBOT_FUNCTION, FUNCTION_RADAR_SLEEP);
        if (Boolean.FALSE.toString().equals(isOpen)) {
            log.info("机器休眠功能未开启");
            return ResultUtil.data(false,"机器休眠功能未开启");
        }else {
            log.info("机器休眠功能已开启");
            return ResultUtil.data(true,"机器休眠功能已开启");
        }
    }

    /**
     * 防跌落开启或关闭在RobotAreasController类中
     */

    /**
     * 液位传感器功能开关
     */
    @GetMapping("/waterLevelCtrl")
    public Result<Boolean> waterLevelCtrl(@RequestParam boolean control) {
        //通知ROS开关液位传感器
        lidarControlUtil.topicPublish(control, OPEN_WATER_LEVEL);
        //存入redis
        RedisUtil.hset(ROBOT_FUNCTION, WATER_LEVEL_OPEN_STATE, String.valueOf(control));
        log.info("液位传感器开关控制！！！");
        return ResultUtil.success("操作成功");
    }

    /**
     * 液位传感器功能状态
     * 话题数据为 true :液位传感器功能为打开状态
     * 话题数据为 false：液位传感器功能为屏蔽状态
     */
    @GetMapping("/getWaterLevelState")
    public Result<Boolean> getWaterLevelState() {
        String topicValue = RedisUtil.getTopicValue(WATER_LEVEL_STATE);
        _Bool bool = JSONObject.parseObject(topicValue, _Bool.class);
        return ResultUtil.data(bool.data);
    }

    /**
     * 活物检测开关
     */
    @GetMapping("/livingThingsUltravioletCtrl")
    public Result<Boolean> livingThingsUltravioletCtrl(@RequestParam boolean control) {
        //存入redis
        RedisUtil.hset(ROBOT_SYS_INFO, LIVING_THINGS_ULRAY, String.valueOf(control));
        log.info("活物检测开关控制:"+control);
        return ResultUtil.success("操作成功");
    }

    /**
     * 活物检测开启状态
     */
    @GetMapping("/getLivingThingsUltravioletState")
    public Result<Boolean> getLivingThingsUltravioletState(){
        Object livingThingsUlrayObj = RedisUtil.getHash(ROBOT_SYS_INFO, LIVING_THINGS_ULRAY);
        if(ObjectUtil.isNotNull(livingThingsUlrayObj)){
            String s = String.valueOf(livingThingsUlrayObj);
            return ResultUtil.data(Boolean.valueOf(s));
        }else {
            return ResultUtil.error("无活物检测功能");
        }
    }

    /**
     * 语音对话功能开关
     * @param control
     * @return
     */
    @GetMapping("/voiceCtrl")
    public Result<Boolean> voiceCtrl(@RequestParam boolean control){
        //通知ROS开关语音对话
        lidarControlUtil.topicPublish(control, OPEN_VOICE_MODULE);
        //存入redis
        RedisUtil.hset(ROBOT_FUNCTION, VOICE_MODEL_OPEN_STATE, String.valueOf(control));
        log.info("语音对话开关控制！！！");
        return ResultUtil.success("操作成功");
    }

    /**
     * 语音对话功能状态
     * 话题数据为 true : 语音对话功能为打开状态
     * 话题数据为 false：语音对话功能为屏蔽状态
     */
    @GetMapping("/getVoiceState")
    public Result<Boolean> getVoiceState() {
        String topicValue = RedisUtil.getTopicValue(VOICE_MODEL_STATE);
        _Bool bool = JSONObject.parseObject(topicValue, _Bool.class);
        return ResultUtil.data(bool.data);
    }

    /**
     * 休眠功能开启或关闭接口
     * true 开启；false 关闭
     * @param control
     * @return
     */
    @GetMapping("/testRestartCtrl")
    public Result<Boolean> testRestartCtrl(@RequestParam Boolean control){
        RedisUtil.hset(ROBOT_FUNCTION, FIXED_TIME_RESTART_ROBOT, String.valueOf(control));
        return ResultUtil.success("操作成功");
    }

}
