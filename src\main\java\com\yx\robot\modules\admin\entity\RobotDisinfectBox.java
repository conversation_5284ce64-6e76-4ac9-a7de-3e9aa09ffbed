package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_disinfect_box")
@ApiModel(value = "机器人消毒仓条目")
public class RobotDisinfectBox extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "消毒仓名称")
    private String name;

    @ApiModelProperty(value = "地图Id")
    private String mapId;

    @ApiModelProperty(value = "消毒仓外部停靠点1")
    private String positionAId;

    @ApiModelProperty(value = "消毒仓内部停靠点")
    private String positionOId;

    @ApiModelProperty(value = "消毒仓外部停靠点2")
    private String positionBId;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;

}