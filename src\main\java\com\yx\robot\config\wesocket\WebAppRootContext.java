package com.yx.robot.config.wesocket;

import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.web.servlet.ServletContextInitializer;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.util.WebAppRootListener;

import javax.servlet.ServletContext;
import javax.servlet.ServletException;

@Configuration
@ComponentScan
@EnableAutoConfiguration
public class WebAppRootContext implements ServletContextInitializer {
    @Override
    public void onStartup(ServletContext servletContext) throws ServletException {
    System.out.println("org.apache.tomcat.websocket.textBufferSize");
    servletContext.addListener(WebAppRootListener.class);
    servletContext.setInitParameter("org.apache.tomcat.websocket.textBufferSize","102400000");
    }
}
