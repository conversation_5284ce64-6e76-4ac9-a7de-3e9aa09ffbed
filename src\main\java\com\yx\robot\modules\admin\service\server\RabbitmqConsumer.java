//package com.yx.robot.modules.admin.service.server;
//
//import org.springframework.amqp.rabbit.annotation.RabbitHandler;
//import org.springframework.amqp.rabbit.annotation.RabbitListener;
//import org.springframework.stereotype.Component;
//
//import static com.yx.robot.common.constant.RabbitMqConstants.VERSION_UPDATE_QUEUE;
//
///**
// * <AUTHOR>
// * @version 1.0
// * @date 2022/7/6 13:29
// */
//@Component
//@RabbitListener(queues = VERSION_UPDATE_QUEUE)
//public class RabbitmqConsumer {
//
//    @RabbitHandler
//    public void onMassage(String msg) {
//        System.out.println("我收到消息啦!" + msg);
//    }
//}
