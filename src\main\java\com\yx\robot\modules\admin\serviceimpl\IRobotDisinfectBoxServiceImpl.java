package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.enums.TaskType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotDisinfectBoxMapper;
import com.yx.robot.modules.admin.entity.RobotDisinfectBox;
import com.yx.robot.modules.admin.entity.RobotMap;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.jike.constant.JikeConstants;
import com.yx.robot.modules.admin.jike.dto.*;
import com.yx.robot.modules.admin.jike.response.GetDisinfectBoxRep;
import com.yx.robot.modules.admin.jike.enums.RespCodeEnum;
import com.yx.robot.modules.admin.jike.response.*;
import com.yx.robot.modules.admin.jike.service.JikePlatformService;
import com.yx.robot.modules.admin.service.IRobotDisinfectBoxService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.service.IRobotMapService;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

import static com.yx.robot.common.constant.RosWebConstants.CURRENT_MAP;
import static com.yx.robot.common.constant.RosWebConstants.ROBOT_SYS_INFO;
import static com.yx.robot.modules.admin.jike.constant.JikeConstants.JIKE;
import static com.yx.robot.modules.admin.jike.constant.JikeConstants.UVR_UC_TIME;

/**
 * 机器人消毒仓条目    接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotDisinfectBoxServiceImpl extends ServiceImpl<RobotDisinfectBoxMapper, RobotDisinfectBox> implements IRobotDisinfectBoxService {

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private JikePlatformService jikePlatformService;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotMapService iRobotMapService;

//    @Autowired
//    public IRobotDisinfectBoxServiceImpl(IRobotPositionService iRobotPositionService, JikePlatformService jikePlatformService, RosWebService rosWebService, IRobotDisinfectBoxService iRobotDisinfectBoxService, IRobotMapService iRobotMapService) {
//        this.iRobotPositionService = iRobotPositionService;
//        this.jikePlatformService = jikePlatformService;
//        this.rosWebService = rosWebService;
//        this.iRobotDisinfectBoxService = iRobotDisinfectBoxService;
//        this.iRobotMapService = iRobotMapService;
//    }


    /**
     * 开始执行消毒仓任务
     */
    @Override
    public void startDisinfectBoxTask() {
        Jedis jedis = null;
        try {
            GetDisinfectBoxDto getDisinfectBoxDto = new GetDisinfectBoxDto();
            jedis = RedisUtil.getJedis();
            assert jedis != null;
            String currentMapId = jedis.hget(ROBOT_SYS_INFO, CURRENT_MAP);
            RobotMap currentMap = iRobotMapService.getById(currentMapId);
            if (currentMap != null) {
                Integer floor = currentMap.getFloor();
                getDisinfectBoxDto.setFloorId(JikeConstants.BULID_NO + "-" + floor + "F");
            }
            GetDisinfectBoxRep disinfectBoxRep = jikePlatformService.getDisinfectBox(getDisinfectBoxDto);
            log.info(JSONObject.toJSONString(disinfectBoxRep));
            if (disinfectBoxRep != null && disinfectBoxRep.getRespCode().toString().equals(RespCodeEnum.SUCCESS.getCode())) {
                log.info("获取空闲消毒仓成功");
                String pointId = disinfectBoxRep.getData().getPointId();
                if (StrUtil.isNotBlank(pointId)) {
                    jedis.set(JikeConstants.JIKE + "::" + JikeConstants.UVR_POINT_ID, pointId);
                }
            } else {
                jedis.set(JikeConstants.JIKE + "::" + JikeConstants.UVR_POINT_ID, "");
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        List<RobotPosition> robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getName, "自定义区域A"));
        if (CollectionUtil.isNotEmpty(robotPositionList)) {
            RobotPosition robotPosition = robotPositionList.get(0);
            RobotTask robotTask = rosWebService.addRobotTask(TaskType.DISINFECT_BOX.getValue(), 1, TaskType.DISINFECT_BOX.getType(), null);
            List<RobotDisinfectBox> robotDisinfectBoxList = this.list(new LambdaQueryWrapper<RobotDisinfectBox>().eq(RobotDisinfectBox::getName, robotPosition.getName()));
            if (CollectionUtil.isNotEmpty(robotDisinfectBoxList)) {
                RobotDisinfectBox robotDisinfectBox = robotDisinfectBoxList.get(0);
                Map<String, Object> positionIds = new LinkedHashMap<>();
                positionIds.put("1", robotDisinfectBox.getPositionAId());
                positionIds.put("2", robotDisinfectBox.getPositionOId());
                positionIds.put("3", robotDisinfectBox.getPositionBId());
                rosWebService.addPointToRedis(positionIds, robotTask.getId());
            }
        }
    }

    /**
     * 标记消毒仓点位处理类
     *
     * @param robotPosition 机器人位置信息
     */
    @Override
    public void handleMarkDisinfectBoxPosition(RobotPosition robotPosition) {
        if (robotPosition.getType().equals(RobotPositionType.INSIDE_DISINFECT_BOX_POSITION.getType())
                || robotPosition.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_1.getType())
                || robotPosition.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_2.getType())
        ) {
            RobotDisinfectBox robotDisinfectBox;
            List<RobotDisinfectBox> robotDisinfectBoxList = this.list(new LambdaQueryWrapper<RobotDisinfectBox>().eq(RobotDisinfectBox::getName, robotPosition.getName()));
            if (CollectionUtil.isNotEmpty(robotDisinfectBoxList)) {
                robotDisinfectBox = robotDisinfectBoxList.get(0);
            } else {
                robotDisinfectBox = new RobotDisinfectBox();
            }
            robotDisinfectBox.setName(robotPosition.getName());
            robotDisinfectBox.setMapId(robotPosition.getMapId());
            if (robotPosition.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_1.getType())) {
                robotDisinfectBox.setPositionAId(robotPosition.getId());
            }
            if (robotPosition.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_2.getType())) {
                robotDisinfectBox.setPositionBId(robotPosition.getId());
            }
            if (robotPosition.getType().equals(RobotPositionType.INSIDE_DISINFECT_BOX_POSITION.getType())) {
                robotDisinfectBox.setPositionOId(robotPosition.getId());
            }
            this.saveOrUpdate(robotDisinfectBox);
        }
    }

    /**
     * 处理到达消毒仓点位
     *
     * @param taskId     任务taskId
     * @param positionId 位置ID
     * @return true / false
     */
    @Override
    public boolean handleArriveDisinfectBoxPosition(String taskId, String positionId) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            RobotPosition target = iRobotPositionService.getById(positionId);
            assert jedis != null;
            String uvrPointId = jedis.get(JIKE + "::" + JikeConstants.UVR_POINT_ID);
            if (StrUtil.isBlank(uvrPointId)) {
                log.info("无法获取空闲消毒仓");
                return false;
            }
            // 到达消毒仓外1
            if (target.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_1.getType())) {
                log.info("到达消毒仓外1");
                OpenDisinfectBoxDoorDto openDisinfectBoxDoorDto = new OpenDisinfectBoxDoorDto();
                openDisinfectBoxDoorDto.setPointId(uvrPointId);
                OpenDisinfectBoxDoorResponse openDisinfectBoxDoorResponse = jikePlatformService.openDisinfectBoxDoor(openDisinfectBoxDoorDto);
                if (openDisinfectBoxDoorResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                    log.info("污染区开门成功");
                    return true;
                }
            }
            // 到达仓位内部点
            if (target.getType().equals(RobotPositionType.INSIDE_DISINFECT_BOX_POSITION.getType())) {
                log.info("到达消毒仓内");
                CloseDisinfectBoxDoorDto closeDisinfectBoxDoorDto = new CloseDisinfectBoxDoorDto();
                closeDisinfectBoxDoorDto.setPointId(uvrPointId);
                CloseDisinfectBoxDoorResponse closeDisinfectBoxDoorResponse = jikePlatformService.closeDisinfectBoxDoor(closeDisinfectBoxDoorDto);
                if (closeDisinfectBoxDoorResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                    log.info("污染区关门成功");
                    log.info("开始执行消毒仓消杀任务.......");
                    Thread.sleep(UVR_UC_TIME * 1000);
                    log.info("结束执行消毒仓消杀任务.......");
                }
            }
            // 到达出仓位点2
            if (target.getType().equals(RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_2.getType())) {
                log.info("到达消毒仓外2");
                CloseDisinfectCleanDoorDto closeDisinfectCleanDoorDto = new CloseDisinfectCleanDoorDto();
                closeDisinfectCleanDoorDto.setPointId(uvrPointId);
                CloseDisinfectCleanDoorResponse closeDisinfectCleanDoorResponse = jikePlatformService.closeDisinfectCleanDoor(closeDisinfectCleanDoorDto);
                if (closeDisinfectCleanDoorResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                    log.info("清洁区关门成功");
                    NotifyDisinfectBoxFinishDto notifyDisinfectBoxFinishDto = new NotifyDisinfectBoxFinishDto();
                    notifyDisinfectBoxFinishDto.setTaskKey(taskId);
                    NotifyDisinfectBoxFinishResponse notifyDisinfectBoxFinishResponse = jikePlatformService.notifyDisinfectBoxFinishResponse(notifyDisinfectBoxFinishDto);
                    if (notifyDisinfectBoxFinishResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                        log.info("消毒仓上报成功");
                    }
                    return true;
                }

            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return false;
    }
}