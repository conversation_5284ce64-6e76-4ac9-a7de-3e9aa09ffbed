package com.yx.robot.modules.admin.serviceimpl;

import com.yx.robot.modules.admin.dao.mapper.RobotCheckDefineMapper;
import com.yx.robot.modules.admin.entity.RobotCheckDefine;
import com.yx.robot.modules.admin.service.IRobotCheckDefineService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 机器人检测状态定义接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotCheckDefineServiceImpl extends ServiceImpl<RobotCheckDefineMapper, RobotCheckDefine> implements IRobotCheckDefineService {

    @Autowired
    private RobotCheckDefineMapper robotCheckDefineMapper;
}