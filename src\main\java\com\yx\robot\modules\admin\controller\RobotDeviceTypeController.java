package com.yx.robot.modules.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotDeviceType;
import com.yx.robot.modules.admin.service.IRobotDeviceTypeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "设备和点位关联信息管理接口")
@RequestMapping("/yx/robotDeviceType")
@Transactional
public class RobotDeviceTypeController {

    @Autowired
    private IRobotDeviceTypeService iRobotDeviceTypeService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotDeviceType> get(@PathVariable String id) {

        RobotDeviceType robotDeviceType = iRobotDeviceTypeService.getById(id);
        return new ResultUtil<RobotDeviceType>().setData(robotDeviceType);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotDeviceType>> getAll() {

        List<RobotDeviceType> list = iRobotDeviceTypeService.list();
        return new ResultUtil<List<RobotDeviceType>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotDeviceType>> getByPage(@ModelAttribute PageVo page) {
        IPage<RobotDeviceType> data = iRobotDeviceTypeService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotDeviceType>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotDeviceType> saveOrUpdate(@ModelAttribute RobotDeviceType robotDeviceType) {

        if (iRobotDeviceTypeService.saveOrUpdate(robotDeviceType)) {
            return new ResultUtil<RobotDeviceType>().setData(robotDeviceType);
        }
        return new ResultUtil<RobotDeviceType>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {
        for (String id : ids) {
            iRobotDeviceTypeService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }

    @GetMapping("/getRobotDeviceTypeByParentType/{type}")
    @ApiOperation("传值为-1，则查询所有父类型，传值为其他（如0，1，2等）父类型，则根据父类型查询所有子类型")
    public Result<List<RobotDeviceType>> getRobotDeviceTypeByParentType(@PathVariable Integer type) {
        List<RobotDeviceType> list = iRobotDeviceTypeService.getList(type);
        return new ResultUtil<List<RobotDeviceType>>().setData(list);
    }
}
