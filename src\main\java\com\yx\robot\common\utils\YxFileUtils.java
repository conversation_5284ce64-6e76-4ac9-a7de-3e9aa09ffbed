package com.yx.robot.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;

import java.io.File;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/4 13:02
 */
public class YxFileUtils {

    public static void saveLast(String filePath) {
        int days = ObjectUtil.isNull(YmlUtils.getValue("hk.saveDays")) ? 7 : Integer.parseInt(YmlUtils.getValue("hk.saveDays") + "");
        List<File> list = FileUtil.loopFiles(filePath);
        List<String> pathList = list.stream().map(File::getParentFile)
                .map(File::getAbsolutePath).distinct()
                .sorted()
                .collect(Collectors.toList());
        if (pathList.size() > days) {
            for (int i = 0; i < pathList.size(); i++) {
                if (i > days - 1) {
                    String path = pathList.get(pathList.size() - i - 1);
                    FileUtil.del(path);
                }
            }
        }
    }

    public static void main(String[] args) {
//        saveLast("E:/home/<USER>/hk/video");
        int days = ObjectUtil.isNull(YmlUtils.getProperties("hk.saveDays")) ? 7 : Integer.parseInt(YmlUtils.getProperties("hk.saveDays") + "");
        System.out.println(days);
    }

}
