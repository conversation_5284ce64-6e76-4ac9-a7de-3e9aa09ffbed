package com.yx.robot.modules.admin.vo;

import lombok.Data;

/**
 * 机器人相关信息
 *
 * <AUTHOR>
 * @date 2020/1/28
 */
@Data
public class RobotInfoVo {

    /**
     * 机器人型号和编号
     */
    private String deviceTypeCode;

    /**
     * 是否在线
     */
    private Boolean status;

    /**
     * 电量
     */
    private Float battery;

    /**
     * 雾化喷雾状态
     */
    private Boolean isSprayWarning;

    /**
     * 急停按钮
     */
    private Boolean isStop;

    /**
     * 防撞条
     */
    private Boolean isCollision;

    /**
     * 是否空闲
     */
    private Boolean isIdle;

    /**
     * 是否包含地图
     */
    private Boolean hasMap;

    /**
     * 是否在建图
     */
    private Boolean isMapping;

    /**
     * 是否在充电
     */
    private Boolean isCharging;

    /**
     * 机器人运行状态 0：空闲，1:工作中，2:暂停中,3: 正在对接充电桩
     */
    private Integer workingOperationStatus;

    /**
     * 机器人雷达休眠状态：true:休眠；false:未休眠;
     */
    private Boolean lidarStatue;

    /**
     * 机器人磁盘信息
     */
    private Double diskInformation;

    /**
     * 机器人电池充电次数百分比
     */
    private String batteryPercent;

    /**
     * U3pro脉冲灯管使用剩余寿命
     */
    private String pulseSurplusHours;

    /**
     * 防撞条开启状态(U3没有防撞条默认关闭)
     */
    private Boolean collisionState = false;

    /**
     * 休眠模式开启状态
     */
    private Boolean sleepState;

}
