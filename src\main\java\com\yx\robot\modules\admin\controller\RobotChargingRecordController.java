package com.yx.robot.modules.admin.controller;

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotChargingRecord;
import com.yx.robot.modules.admin.service.IRobotChargingRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;

import com.yx.robot.modules.admin.vo.RobotChargingRecordAppVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人充电记录管理接口")
@RequestMapping("/yx/api-v1/robotChargingRecord")
@Transactional
public class RobotChargingRecordController {

    @Autowired
    private IRobotChargingRecordService iRobotChargingRecordService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotChargingRecord> get(@PathVariable String id){

        RobotChargingRecord robotChargingRecord = iRobotChargingRecordService.getById(id);
        return new ResultUtil<RobotChargingRecord>().setData(robotChargingRecord);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotChargingRecord>> getAll(){

        List<RobotChargingRecord> list = iRobotChargingRecordService.list();
        return new ResultUtil<List<RobotChargingRecord>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotChargingRecord>> getByPage(@ModelAttribute PageVo page){
        if (StringUtils.isBlank(page.getSortOrigin())){
            page.setSort("start_time");
        }
        IPage<RobotChargingRecord> data = iRobotChargingRecordService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotChargingRecord>>().setData(data);
    }



    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotChargingRecord> saveOrUpdate(@ModelAttribute RobotChargingRecord robotChargingRecord){

        if(iRobotChargingRecordService.saveOrUpdate(robotChargingRecord)){
            return new ResultUtil<RobotChargingRecord>().setData(robotChargingRecord);
        }
        return new ResultUtil<RobotChargingRecord>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotChargingRecordService.removeById(id);
        }
        return new ResultUtil<>().setSuccessMsg("批量通过id删除数据成功");
    }
}
