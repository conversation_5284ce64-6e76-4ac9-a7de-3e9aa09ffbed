package com.yx.robot.modules.admin.controller;

import com.baomidou.mybatisplus.extension.api.R;
import com.yx.robot.common.hk.HkUtils;
import com.yx.robot.common.vo.Result;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/1 11:39
 */
@Slf4j
@RestController
@Api("海康摄像头操作接口")
@RequestMapping("/yx/api-v1/hk")
public class HkController {

    @ApiOperation(value = "开始录制")
    @GetMapping("/startRecord")
    public Result<String> startRecord(@RequestParam String fileName) {
        int userHandler = HkUtils.preRecord();
        if (StringUtils.isBlank(fileName)) {
            fileName = UUID.randomUUID().toString();
        }
        String filePath = HkUtils.getStartRecord(fileName, userHandler);
        Result<String> result = new Result<>();
        result.setResult(filePath);
        result.setSuccess(StringUtils.isNotBlank(filePath));
        return result;
    }

    @ApiOperation(value = "结束录制")
    @GetMapping("/endRecord")
    public void endRecord() {
        HkUtils.endRecord();
    }
}
