package com.yx.robot.modules.admin.dto;

import lombok.Data;

/**
 * 消毒手动操作
 * <AUTHOR>
 * @date 2020/09/19
 */
@Data
public class DisinfectManualOperationDto {

    /**
     * 操作(1:开启 0:关闭)
     */
    private Integer operation;

    /**
     * 喷雾
     */
    private boolean spray;

    /**
     * 紫外
     */
    private boolean ulray;

    /**
     * 消毒模块
     */
    private boolean xt;

    /**
     * 如果定时开关开启，到达指定时间后开始消毒
     * 如果定时开关未开启，则直接开始消毒
     */
    private boolean fixedTime;

    /**
     * 定时开始时间
     */
    private String startTime;

    /**
     * 持续时间（分钟）
     */
    private Integer time = 1;
}
