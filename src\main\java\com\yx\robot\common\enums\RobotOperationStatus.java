package com.yx.robot.common.enums;

/**
 * 机器人运行状态信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/5/26 9:38
 */
public enum RobotOperationStatus {

    /**
     * 空闲状态
     */
    SPARE(0, "空闲状态"),

    /**
     * 运行中
     */
    RUNNING(1, "运行中"),
    
    /**
     * 任务暂停中
     */
    PAUSE(2, "暂停中"),

    /**
     * 正在充电中
     */
    DOCKING(3, "正在充电中");

    RobotOperationStatus(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    private Integer value;

    private String label;

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
