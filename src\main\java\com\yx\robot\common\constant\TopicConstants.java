package com.yx.robot.common.constant;

/**
 * 话题相关常量
 *
 * <AUTHOR>
 * @date 2020/07/23
 */
public interface TopicConstants {
    /**
     * 机器人在地图中的位置信息
     */
    String ROBOT_POSE = "/robot_pose";

    /**
     * 临时机器人位置信息
     */
    String ROBOT_POSE_STAMPED = "/robot_pose_stamped";

    /**
     * 机器人雷达数据
     */
    String SCAN = "/scan";

    /**
     * 地图
     */
    String MAP_METADATA = "map_metadata";

    /**
     * 机器人路径数据
     */
    String PATH_RECORD = "/path_record";

    /**
     * 导航状态
     */
    String NAV_STATUS = "/nav_manage_node/robot_nav_status";

    /**
     * 电池电源管理
     */
    String BATTERY_STATE = "/battery_state";

    /**
     * 电池健康状态
     */
    //String BATTERY_HEALTH_STATE = "/battery_health_state";
    /**
     * 电机控制
     */
    String MOTOR_CONTROL = "/motor_control";

    /**
     * 重定位控制
     */
    String INIT_POSE = "/initialpose";

    /**
     * 初始化角度
     */
    String INIT_ANGLE = "/init_angle";

    /**
     * 防撞条
     */
    String COLLISION = "/collision";

    /**
     * 进出水控制
     */
    String WATER_CTRL = "/water_ctrl";

    /**
     * 风扇
     */
    String FAN_CTRL = "/fan_ctrl";

    /**
     * 西铭风扇
     */
    String XM_FAN_CTRL = "/ximing_fan_ctrl";

    /**
     * 紫外灯保护
     */
    String SHIELDING_CTRL = "/shielding_ctrl";

    /**
     * 紫外线
     */
    String ULRAY_CTRL = "/ulray_ctrl";

    /**
     * 脉冲灯
     */
    String PULSE_CTRL = "/pulse_ctrl";

    /**
     * 喷雾
     */
    String SPRAY_CTRL = "/spray_ctrl";

    /**
     * 消毒液的量
     */
    String DISINFECTANT = "/wiquid_level";

    /**
     * 喷雾液位状态 告警 （下液位）
     * true:缺液，false:不缺液
     */
    String SPRAY_WIQUID_STATUS = "/spray_wiquid_status";

    /**
     * 消毒按键状态
     */
    String DOCTOR_KEY_STATUS = "/doctor_key_status";

    /**
     * 水箱进控制状态
     */
    String WATER_IN_STATUS = "/water_in_status";

    /**
     * 水箱出控制状态
     */
    String WATER_OUT_STATUS = "/water_out_status";

    /**
     * 风扇状态
     */
    String FAN_STATUS = "/fan_status";

    /**
     * 紫外灯保护状态
     */
    String SHIELDING_STATUS = "/shielding_status";

    /**
     * 紫外线状态
     */
    String ULRAY_STATUS = "/ulray_status";


    /**
     * 脉冲灯状态
     */
    String PULSE_STATUS = "/pulse_status";

    /**
     * 喷雾状态
     */
    String SPRAY_STATUS = "/spray_status";

    /**
     * 活物判断
     */
    String FIND_PEOPLE = "/human_detection_status";

    /**
     * 点位标记
     */
    String POINT_MARKER = "/point_marker";

    /**
     * 急停开关 true:急停被按下，false:未被按下
     */
    String EMERGENCY_BUTTON = "/stop_btn";

    /**
     * 关机状态(发布：5：关机 6：重启)（订阅：1：收到关机指令 2：未知）
     */
    String SYSTEM_CHARGE = "/system_charge";

    /**
     * 红外状态(实时)
     */
    String INFRARED_STATUS = "/infrared_status";

    /**
     * 高低位精度 3代表低精度 1高精度
     */
    String LOCATION_ACCURACY = "/location_accuracy";

    /**
     * 自动充电状态
     */
    String AUTO_DOCK_STATE = "/auto_dock_state";

    /**
     * 是否接触到充电桩，充电片。
     * 接触了不一定在充电，不能作为充电的判断依据。
     */
    String SYSTEM_CHARGE_STATE = "/system_charge_state";

    /**
     * 广告控制
     */
    String AD_CONTROL = "/ad_ctrl";

    /**
     * 开机自检
     */
    String SYSTEM_CHECK = "/system_check";

    /**
     * 消毒模式状态  只针对路径
     */
    String DOCTOR_MODE_STATE = "/doctor_mode_state";

    /**
     * 环形灯控制
     */
    String RING_LIGHT_CTRL = "/ring_light_ctrl";

    /**
     * 声音控制
     */
    String PLAY_VOICE_CTRL = "/play_voice_ctrl";

    /**
     * 停止声音
     */
    String STOP_VOICE_CTRL = "/stop_voice_ctrl";

    /**
     * 速度等级
     */
    String SPEED_LEVEL = "/speed_level";

    /**
     * 音量大小控制
     */
    String SOUNDS_CTRL = "/sounds_ctrl";

    /**
     * 路径点位记录
     */
    String ORIGINAL_PATH_VISUAL = "/original_path_visual";

    /**
     * 地图校准
     */
    String MAP_TRANSFORM = "/map_merge/map_transform";

    /**
     * 清空地图
     */
    String CLEAR_COSTMAPS = "/clear_costmaps";

    /**
     * 充电桩检测
     */
    String GET_DOCK = "/get_dock";

    /**
     * 电梯任务反馈
     */
    String ELEVATOR_TASK_FEEDBACK = "/yl_elevator_collaboration_node/elevator_task_feedback";

    /**
     * 无线网卡重启
     */
    String WIRELESS_RESTART = "/wireless_network_restart";

    /**
     * 点击休眠话题， 休眠 发送false, 唤醒使用true
     */
    String MOTOR_SLEEP = "/motor_sleep";

    /**
     * 高液位报警, true:高液位,  false：没有到达
     */
    String SPRAY_LIQUID_OVERFLOW = "spray_wiquid_overflow";

    /**
     * 方向控制
     */
    String DIRECTIONAL_CONTROL = "cmd_vel";

    /**
     * 语音交流
     * 订阅话题不需要包名
     */
    String TALK_SCENE = "/voice_control_topic";

    /**
     * 点阵表情
     */
    String EXPRESSION_CONTROL="/expression_control_topic";

    /**
     * 充电桩对接状态话题
     * <p>
     * 消息类型：udrive_auto_dock_state.msg
     * <p>
     * string stage 对接充电桩的阶段
     * uint8 error 对接充电桩错误码，正常情况为0
     * bool working 机器人是否处于运动状态
     * bool charged # 机器人是否成功充上电
     * string msg # 提示信息
     */
    String UDRIVE_AUTO_DOCK_STATE = "/udrive_auto_dock_state";

    /**
     * 深度相机数据---用于判断深度相机是否正常
     */
    String IMAGE_RAW = "/camera/depth/image_raw";

    /**
     * 获取电机的故障码，负载率，温度信息话题
     */
    String MOTOR_DATA = "/motor_data_topic";

    /**
     * 防跌落话题
     */
    String MOTOR_LOCK = "/motor_lock";


    /**
     * 液位灯状态控制
     */
    String LIQUID_LIGHT_CONTROL = "/water_light_ctrl";

    /**
     * 电池状态
     */
    String WHOLE_BATTERY_STATE = "/whole_battery_state";

    /**
     * 雷达休眠控制话题
     * true:开启休眠，系统自检话题会接收'sleep'字符串
     * false:关闭休眠，系统自检话题正常接收
     */
    String LIDAR_SLEEP_TOPIC = "/lidar_sleep_topic";

    /**
     * 防跌落开关控制话题
     * 话题数据类型：
     * std_msgs/Bool
     * 向话题里发 true: 打开防跌落功能
     * 向话题里发 false: 关闭防跌落功能
     * 机器默认打开防跌落功能，如果需要关闭直接发 false,想再次打开发 true
     */
    String ROBOT_FALL_ON = "/robot_fall_on";

    /**
     * 防跌落开启状态话题
     * True: 防跌落处于打开状态
     * False：防跌落处于关闭状态
     */
    String ROBOT_FALL_STATE = "/robot_fall_state";

    /**
     * 深度相机休眠控制话题
     * true:开启休眠，系统自检话题会接收'sleep'字符串
     * false:关闭休眠，系统自检话题正常接收
     */
    String DEPTH_CAMERA_SLEEP_TOPIC = "/depth_camera_sleep";

    /**
     * 底层板低功耗控制话题
     * 向话题里面发送 1 开启低功耗
     * 向话题里面发送 0 关闭低功耗
     */
    String LOW_POWER_CTRL= "/low_power_ctrl";

    /**
     * 底层板低功耗状态话题
     * True: 底层板处于低功耗状态
     * False：底层板处于正常状态
     */
    String LOW_POWER_STATE= "/low_power_state";

    /**
     * 液位传感器功能开关话题
     * 向话题里发 false 屏蔽液位传感器功能
     * 向话题里发 true 开启液位传感器功能
     * 液位传感器默认为打开状态，必要时调用话题关闭，关闭后可以调用话题再次打开
     */
    String OPEN_WATER_LEVEL = "/open_water_level";

    /**
     * 液位传感器功能状态话题
     * 话题数据为 true :液位传感器功能为打开状态
     * 话题数据为 false：液位传感器功能为屏蔽状态
     */
    String WATER_LEVEL_STATE = "/water_level_state";

    /**
     * 语音对话功能开关
     * 数据类型： std_msgs/Bool
     * 向话题发 true： 打开语音对话功能
     * 向话题发 false：关闭语音对话功能
     */
    String OPEN_VOICE_MODULE = "/open_voice_module";

    /**
     * 语音对话功能状态
     * 数据为 true：功能打开状态
     * 数据为 false：功能处于关闭状态
     */
    String VOICE_MODEL_STATE = "/voice_model_state";

}
