package com.yx.robot.modules.base.serviceimpl;

import com.yx.robot.modules.base.service.SystemHealthService;
import com.yx.robot.modules.base.vo.RobotCheckVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 系统健康相关服务实现
 * <AUTHOR>
 * @date 2020/09/19
 */
@Slf4j
@Service
@Transactional
public class SystemHealthServiceImpl implements SystemHealthService {
    /**
     * 机器人自检
     *
     * @return
     */
    @Override
    public RobotCheckVo robotCheck() {
        /*RobotCheckVo robotCheckVo = new RobotCheckVo();
        for(RobotAutoCheckType robotAutoCheckType : RobotAutoCheckType.values()) {
            Integer type = robotAutoCheckType.getType();
            List<CheckInfoVo> checkInfoVoList = new ArrayList<>();
            for (RobotCheckProject value : RobotCheckProject.values()) {
                if(value.getType().equals(type)) {
                    CheckInfoVo checkInfoVo = new CheckInfoVo();
                    checkInfoVo.setCode(value.getCode());
                    checkInfoVo.setLabel(value.getValue());
                    checkInfoVo.setType(value.getType());
                    checkInfoVoList.add(checkInfoVo);
                }
            }
            if(type.equals(RobotAutoCheckType.COMMUNICATION_STATUS.getType())) {
                robotCheckVo.setCommunicationStatus(checkInfoVoList);
            }
            if(type.equals(RobotAutoCheckType.POSITION_STATUS.getType())) {
                robotCheckVo.setPositionStatus(checkInfoVoList);
            }
            if(type.equals(RobotAutoCheckType.DEVICE_STATUS.getType())) {
                robotCheckVo.setDeviceStatus(checkInfoVoList);
            }
            if(type.equals(RobotAutoCheckType.CONFIG_STATUS.getType())) {
                robotCheckVo.setConfigStatus(checkInfoVoList);
            }
        }
        return robotCheckVo;*/
        return null;
    }
}
