package com.yx.robot.modules.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yx.robot.common.enums.NavType;
import com.yx.robot.common.enums.TaskType;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotChargingRecord;
import com.yx.robot.modules.admin.entity.RobotDisinfectTaskDetail;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.entity.RobotTaskRecord;
import com.yx.robot.modules.admin.service.IRobotDisinfectTaskDetailService;
import com.yx.robot.modules.admin.service.IRobotTaskRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.modules.admin.service.IRobotTaskService;
import com.yx.robot.modules.admin.vo.RobotDisinfectTaskDetailVo;
import com.yx.robot.modules.admin.vo.RobotDisinfectTaskRecordVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人任务记录管理接口")
@RequestMapping("/yx/api-v1/robotTaskRecord")
@Transactional
public class RobotTaskRecordController {

    @Autowired
    private IRobotTaskRecordService iRobotTaskRecordService;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotDisinfectTaskDetailService iRobotDisinfectTaskDetailService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotTaskRecord> get(@PathVariable String id) {

        RobotTaskRecord robotTaskRecord = iRobotTaskRecordService.getById(id);
        return new ResultUtil<RobotTaskRecord>().setData(robotTaskRecord);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotTaskRecord>> getAll() {

        List<RobotTaskRecord> list = iRobotTaskRecordService.list();
        return new ResultUtil<List<RobotTaskRecord>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotTaskRecord>> getByPage(@ModelAttribute PageVo page) {
        if (StringUtils.isBlank(page.getSortOrigin()) || "create_time".equals(page.getSortOrigin())) {
            page.setSort("start_time");
        }
        IPage<RobotTaskRecord> data = iRobotTaskRecordService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotTaskRecord>>().setData(data);
    }

    @RequestMapping(value = "/disinfect/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "消毒记录分页获取")
    public Result<IPage<RobotDisinfectTaskRecordVo>> getDisinfectByPage(@ModelAttribute PageVo page,
                                                                        @RequestParam(required = false) String startTime,
                                                                        @RequestParam(required = false) String endTime) {
        if (StringUtils.isBlank(page.getSortOrigin()) || "create_time".equals(page.getSortOrigin())) {
            page.setSort("start_time");
        }
        // 只获取消毒相关记录
        LambdaQueryWrapper<RobotTaskRecord> robotTaskRecordLambdaQueryWrapper = new LambdaQueryWrapper();
        List<String> idArr = iRobotTaskService.list(new LambdaQueryWrapper<RobotTask>().eq(RobotTask::getType, TaskType.DISINFECT.getType()))
                .stream().map(RobotTask::getId).collect(Collectors.toList());
        robotTaskRecordLambdaQueryWrapper.in(RobotTaskRecord::getTaskId, idArr);
        if (StrUtil.isNotBlank(startTime)) {
            startTime = startTime.concat(" 00:00:00");
            robotTaskRecordLambdaQueryWrapper.ge(RobotTaskRecord::getStartTime, startTime);
        }
        if (StrUtil.isNotBlank(endTime)) {
            endTime = endTime.concat(" 23:59:59");
            robotTaskRecordLambdaQueryWrapper.le(RobotTaskRecord::getStartTime, endTime);
        }
        IPage<RobotTaskRecord> data = iRobotTaskRecordService.page(PageUtil.initMpPage(page), robotTaskRecordLambdaQueryWrapper);
        IPage<RobotDisinfectTaskRecordVo> data1 = new Page<>(page.getPageNumber(), page.getPageSize());
        data1.setCurrent(data.getCurrent());
        data1.setPages(data.getPages());
        data1.setSize(data.getSize());
        data1.setTotal(data.getTotal());
        List<RobotTaskRecord> records = data.getRecords();
        List<RobotDisinfectTaskRecordVo> disinfectTaskRecordVos = new ArrayList<>();
        for (RobotTaskRecord robotTaskRecord : records) {
            RobotDisinfectTaskRecordVo robotDisinfectTaskRecordVo = new RobotDisinfectTaskRecordVo(robotTaskRecord);
            RobotTask robotTask = iRobotTaskService.getById(robotTaskRecord.getTaskId());
            if (null != robotTask) {
                //任务类型
                Integer type = robotTask.getType();
                for (TaskType taskType : TaskType.values()) {
                    if (taskType.getType().equals(type)) {
                        robotDisinfectTaskRecordVo.setTaskType(taskType.getValue());
                    }
                }
                //任务名称
                robotDisinfectTaskRecordVo.setTaskName(robotTask.getName());
                //任务执行状态描述
                String status = robotTaskRecord.getStatus();
                for (NavType navType : NavType.values()) {
                    if (status.equals(navType.getType().toString())) {
                        robotDisinfectTaskRecordVo.setStatusText(navType.getValue());
                    }
                }
            }
            disinfectTaskRecordVos.add(robotDisinfectTaskRecordVo);
        }
        data1.setRecords(disinfectTaskRecordVos);
        return new ResultUtil<IPage<RobotDisinfectTaskRecordVo>>().setData(data1);
    }

    @RequestMapping(value = "/disinfectDetail", method = RequestMethod.GET)
    @ApiOperation(value = "获取消毒任务记录详情")
    public Result<RobotDisinfectTaskDetailVo> getDisinfectDetail(@RequestParam String id) {
        RobotDisinfectTaskDetail robotDisinfectTaskDetail = iRobotDisinfectTaskDetailService.getOne(new LambdaQueryWrapper<RobotDisinfectTaskDetail>().eq(RobotDisinfectTaskDetail::getRobotTaskRecordId, id));
        if (null != robotDisinfectTaskDetail) {
            RobotDisinfectTaskDetailVo robotDisinfectTaskDetailVo = new RobotDisinfectTaskDetailVo(robotDisinfectTaskDetail);
            return new ResultUtil<RobotDisinfectTaskDetailVo>().setData(robotDisinfectTaskDetailVo);
        }
        return new ResultUtil<RobotDisinfectTaskDetailVo>().setErrorMsg("无法获取消毒任务记录详情信息");
    }


    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotTaskRecord> saveOrUpdate(@ModelAttribute RobotTaskRecord robotTaskRecord) {
        if (iRobotTaskRecordService.saveOrUpdate(robotTaskRecord)) {
            return new ResultUtil<RobotTaskRecord>().setData(robotTaskRecord);
        }
        return new ResultUtil<RobotTaskRecord>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {

        for (String id : ids) {
            iRobotTaskRecordService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
