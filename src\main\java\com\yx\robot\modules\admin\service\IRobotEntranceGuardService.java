package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotEntranceGuard;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.vo.RobotEntranceGuardVo;

import java.util.List;

/**
 * 机器人门禁控制接口
 *
 * <AUTHOR>
 */
public interface IRobotEntranceGuardService extends IService<RobotEntranceGuard> {

    /**
     * 获取所有的门禁区域
     *
     * @return
     */
    List<RobotEntranceGuardVo> getAll();

    /**
     * 删除门禁区域
     *
     * @param id
     */
    void delete(String id);

    /**
     * 标记门禁点处理类
     *
     * @param robotPosition
     */
    void handleMarkEntranceGuardPosition(RobotPosition robotPosition);

    /**
     * 生成门禁区域
     *
     * @param insidePosition
     * @param outsidePosition
     */
    void createEntranceGuardAreas(RobotPosition insidePosition, RobotPosition outsidePosition);

    /**
     * 获取路径所穿越的门禁区域
     *
     * @param robotPositionId
     * @return
     */
    void checkLocalRouteThrough(String robotPositionId);

    /**
     * 处理到达门禁点之后
     *
     * @param positionId id
     * @param operate    true:开门禁，false:关门禁
     * @return true:成功，false:失败
     */
    boolean handleArriveEntranceGuardPosition(boolean operate, String positionId);

    /**
     * 处理到达门禁点之后
     *
     * @param positionId      id
     * @param operate         0:设置门禁点参数，1:开门，2：关门
     * @param entranceGuardId 门禁点ID
     * @return true:成功，false:失败
     */
    boolean handleArriveEntranceGuardPosition(Short operate, String positionId, String entranceGuardId);

    /**
     * 处理到达门禁点之后 ,并保存记录
     *
     * @param positionId      id
     * @param operate         0:设置门禁点参数，1:开门，2：关门
     * @param entranceGuardId 门禁点ID
     * @return true:成功，false:失败
     */
    boolean handleArriveEntranceGuardPositionAndSaveRecord(Short operate, String positionId, String entranceGuardId);

    /**
     * 开门子线程
     *
     * @param positionId      位置ID
     * @param entranceGuardId 门禁ID
     * @return true/false
     */
    boolean threadOpenDoor(String positionId, String entranceGuardId);
}