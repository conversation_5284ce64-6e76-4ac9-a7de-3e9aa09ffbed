package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotTaskRecord;

/**
 * 机器人任务记录接口
 * <AUTHOR>
 */
public interface IRobotTaskRecordService extends IService<RobotTaskRecord> {

    /**
     * 添加任务记录
     *
     * @param robotPositionName
     * @param taskId
     */
    void saveRobotTaskRecord(String robotPositionName, String taskId);

    /**
     * 更新任务记录
     *
     * @param statusCode
     */
    void updateRobotTaskRecord(Integer statusCode);
}