package com.yx.robot.modules.admin.vo;

import com.sun.management.OperatingSystemMXBean;
import lombok.Data;
import lombok.ToString;
import oshi.SystemInfo;
import oshi.hardware.CentralProcessor;

import java.io.File;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.text.DecimalFormat;
import java.util.concurrent.TimeUnit;

/**
 * 系统监控
 * <AUTHOR>
 * @date 20201/08/09
 */
@Data
@ToString
public class SysMonitor {

    public SysMonitor() {
        SystemInfo systemInfo = new SystemInfo();
        OperatingSystemMXBean osmxb = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
        MemoryMXBean memoryMXBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage memoryUsage = memoryMXBean.getHeapMemoryUsage();
        this.osName = System.getProperty("os.name");
        this.initTotalMemorySize = new DecimalFormat("#.##").format(memoryUsage.getInit() / 1024.0 / 1024 / 1024) + "G";
        this.maxMemorySize = new DecimalFormat("#.##").format(memoryUsage.getMax() / 1024.0 / 1024 / 1024) + "G";
        this.usedMemorySize =new DecimalFormat("#.##").format(memoryUsage.getUsed() / 1024.0 / 1024 / 1024) + "G";
        this.totalMemorySize = new DecimalFormat("#.##").format(osmxb.getTotalPhysicalMemorySize() / 1024.0 / 1024 / 1024) + "G";
        this.usedMemory = new DecimalFormat("#.##").format((osmxb.getTotalPhysicalMemorySize() - osmxb.getFreePhysicalMemorySize()) / 1024.0 / 1024 / 1024) + "G";
        this.freePhysicalMemorySize = new DecimalFormat("#.##").format(osmxb.getFreePhysicalMemorySize() / 1024.0 / 1024 / 1024) + "G";
        ThreadGroup parentThread;
        for (parentThread = Thread.currentThread().getThreadGroup(); parentThread
                .getParent() != null; parentThread = parentThread.getParent()) {
        }
        this.totalThread = parentThread.activeCount() + "";
        File[] files = File.listRoots();
        for (File file : files) {
            this.totalSpace = new DecimalFormat("#.#").format(file.getTotalSpace() * 1.0 / 1024 / 1024 / 1024) + "G";
            this.freeSpace = new DecimalFormat("#.#").format(file.getFreeSpace() * 1.0 / 1024 / 1024 / 1024) + "G";
            this.usableSpace = new DecimalFormat("#.#").format(file.getUsableSpace() * 1.0 / 1024 / 1024 / 1024) + "G";
        }
        CentralProcessor processor = systemInfo.getHardware().getProcessor();
        long[] prevTicks = processor.getSystemCpuLoadTicks();
        try {
            TimeUnit.SECONDS.sleep(1);
        } catch (Exception e) {
            e.printStackTrace();
        }
        long[] ticks = processor.getSystemCpuLoadTicks();
        long nice = ticks[CentralProcessor.TickType.NICE.getIndex()]
                - prevTicks[CentralProcessor.TickType.NICE.getIndex()];
        long irq = ticks[CentralProcessor.TickType.IRQ.getIndex()]
                - prevTicks[CentralProcessor.TickType.IRQ.getIndex()];
        long softirq = ticks[CentralProcessor.TickType.SOFTIRQ.getIndex()]
                - prevTicks[CentralProcessor.TickType.SOFTIRQ.getIndex()];
        long steal = ticks[CentralProcessor.TickType.STEAL.getIndex()]
                - prevTicks[CentralProcessor.TickType.STEAL.getIndex()];
        long cSys = ticks[CentralProcessor.TickType.SYSTEM.getIndex()]
                - prevTicks[CentralProcessor.TickType.SYSTEM.getIndex()];
        long user = ticks[CentralProcessor.TickType.USER.getIndex()]
                - prevTicks[CentralProcessor.TickType.USER.getIndex()];
        long iowait = ticks[CentralProcessor.TickType.IOWAIT.getIndex()]
                - prevTicks[CentralProcessor.TickType.IOWAIT.getIndex()];
        long idle = ticks[CentralProcessor.TickType.IDLE.getIndex()]
                - prevTicks[CentralProcessor.TickType.IDLE.getIndex()];
        long totalCpu = user + nice + cSys + idle + iowait + irq + softirq + steal;
        this.cpuCount = processor.getLogicalProcessorCount() + "";
        this.cpuSysPercent = new DecimalFormat("#.##%").format(cSys * 1.0 / totalCpu);
        this.cpuUserPercent = new DecimalFormat("#.##%").format(user * 1.0 / totalCpu);
        this.cpuIowaitPercent = new DecimalFormat("#.##%").format(iowait * 1.0 / totalCpu);
        this.cpuIdlePercent = new DecimalFormat("#.##%").format(idle * 1.0 / totalCpu);
    }

    /**
     * 操作系统名称
     */
    private String osName;

    /**
     * 初始总内存
     */
    private String initTotalMemorySize;

    /**
     * 最大可用内存
     */
    private String maxMemorySize;

    /**
     * 已使用内存
     */
    private String usedMemorySize;

    /**
     * 总物理内存
     */
    private String totalMemorySize;

    /**
     * 已使用物理内存
     */
    private String usedMemory;

    /**
     * 剩余物理内存
     */
    private String freePhysicalMemorySize;

    /**
     * 线程总数
     */
    private String totalThread;

    /**
     * 总磁盘空间
     */
    private String totalSpace;

    /**
     * 空闲磁盘空间
     */
    private String freeSpace;

    /**
     * 可用磁盘空间
     */
    private String usableSpace;

    /**
     * cpu核数
     */
    private String cpuCount;

    /**
     *  cpu系统使用率
     */
    private String cpuSysPercent;

    /**
     * cpu用户使用率
     */
    private String cpuUserPercent;

    /**
     * cpu当前等待率
     */
    private String cpuIowaitPercent;

    /**
     * cpu当前空闲率
     */
    private String cpuIdlePercent;

}
