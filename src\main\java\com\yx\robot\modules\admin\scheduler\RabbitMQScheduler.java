package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.alibaba.fastjson.JSON;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.utils.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.core.MessageProperties;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.HashMap;

import static com.yx.robot.common.constant.RabbitMqConstants.QUEUE_ROBOT_MESSAGE_NOTIFY;
import static com.yx.robot.common.constant.RabbitMqConstants.ROUTING_KEY_ROBOT_NOTIFY;
import static com.yx.robot.common.constant.RosWebConstants.*;


/**
 * <AUTHOR>
 * @date 2022/8/29
 * description：将RabbitMQ就收到的消息通过websocket发送到小程序
 */
@Component("RabbitMQScheduler")
@Slf4j
@Order(2)
public class RabbitMQScheduler {

    private final RabbitTemplate rabbitTemplate;

    public RabbitMQScheduler(RabbitTemplate rabbitTemplate) {
        this.rabbitTemplate = rabbitTemplate;
    }

    /**
     * 机器人消息通知发送
     * uploadResult:地图上传结果；
     * autoDuckFailureReason:自动充电失败原因
     */
    public void meaasgeNotify(Integer option) {
        try {
            String robotNumber = RobotBaseInfoConstant.robotNumber;
            HashMap<String, Integer> map = new HashMap<>(1);
            map.put(robotNumber, option);
            log.info("机器人消息通知发送:{}", JSON.toJSONString(map));
            String isNetwork = RedisUtil.getHash(ROBOT_SYS_INFO, NETWORK_STATUS);
            // 网络正常则发送mq消息
            if (StringUtils.isBlank(isNetwork) || Boolean.FALSE.toString().equalsIgnoreCase(isNetwork)) {
                RedisUtil.zadd(MESSAGE_INFO + ":notify:" + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN),
                        JSON.toJSONString(JSON.toJSONString(map)));
                return;
            }
            rabbitTemplate.setRoutingKey(ROUTING_KEY_ROBOT_NOTIFY);
            rabbitTemplate.setQueue(QUEUE_ROBOT_MESSAGE_NOTIFY);
            Message message = new Message(map.toString().getBytes(), new MessageProperties());
            rabbitTemplate.send(message);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 给孔提供故障消息
     */
    public void test(){
        try {
            Thread.sleep(20000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        String robotNumber = "X1-000";
            try {
                log.info("机器人消息通知发送...........测试。。。。。。。");
                HashMap<String, Integer> map = new HashMap<>(1);
                map.put(robotNumber,19);
                rabbitTemplate.setRoutingKey(ROUTING_KEY_ROBOT_NOTIFY);
                rabbitTemplate.setQueue(QUEUE_ROBOT_MESSAGE_NOTIFY);
                Message message = new Message(map.toString().getBytes(), new MessageProperties());
                rabbitTemplate.send(message);
            } catch (Exception e) {
                e.printStackTrace();
            }
    }

}
