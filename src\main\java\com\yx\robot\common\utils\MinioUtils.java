package com.yx.robot.common.utils;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import io.minio.*;
import io.minio.errors.MinioException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.multipart.MultipartFile;

import static com.yx.robot.common.constant.RobotRedisConstants.*;

/**
 * minio 文件操作工具类
 * 官方文档
 * https://docs.min.io/docs/java-client-quickstart-guide.html
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/15 11:00
 */
@Slf4j
public class MinioUtils {

    /**
     * url 和minio 客户端做映射
     * 一个url 只可以映射一个客户端
     */
    private static volatile MinioClient minioClient;

    private MinioClient getConnect() {

        if (ObjectUtil.isNull(minioClient)) {
            Object host = YmlUtils.getValue("yx.minio.host");
            Object port = YmlUtils.getValue("yx.minio.port");
            String secretKey = YmlUtils.getValue("yx.minio.secretKey") + "";
            String accessKey = YmlUtils.getValue("yx.minio.accessKey") + "";
            String minioUrl = "http://" + host + ":" + port + "";
            synchronized (MinioUtils.class) {
                if (ObjectUtil.isNull(minioClient)) {
                    minioClient = MinioClient.builder()
                            .endpoint(minioUrl)
                            .credentials(accessKey, secretKey)
                            .build();
                }
            }
        }
        return minioClient;
    }

    /**
     * 文件下载操作
     *
     * @param source 下载源，所在bucket的文件路径
     * @param target 下载到本地的目标路径
     * @param bucket 桶名称
     * @return true（成功）、 false(失败)
     */
    public static boolean download(String bucket, String source, String target) {
        log.info("target:"+target);
        boolean isSuccess = true;
        MinioUtils minioUtils = new MinioUtils();
        try {
            FileUtil.mkParentDirs(target);
            deleteLocalPath(target);
            /* play.min.io for test and development. */
            MinioClient minioClient = minioUtils.getConnect();
            // Download 'my-objectname' from 'my-bucketname' to 'my-filename'
            minioClient.downloadObject(
                    DownloadObjectArgs.builder()
                            .bucket(bucket)
                            .object(source)
                            .filename(target)
                            .build());
            log.info("{} is successfully downloaded to {}", source, target);
        } catch (Exception e) {
            //捕捉到异常后将状态改为false，否则之后更新会一直提示更新中
            RedisUtil.hset(ROBOT_INFO_MAP, IS_UPDATE_ROBOT_INFO_MAP_WEB_VERSION, "false");
            log.error("Error occurred:{}", e.getMessage());
            isSuccess = false;
        }
        if (!isSuccess) {
            deleteLocalPath(target);
        }
        return isSuccess;
    }

    /**
     * 文件下载操作
     *
     * @param uploadPath 上传文件，所在bucket的文件路径
     * @param localPath  文件上传的本地路径
     * @param bucket     桶名称
     * @return true（成功）、 false(失败)
     */
    public static boolean fileUploader(String bucket, String uploadPath, String localPath) {
        boolean isSuccess = true;
        try {
            MinioUtils minioUtils = new MinioUtils();
            // Create a minioClient with the MinIO server playground, its access key and secret key.
            MinioClient minioClient = minioUtils.getConnect();

            // Make 'asiatrip' bucket if not exist.
            boolean found =
                    minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
            if (!found) {
                // Make a new bucket called 'asiatrip'.
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
            } else {
                log.info("Bucket '{}' already exists.", bucket);
            }

            // Upload '/home/<USER>/Photos/asiaphotos.zip' as object name 'asiaphotos-2015.zip' to bucket
            // 'asiatrip'.
            minioClient.uploadObject(
                    UploadObjectArgs.builder()
                            .bucket(bucket)
                            .object(uploadPath)
                            .filename(localPath)
                            .build());
            log.info("'{}' is successfully uploaded as object '{}' to bucket '{}'.", localPath, uploadPath, bucket);
        } catch (MinioException e) {
            log.error("Error occurred: {}", e.getMessage());
            log.error("HTTP trace: {}", e.httpTrace());
            isSuccess = false;
        } catch (Exception e) {
            log.error("Error occurred: {}", e.getMessage());
            isSuccess = false;
        }
        if (!isSuccess) {
            deleteBucketFilePath(bucket, uploadPath);
        }
        return isSuccess;
    }

    /**
     * 文件上传
     *
     * @param bucket     bucket
     * @param uploadPath 文件上传位置
     * @param file       上传的文件
     * @return true
     */
    public static boolean fileUploader(String bucket, String uploadPath, MultipartFile file) {
        //开始上传
        try {
            MinioUtils minioUtils = new MinioUtils();
            // Create a minioClient with the MinIO server playground, its access key and secret key.
            MinioClient minioClient = minioUtils.getConnect();

            // Make 'asiatrip' bucket if not exist.
            boolean found =
                    minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
            if (!found) {
                // Make a new bucket called 'asiatrip'.
                minioClient.makeBucket(MakeBucketArgs.builder().bucket(bucket).build());
            } else {
                log.info("Bucket '{}' already exists.", bucket);
            }
            minioClient.putObject(
                    PutObjectArgs.builder().bucket(bucket).object(uploadPath).stream(
                                    file.getInputStream(), file.getSize(), -1)
                            .contentType(file.getContentType())
                            .build());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 删除已经上传的文件
     *
     * @param bucket     桶名称
     * @param objectName bucket文件路劲
     */
    public static void deleteBucketFilePath(String bucket, String objectName) {
        MinioUtils minioUtils = new MinioUtils();
        MinioClient minioClient = minioUtils.getConnect();
        try {
            minioClient.deleteObjectTags(
                    DeleteObjectTagsArgs.builder().bucket(bucket).object(objectName).build());
        } catch (MinioException e) {
            log.error("Error occurred: {}", e.httpTrace());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除本地文件
     *
     * @param path 本地文件路径
     */
    public static void deleteLocalPath(String path) {
        FileUtil.del(path);
    }

    public static void main(String[] args) {
//        MinioUtils.download("robot", "/0.1/yx-robot.jar", "E:/temp/1.1/test.jar");
        MinioUtils.fileUploader("robot", "/0.3/yx-robot.jar", "E:/temp/yx-robot.jar");
//        System.out.println("finish");
//        System.out.println(FileUtil.getSuffix("E:/temp/yx-robot.jar"));
//        System.out.println(FileUtil.getPrefix("E:/temp/yx-robot.jar"));
//        System.out.println(FileUtil.getParent(("E:/temp/yx-robot.jar"), 2));
//        FileUtil.getParent();
    }
}
