package com.yx.robot.modules.base.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 机器人自检封装
 * <AUTHOR>
 * @date 2020/09/17
 */
@Data
public class RobotCheckVo implements Serializable {

    private static final long serialVersionUID = 1L;

    // 通信自检
    private List<CheckInfoVo> communicationStatus;

    // 定位自检
    private List<CheckInfoVo> positionStatus;

    // 设备自检
    private List<CheckInfoVo> deviceStatus;

    // 配置自检
    private List<CheckInfoVo> configStatus;
}
