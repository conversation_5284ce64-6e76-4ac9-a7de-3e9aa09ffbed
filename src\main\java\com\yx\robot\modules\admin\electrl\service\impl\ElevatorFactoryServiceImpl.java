package com.yx.robot.modules.admin.electrl.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.utils.HttpSendUtil;
import com.yx.robot.modules.admin.electrl.constant.ApiConstants;
import com.yx.robot.modules.admin.electrl.constant.ElevatorFactoryConfig;
import com.yx.robot.modules.admin.electrl.constant.ElevatorTopicConstants;
import com.yx.robot.modules.admin.electrl.enums.EncryptTypeEnum;
import com.yx.robot.modules.admin.electrl.msg.*;
import com.yx.robot.modules.admin.electrl.service.ElevatorFactoryService;
import com.yx.robot.modules.admin.electrl.util.ElevatorUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.Map;

/**
 * 梯控厂家服务实现类
 *
 * <AUTHOR>
 * @date 2021/12/11
 */
@Service
@Slf4j
public class ElevatorFactoryServiceImpl implements ElevatorFactoryService {

//    private final HttpSendUtil httpSendUtil;
//
//    @Autowired
//    public ElevatorFactoryServiceImpl(HttpSendUtil httpSendUtil) {
//        this.httpSendUtil = httpSendUtil;
//    }

    /**
     * 开发者认证
     *
     * @param developerLoginReq 登录请求参数
     * @return 登录响应参数
     */
    @Override
    public DeveloperLoginRep developerLogin(DeveloperLoginReq developerLoginReq) {
        String url = ApiConstants.BASE_URL + ApiConstants.DEVELOPER_LOGIN;
        Response response = null;
        try {
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq));
            String sign = ElevatorUtil.getSDKV3Sign(JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq), Map.class), ElevatorFactoryConfig.APP_SECRET);
            params.put("sign", sign);
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String, String> formParams = new HashMap<>();
            formParams.put("encryptScript", encryptScript);
            formParams.put("appId", ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
            response = HttpSendUtil.okhttpPost(url, formParams);
            if (response != null && response.isSuccessful()) {
                JSONObject result = JSONObject.parseObject(response.body().string());
                if (result != null && result.get("encryptScript") != null) {
                    String encryptResultScript = result.get("encryptScript").toString();
                    String nsout = ElevatorUtil.decryptStr(encryptResultScript, ElevatorFactoryConfig.APP_SECRET);
                    DeveloperLoginRep developerLoginRep = JSONObject.parseObject(nsout, DeveloperLoginRep.class);
                    return developerLoginRep;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
        return null;
    }

    /**
     * 预约单云电梯
     *
     * @param jedis           jedis
     * @param callElevatorReq 呼叫预约单云电梯
     * @return 响应参数
     */
    @Override
    public CallElevatorRep callElevator(Jedis jedis, CallElevatorReq callElevatorReq) {
        String url = ApiConstants.BASE_URL + ApiConstants.CALL_ELEVATOR;
        Response response = null;
        try {
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(callElevatorReq));
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String, String> formParams = new HashMap<>();
            formParams.put("encryptScript", encryptScript);
            formParams.put("appId", ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
            String token = jedis.get(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN);
            if (StrUtil.isNotBlank(token)) {
                formParams.put("token", token);
            }
            response = HttpSendUtil.okhttpPost(url, formParams);
            if (response != null && response.isSuccessful()) {
                JSONObject result = JSONObject.parseObject(response.body().string());
                if (result != null && result.get("encryptScript") != null) {
                    String encryptResultScript = result.get("encryptScript").toString();
                    String nsout = ElevatorUtil.decryptStr(encryptResultScript, ElevatorFactoryConfig.APP_SECRET);
                    log.info("nout:{}", nsout);
                    CallElevatorRep callElevatorRep = JSONObject.parseObject(nsout, CallElevatorRep.class);
                    return callElevatorRep;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
        return null;
    }

    /**
     * 预约无感电梯
     *
     * @param callNoninductiveElevatorReq 预约无感电梯请求参数
     * @return 预约无感电梯响应参数
     */
    @Override
    public CallNoninductiveElevatorRep callNoninductiveElevator(Jedis jedis, CallNoninductiveElevatorReq callNoninductiveElevatorReq) {
        String url = ApiConstants.BASE_URL + ApiConstants.CALL_NONIDUCTIVE_ELEVATOR;
        Response response = null;
        try {
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(callNoninductiveElevatorReq));
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String, String> formParams = new HashMap<>();
            formParams.put("encryptScript", encryptScript);
            formParams.put("appId", ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
            String token = jedis.get(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN);
            if (StrUtil.isNotBlank(token)) {
                formParams.put("token", token);
            }
            response = HttpSendUtil.okhttpPost(url, formParams);
            if (response != null && response.isSuccessful()) {
                JSONObject result = JSONObject.parseObject(response.body().string());
                if (result != null && result.get("encryptScript") != null) {
                    String encryptResultScript = result.get("encryptScript").toString();
                    String nsout = ElevatorUtil.decryptStr(encryptResultScript, ElevatorFactoryConfig.APP_SECRET);
                    CallNoninductiveElevatorRep callNoninductiveElevatorRep = JSONObject.parseObject(nsout, CallNoninductiveElevatorRep.class);
                    return callNoninductiveElevatorRep;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
        return null;
    }


    /**
     * 发送开门指令
     *
     * @param jedis           jedis
     * @param sendOpenDoorReq 发送开门指令请求参数
     * @return 发送开门指令响应参数
     */
    @Override
    public SendOpenDoorRep sendOpenDoor(Jedis jedis, SendOpenDoorReq sendOpenDoorReq) {
        log.info("sendOpenDoor start");
        Response response = null;
        try {
            String url = ApiConstants.BASE_URL + ApiConstants.SEND_OPEN_DOOR;
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(sendOpenDoorReq));
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String, String> formParams = new HashMap<>();
            formParams.put("encryptScript", encryptScript);
            formParams.put("appId", ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
            String token = jedis.get(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN);
            if (StrUtil.isNotBlank(token)) {
                formParams.put("token", token);
            }
            // 防止网络不好执行三次
            for (int index = 0; index < 3; index++) {
                response = HttpSendUtil.okhttpPost(url, formParams);
                log.info("电梯门开门时间:{}", JSONObject.toJSONString(response));
                if (response != null && response.isSuccessful()) {
                    JSONObject result = JSONObject.parseObject(response.body().string());
                    if (result != null && result.get("encryptScript") != null) {
                        String encryptResultScript = result.get("encryptScript").toString();
                        String nsout = ElevatorUtil.decryptStr(encryptResultScript, ElevatorFactoryConfig.APP_SECRET);
                        log.info("电梯门开门时间 nsout :{}", nsout);
                        SendOpenDoorRep sendOpenDoorRep = JSONObject.parseObject(nsout, SendOpenDoorRep.class);
                        return sendOpenDoorRep;
                    }
                }
                Thread.sleep(1000);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
        return null;
    }


    /**
     * 获取任务信息
     *
     * @param jedis       jedis
     * @param taskInfoReq 任务请求参数
     * @return 任务响应参数
     */
    @Override
    public TaskInfoRep getTaskInfo(Jedis jedis, TaskInfoReq taskInfoReq) {
        String url = ApiConstants.BASE_URL + ApiConstants.GET_TASK_INFO;
        Response response = null;
        try {
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(taskInfoReq));
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String, String> formParams = new HashMap<>();
            formParams.put("encryptScript", encryptScript);
            formParams.put("appId", ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
            String token = jedis.get(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN);
            if (StrUtil.isNotBlank(token)) {
                formParams.put("token", token);
            }
            response = HttpSendUtil.okhttpPost(url, formParams);
            if (response != null && response.isSuccessful()) {
                JSONObject result = JSONObject.parseObject(response.body().string());
                if (result != null && result.get("encryptScript") != null) {
                    String encryptResultScript = result.get("encryptScript").toString();
                    String nsout = ElevatorUtil.decryptStr(encryptResultScript, ElevatorFactoryConfig.APP_SECRET);
                    Map<String, String> nsoutMap = JSONObject.parseObject(nsout, Map.class);
                    log.info("nsout===success:{},msg:{},msgCode:{}", nsoutMap.get("success"), nsoutMap.get("msg"),
                            nsoutMap.get("msgCode"));
                    TaskInfoRep taskInfoRep = JSONObject.parseObject(nsout, TaskInfoRep.class);
                    return taskInfoRep;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("getTaskInfo:{}", e.getMessage());
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
        return null;
    }
}
