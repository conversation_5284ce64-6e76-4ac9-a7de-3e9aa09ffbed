package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotDisinfectantRecord;

import java.util.List;

/**
 * 机器人消毒液变化记录接口
 *
 * <AUTHOR>
 */
public interface IRobotDisinfectantRecordService extends IService<RobotDisinfectantRecord> {

    /**
     * 处理消毒水控制
     *
     * @param action    开始(1) 停止(0)
     * @param operation 进水(1) 出水(-1)
     * @return true / false
     */
    boolean handleRobotDisinfectant(String operation, String action);

    /**
     * U3专用排水口
     * 开始排水：1
     * 结束排水：0
     * @param action
     * @return
     */
    boolean outWaterControl(String action);

}