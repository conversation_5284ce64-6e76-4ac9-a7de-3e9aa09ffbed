package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.dto.RobotRouteInfoDto;
import com.yx.robot.modules.admin.entity.RobotRoute;
import com.yx.robot.modules.admin.vo.RobotMapVo;
import com.yx.robot.modules.admin.vo.RobotRouteVo;

import java.util.List;

/**
 * 机器人路线接口
 * <AUTHOR>
 */
public interface IRobotRouteService extends IService<RobotRoute> {

    /**
     * 保存路径并新建路线任务
     * @param robotMapVo
     */
    void saveRouteAndTask(RobotMapVo robotMapVo);

    /**
     * 获取所有路线二级结构
     * @return
     */
    List<RobotRouteInfoDto> getAllRoute();

    /**
     * 保存路线
     * @param robotRouteVo
     * @return
     */
    RobotRoute save(RobotRouteVo robotRouteVo);

    /**
     * 编辑路线
     * @param robotRouteVo
     * @return
     */
    boolean edit(RobotRouteVo robotRouteVo);

    /**
     * 初始化路径录制
     * @return
     */
    boolean initPathRecord(Integer pathRecord);

    /**
     * 获取默认路径id
     */
    String getDefaultRouteId();
}