package com.yx.robot.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yx.robot.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_task_record")
@ApiModel(value = "机器人任务记录")
public class RobotTaskRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * uuId
     */
    private String uuId;

    /**
     * 任务id
     */
    private String taskId;

    /**
     * 开始时间
     */
//    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
//    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    /**
     * 结束时间
     */
//    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
//    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date endTime;

    /**
     * 机器人位置信息
     */
    private String robotLocation;

    /**
     * 机器人行驶里程
     */
    private Double mileage;

    /**
     * 任务耗时
     */
    private long timeConsume;

    /**
     * 任务执行状态
     * 0 为初始状态
     * 405 导航中
     * 407 到达目标点
     * 4071 到达目标点附近
     * 4080 急停开关被摁下
     * 4081 防撞条触碰
     * 4082 定位问题
     * 4083 节点错误
     * 4086 深度问题
     */
    private String status;

    /**
     * 数据状态（1 已更新 0未更新）
     */
    private String dataStatus;

    /**
     * 同步状态(1 已同步 0未同步)
     */
    private String syncStatus;

    private String videoPath;
}