package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.constant.StatusConstants;
import com.yx.robot.common.enums.TaskType;
import com.yx.robot.common.utils.DateUtils;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.jike.service.JikePlatformCore;
import com.yx.robot.modules.admin.service.IRobotMotorService;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.IRobotTaskService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.RobotInfoVo;
import com.yx.robot.modules.base.utils.LidarControlUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.io.File;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Calendar;
import java.util.List;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
@Order(2)
public class FixedTimeTaskScheduler implements CommandLineRunner {

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1, new BasicThreadFactory.Builder().namingPattern("scheduled-FixedTime-%d").daemon(true).build());

    private final RosWebService rosWebService;

    private final IRobotTaskService robotTaskService;

    private final JikePlatformCore jikePlatformCore;

    private final IRobotStatusService iRobotStatusService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Autowired
    private LidarControlUtil lidarControlUtil;

    /**
     * 判断执行次数，只可以执行一次
     */
    private static long count = 0;

    /**
     * 定时重启判断执行次数，只可以执行一次
     */
    private static long restartCount = 0;

    public FixedTimeTaskScheduler(RosWebService rosWebService, IRobotTaskService robotTaskService, JikePlatformCore jikePlatformCore, IRobotStatusService iRobotStatusService) {
        this.rosWebService = rosWebService;
        this.robotTaskService = robotTaskService;
        this.jikePlatformCore = jikePlatformCore;
        this.iRobotStatusService = iRobotStatusService;
    }

    @Override
    public void run(String... args) {
        jikePlatformCore.handleCleanToken();
        executorService.scheduleWithFixedDelay(() -> {
            try {
                //当前时间
                Calendar currentCalendar = DateUtils.getCalendar();
                int currentDayOfWeek = currentCalendar.get(Calendar.DAY_OF_WEEK);
                int currentTimeHour = currentCalendar.get(Calendar.HOUR_OF_DAY);
                int currentTimeMinute = currentCalendar.get(Calendar.MINUTE);
                //处理消毒任务
                handleDisinfect(currentDayOfWeek, currentTimeHour, currentTimeMinute);
                //处理自动重启
                handleAutoStart(currentTimeHour, currentTimeMinute);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, 0, 5, TimeUnit.SECONDS);
        log.info("定时任务检测中......");
    }

    /**
     * 处理消毒
     *
     * @param currentDayOfWeek  当前周几
     * @param currentTimeHour   当前小时
     * @param currentTimeMinute 当前分钟
     * @throws Exception e
     */
    private void handleDisinfect(int currentDayOfWeek, int currentTimeHour, int currentTimeMinute) throws Exception {
        Object currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
        if (currentMap == null || StrUtil.isBlank(currentMap.toString())) {
            log.warn("无法获取当前地图");
            return;
        }
        List<RobotTask> robotTaskList = robotTaskService.list(new LambdaQueryWrapper<RobotTask>()
                .eq(RobotTask::getType, TaskType.DISINFECT.getType())
                .eq(RobotTask::getMapId, currentMap.toString()));
        if (CollectionUtil.isEmpty(robotTaskList)) {
            log.warn("无法查询到任务列表");
            return;
        }
        if (!iRobotStatusService.getPositionStatus()) {
            log.info("定位丢失无法执行定时任务");
            return;
        }
        // 每次检查只获取一个定时任务
        for (RobotTask robotTask : robotTaskList) {
            Integer isFixedTime = robotTask.getIsFixedTime();
            String weekdays = robotTask.getWeekdays();
            //是否定时
            if (null != isFixedTime && isFixedTime == 1) {
                String startTime = robotTask.getStartTime();
                if (StrUtil.isNotEmpty(startTime)) {
                    Calendar startTimeStr = DateUtils.parseCalendar(startTime, "HH:mm");
                    int startTimeHour = startTimeStr.get(Calendar.HOUR_OF_DAY);
                    int startTimeMinute = startTimeStr.get(Calendar.MINUTE);
                    boolean weekDayOfCheck = lidarControlUtil.weekDaysCheck(weekdays, currentDayOfWeek);
                    RobotInfoVo robotInfo = rosWebService.getRobotInfo();
                    if (currentTimeHour == startTimeHour
                            && currentTimeMinute == startTimeMinute
                            && weekDayOfCheck && robotInfo.getIsIdle() && !robotInfo.getIsMapping()) {
                        // 判断电量
                        Float batteryRemain = iRobotStatusService.getBatteryPercentage();
                        // 判断是否为手动充电，如果是手动充电则不执行定时任务
                        if (rosWebService.isManualCharge()) {
                            log.warn("如果是手动充电则不执行定时任务");
                            return;
                        }
                        //进行急停状态判断，如果按下急停，则不执行定时任务
                        if(!iRobotMotorService.getEmergencyState()) {
                            if (ObjectUtil.isNotNull(batteryRemain)) {
                                //如果能获取到电池电量，且电量大于15%，则直接执行
                                if (batteryRemain > StatusConstants.BATTERY_POWER_THRESHOLD_FORCE_CHARGE.floatValue()) {
                                    log.info("定时时间已到，准备执行消毒任务");
                                    rosWebService.startDisinfectService(robotTask.getId());
                                }
                            } else {
                                // 获取不到电池电量则直接进行消毒任务执行
                                log.info("定时时间已到，准备执行消毒任务");
                                rosWebService.startDisinfectService(robotTask.getId());
                            }
                        }else {
                            log.info("急停被按下，不执行定时任务");
                        }
                        Thread.sleep(60000);
                    }
                }
            }
        }
    }

    /**
     * 处理自动重启
     */
    private void handleAutoStart(int currentTimeHour, int currentTimeMinute) throws Exception {
        Object autoStartTime = RedisUtil.getHash(ROBOT_SYS_INFO, AUTO_START_TIME);
//        boolean res1 = false;
//        String infraredResult = RedisUtil.getValue(TOPIC + "::" + TopicConstants.INFRARED_STATUS);
//        if (StrUtil.isNotBlank(infraredResult)) {
//            _Infrared infrared = JSON.parseObject(infraredResult, _Infrared.class);
//            // 是否检测到了红外信号
//            res1 = infrared.left_center != 0 || infrared.right_center != 0;
//        }
        // 是否在充电
        boolean res2 = rosWebService.isCharging();
        // 定时重启
        if (autoStartTime != null && StringUtils.isNotBlank(autoStartTime.toString())) {
            DateTime dateTime = DateUtil.parse(autoStartTime.toString(), "HH:mm");
            int startTimeHour = dateTime.getField(DateField.HOUR_OF_DAY);
            int startTimeMinute = dateTime.getField(DateField.MINUTE);
            if (currentTimeHour == startTimeHour && currentTimeMinute == startTimeMinute) {
                if (restartCount == 0) {
                    // 当前处于对接充电桩状态
                    if (res2) {
                        log.info("定时重启");
                        rosWebService.notifyRosRestart();
                        restartCount++;
                    }
                }
            } else {
                restartCount = 0;
            }
        }
        // 磁盘负载重启，如果空闲磁盘空间小于20% 则执行重启
        String s = RedisUtil.getValue(MONITOR_INFO);
        if (StrUtil.isNotEmpty(s)) {
            File[] disks = File.listRoots();
            BigDecimal percent = new BigDecimal(0);
            for (File file : disks) {
                BigDecimal total = new BigDecimal(file.getTotalSpace() / 1024 / 1024 / 1024).setScale(2, RoundingMode.HALF_UP);
                BigDecimal useSpace = new BigDecimal(file.getUsableSpace() / 1024 / 1024 / 1024).setScale(2, RoundingMode.HALF_UP);
                BigDecimal freeSpace = new BigDecimal(file.getFreeSpace() / 1024 / 1024 / 1024).setScale(2, RoundingMode.HALF_UP);
                if (ObjectUtil.isNull(total)) {
                    continue;
                }

                if (percent.floatValue() < 0.2) {
                    continue;
                }
                percent = freeSpace.divide(total, 2, RoundingMode.HALF_UP);
            }
            // 当前处于对接充电桩状态
            if (percent.floatValue() < 0.2) {
                if (count == 0) {
                    if (res2) {
                        count++;
                    }
                }
            } else if (percent.floatValue() < 0.1) {
                log.info("磁盘占用：" + percent.floatValue());
//                sendMessageUtil.sendShortMessage("磁盘占用超过90%");
            } else {
                count = 0;
            }
        }
    }


}
