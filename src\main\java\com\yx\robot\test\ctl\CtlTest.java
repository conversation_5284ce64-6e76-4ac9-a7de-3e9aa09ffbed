package com.yx.robot.test.ctl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.utils.HttpSendUtil;
import com.yx.robot.modules.admin.electrl.constant.ApiConstants;
import com.yx.robot.modules.admin.electrl.constant.ElevatorFactoryConfig;
import com.yx.robot.modules.admin.electrl.constant.ElevatorTopicConstants;
import com.yx.robot.modules.admin.electrl.enums.EncryptTypeEnum;
import com.yx.robot.modules.admin.electrl.msg.CallElevatorRep;
import com.yx.robot.modules.admin.electrl.msg.CallElevatorReq;
import com.yx.robot.modules.admin.electrl.msg.DeveloperLoginRep;
import com.yx.robot.modules.admin.electrl.msg.DeveloperLoginReq;
import com.yx.robot.modules.admin.electrl.service.ElevatorFactoryService;
import com.yx.robot.modules.admin.electrl.service.impl.ElevatorFactoryServiceImpl;
import com.yx.robot.modules.admin.electrl.util.ElevatorUtil;
import okhttp3.Response;
import org.apache.commons.lang3.ArrayUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;
import redis.clients.jedis.Jedis;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/7 10:39
 */
public class CtlTest {

    public static void main(String[] args) {
        List<String> positionIdList = new ArrayList<>();
        positionIdList.add("1");
        positionIdList.add("3");
        positionIdList.add("2");
        int sortedSize = 5;
        if (CollectionUtil.size(positionIdList) < sortedSize) {
            System.out.println("yes");
        }
//        CtlTest ctlTest = new CtlTest();
//        DeveloperLoginReq developerLoginReq = new DeveloperLoginReq();
//        DeveloperLoginRep developerLoginRep = ctlTest.developerLogin(developerLoginReq);
//        CallElevatorReq callElevatorReq = new CallElevatorReq();
//        callElevatorReq.setFromFloor("3");
//        callElevatorReq.setToFloor("4");
//        CallElevatorRep callElevatorRep = ctlTest.callElevator(developerLoginRep.getData().getToken(), callElevatorReq);
//        System.out.println(JSON.toJSONString(callElevatorRep));
//        test();
    }

    /**
     * 获取单云电梯token
     *
     * @param developerLoginReq
     * @return
     */
    public DeveloperLoginRep developerLogin(DeveloperLoginReq developerLoginReq) {
        String url = ApiConstants.BASE_URL + ApiConstants.DEVELOPER_LOGIN;
        Response response = null;
        try {
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq));
            String sign = ElevatorUtil.getSDKV3Sign(JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq), Map.class), ElevatorFactoryConfig.APP_SECRET);
            params.put("sign", sign);
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String, String> formParams = new HashMap<>();
            formParams.put("encryptScript", encryptScript);
            formParams.put("appId", ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
            System.out.println(JSON.toJSONString(formParams));
            response = HttpSendUtil.okhttpPost(url, formParams);
            if (response != null && response.isSuccessful()) {
                JSONObject result = JSONObject.parseObject(response.body().string());
                if (result != null && result.get("encryptScript") != null) {
                    String encryptResultScript = result.get("encryptScript").toString();
                    String nsout = ElevatorUtil.decryptStr(encryptResultScript, ElevatorFactoryConfig.APP_SECRET);
                    DeveloperLoginRep developerLoginRep = JSONObject.parseObject(nsout, DeveloperLoginRep.class);
                    return developerLoginRep;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
        return null;
    }

    public static void test() {
        DeveloperLoginReq developerLoginReq = new DeveloperLoginReq();
        JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq));
        String sign = ElevatorUtil.getSDKV3Sign(JSONObject.parseObject(JSONObject.toJSONString(developerLoginReq), Map.class), ElevatorFactoryConfig.APP_SECRET);
        params.put("sign", sign);
        System.out.println("sign:" + sign);
        String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
        Map<String, String> formParams = new HashMap<>();
        formParams.put("encryptScript", encryptScript);
        System.out.println("encryptScript:" + encryptScript);
        formParams.put("appId", ElevatorFactoryConfig.APP_ID);
        System.out.println("appId:" + ElevatorFactoryConfig.APP_ID);
        formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
        System.out.println("encryptType:" + EncryptTypeEnum.MODE_1.getValue().toString());
    }

    public CallElevatorRep callElevator(String token, CallElevatorReq callElevatorReq) {
        String url = ApiConstants.BASE_URL + ApiConstants.CALL_ELEVATOR;
        Response response = null;
        try {
            JSONObject params = JSONObject.parseObject(JSONObject.toJSONString(callElevatorReq));
            String encryptScript = ElevatorUtil.encryptHex(params.toString(), ElevatorFactoryConfig.APP_SECRET);
            Map<String, String> formParams = new HashMap<>();
            formParams.put("encryptScript", encryptScript);
            formParams.put("appId", ElevatorFactoryConfig.APP_ID);
            formParams.put("encryptType", EncryptTypeEnum.MODE_1.getValue().toString());
//            String token = jedis.get(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN);
            if (StrUtil.isNotBlank(token)) {
                formParams.put("token", token);
            }
            response = HttpSendUtil.okhttpPost(url, formParams);
            System.out.println(JSON.toJSONString(response));
            if (response != null && response.isSuccessful()) {
                JSONObject result = JSONObject.parseObject(response.body().string());
                System.out.println(result);
                if (result != null && result.get("encryptScript") != null) {
                    String encryptResultScript = result.get("encryptScript").toString();
                    String nsout = ElevatorUtil.decryptStr(encryptResultScript, ElevatorFactoryConfig.APP_SECRET);
                    System.out.println("nsout:" + nsout);
                    CallElevatorRep callElevatorRep = JSONObject.parseObject(nsout, CallElevatorRep.class);
                    return callElevatorRep;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (response != null) {
                response.body().close();
            }
        }
        return null;
    }
}
