package com.yx.robot.test.ctl;

import cn.hutool.core.thread.ThreadUtil;
import com.yx.robot.modules.admin.service.IRobotChargingService;
import com.yx.robot.modules.admin.service.core.RosCallService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/16 16:13
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class RosServiceTests {

    @Autowired
    private IRobotChargingService iRobotChargingService;

    @Autowired
    private RosCallService rosCallService;

    @Test
    public void getImgPath() {
        System.out.println(iRobotChargingService.saveDockPosition());
    }

    @Test
    public void testUpdateJSON() {
        System.out.println("=========================================================");
//        ThreadUtil.sleep(10, TimeUnit.SECONDS);
        Short cmdType = new Short("3");
        Short[] paramsShortArray = {5, 5, 5, 5};
//        Boolean flag = rosCallService.callEntranceGuard(cmdType, paramsShortArray);
        System.out.println("=========================================================");
//        System.out.println(flag);

//        ThreadUtil.sleep(30, TimeUnit.SECONDS);
        cmdType = new Short("2");
        Boolean flag = rosCallService.callEntranceGuard(cmdType, paramsShortArray);
        System.out.println("=========================================================");
        System.out.println(flag);
    }
}
