package tt;

import cn.hutool.core.thread.ThreadUtil;
import com.alibaba.fastjson.JSON;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._Bool;
import edu.wpi.rail.jrosbridge.Ros;
import edu.wpi.rail.jrosbridge.Topic;
import edu.wpi.rail.jrosbridge.callback.TopicCallback;
import edu.wpi.rail.jrosbridge.handler.RosHandler;

import javax.websocket.Session;
import java.time.Duration;

public class Test {

    public static void main(String[] args) {
        Ros ros = new Ros("192.168.1.178", 9090);

        _Bool bool = new _Bool();
        bool.data = true;
        ros.addRosHandler(new RosHandler() {
            @Override
            public void handleConnection(Session session) {
                System.out.println(session.isOpen());
            }

            @Override
            public void handleDisconnection(Session session) {

            }

            @Override
            public void handleError(Session session, Throwable t) {
                System.out.println(t.getMessage());
            }
        });
        ros.registerTopicCallback("/spray_ctrl", message -> System.out.println(message.toJsonObject()));
        if (ros.connect()) {
            System.out.println("已经连接");
        }
        Topic rosBridge = new Topic(ros, "/spray_ctrl", Message.getMessageType(_Bool.class));
        System.out.println("=======================================================================");
        edu.wpi.rail.jrosbridge.messages.Message message = new edu.wpi.rail.jrosbridge.messages.Message(JSON.toJSONString(bool));
        rosBridge.publish(message);
        rosBridge.subscribe(new TopicCallback() {
            @Override
            public void handleMessage(edu.wpi.rail.jrosbridge.messages.Message message) {
                System.out.println(message.toString());
            }
        });
        System.out.println("=======================================================================");
        ThreadUtil.sleep(Duration.ofMinutes(5).toMillis());
        System.out.println("stop");
        bool.data = false;
        message = new edu.wpi.rail.jrosbridge.messages.Message(JSON.toJSONString(bool));
        rosBridge.publish(message);
        ThreadUtil.sleep(Duration.ofMinutes(5).toMillis());
    }
}
