package com.yx.robot.modules.admin.controller;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotCheckHistoryRecord;
import com.yx.robot.modules.admin.service.IRobotCheckHistoryRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.modules.admin.vo.CheckItemVo;
import com.yx.robot.modules.admin.vo.CheckReportVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人检测历史记录管理接口")
@RequestMapping("/yx/api-v1/robotCheckHistoryRecord")
@Transactional
public class RobotCheckHistoryRecordController {

    @Autowired
    private IRobotCheckHistoryRecordService iRobotCheckHistoryRecordService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotCheckHistoryRecord> get(@PathVariable String id) {
        RobotCheckHistoryRecord robotCheckHistoryRecord = iRobotCheckHistoryRecordService.getById(id);
        return new ResultUtil<RobotCheckHistoryRecord>().setData(robotCheckHistoryRecord);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotCheckHistoryRecord>> getAll() {
        List<RobotCheckHistoryRecord> list = iRobotCheckHistoryRecordService.list();
        return new ResultUtil<List<RobotCheckHistoryRecord>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotCheckHistoryRecord>> getByPage(@ModelAttribute PageVo page,
                                                            @RequestParam(required = false) Boolean result,
                                                            @RequestParam(required = false) String operationUser,
                                                            @RequestParam(required = false) String startTime,
                                                            @RequestParam(required = false) String endTime) {

        if (StringUtils.isBlank(page.getSortOrigin())) {
            page.setSort("id");
        }
        LambdaQueryWrapper<RobotCheckHistoryRecord> robotCheckHistoryRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (result != null) {
            robotCheckHistoryRecordLambdaQueryWrapper.eq(RobotCheckHistoryRecord::getStatus, result ? "1" : "0");
        }
        if (StrUtil.isNotBlank(operationUser)) {
            robotCheckHistoryRecordLambdaQueryWrapper.eq(RobotCheckHistoryRecord::getOperationUser, operationUser);
        }
        if (StrUtil.isNotBlank(startTime)) {
            robotCheckHistoryRecordLambdaQueryWrapper.gt(RobotCheckHistoryRecord::getCheckDate, startTime);
        }
        if (StrUtil.isNotBlank(endTime)) {
            robotCheckHistoryRecordLambdaQueryWrapper.lt(RobotCheckHistoryRecord::getCheckDate, endTime);
        }
        IPage<RobotCheckHistoryRecord> data = iRobotCheckHistoryRecordService.page(PageUtil.initMpPage(page), robotCheckHistoryRecordLambdaQueryWrapper);
        return new ResultUtil<IPage<RobotCheckHistoryRecord>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotCheckHistoryRecord> saveOrUpdate(@ModelAttribute RobotCheckHistoryRecord robotCheckHistoryRecord) {
        if (iRobotCheckHistoryRecordService.saveOrUpdate(robotCheckHistoryRecord)) {
            return new ResultUtil<RobotCheckHistoryRecord>().setData(robotCheckHistoryRecord);
        }
        return new ResultUtil<RobotCheckHistoryRecord>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {
        for (String id : ids) {
            iRobotCheckHistoryRecordService.delete(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }

    @RequestMapping(value = "/submitCheckReport", method = RequestMethod.POST)
    @ApiOperation(value = "提交检测报告")
    public Result<Object> submitCheckReport(@RequestBody CheckReportVo checkReportVo) {
        boolean b = iRobotCheckHistoryRecordService.submitCheckReport(checkReportVo);
        if (b) {
            return new ResultUtil<Object>().setSuccessMsg("提交成功");
        } else {
            return new ResultUtil<Object>().setErrorMsg("提交失败");
        }
    }

    ;

    @RequestMapping(value = "/checkReportDetail/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "检测报告详情")
    public Result<Object> checkReportDetail(@PathVariable String id) {
        List<CheckItemVo> checkItemVos = iRobotCheckHistoryRecordService.checkReportDetail(id);
        return new ResultUtil<Object>().setData(checkItemVos);
    }

    @RequestMapping(value = "/getCheckData", method = RequestMethod.GET)
    @ApiOperation(value = "获取自检信息")
    public Result<?> getCheckData() {
        JSONObject checkData = iRobotCheckHistoryRecordService.getCheckData();
        return new ResultUtil<Object>().setData(checkData);
    }
}
