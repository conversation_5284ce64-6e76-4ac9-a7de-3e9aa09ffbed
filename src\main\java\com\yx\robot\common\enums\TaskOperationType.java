package com.yx.robot.common.enums;

/**
 * 任务操作类型
 *
 * <AUTHOR>
 * @date 2020/11/3
 */
public enum TaskOperationType {

    /**
     * 1,"开始任务"
     */
    START(1, "开始任务"),

    /**
     * 2, "停止任务"
     */
    STOP(2, "停止任务"),

    /**
     * 3, "继续任务"
     */
    CONTINUE(3, "继续任务"),

    /**
     * 4, "取消任务"
     */
    CANCEL(4, "取消任务");

    public final Integer operation;

    public final String label;

    TaskOperationType(Integer operation, String label) {
        this.operation = operation;
        this.label = label;
    }

    public Integer getOperation() {
        return this.operation;
    }

    public String getLabel() {
        return this.getLabel();
    }
}
