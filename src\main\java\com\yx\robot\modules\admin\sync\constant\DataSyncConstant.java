package com.yx.robot.modules.admin.sync.constant;

/**
 * 数据同步相关常量
 *
 * <AUTHOR>
 * @date 2020/05/14
 */
public interface DataSyncConstant {
    /**
     * 设备信息
     */
    String DEVICE_INFO_KEY = "device_info";

    /**
     * 任务记录
     */
    String TASK_RECORD_KEY = "task_record";

    /**
     * 充电记录
     */
    String CHARGING_RECORD_KEY = "charging_record";

    /**
     * 开关机记录详情
     */
    String SWITCH_RECORD_KEY = "switch_record";

    /**
     * 消毒任务详情记录
     */
    String DISINFECT_TASK_DETAIL = "disinfect_task_detail";

    /**
     * 机器人设备数据
     */
    String DEVICE_DATA_KEY = "device_data";

    /**
     * 机器人唯一序列号
     */
    String SERIAL_NUMBER = "serial_number";

    /**
     * 机器人当前地图
     */
    String CURRENT_MAP = "current_map";

    /**
     * 主机地址
     */
    String DEVICE_HOST_KEY = "device_host";

    /**
     * 设备状态
     */
    String DEVICE_STATUS_KEY = "device_status";

    /**
     * 运行信息
     */
    String WORKING_INFO_KEY = "working_info";

    /**
     * 运行状态
     */
    String WORKING_OPERATION_KEY = "operation_status";

    /**
     * 电量
     */
    String BATTERY_KEY = "battery";

    /**
     * 行驶里程
     */
    String MILEAGE_KEY = "mileage";

    /**
     * 运行时间
     */
    String RUN_TIME_KEY = "runtime";

    /**
     * 音量大小
     */
    String VOLUME_SIZE_KEY = "volume_size";

    /**
     * 喷雾告警
     */
    String SPRAY_WARNING_KEY = "spray_warning";

    /**
     * 急停开关
     */
    String STOP_KEY = "stop";

    /**
     * 防撞条
     */
    String COLLISION_KEY = "collision";

    /**
     * 空闲状态
     */
    String IDLE_KEY = "idle";

    /**
     * 建图状态
     */
    String MAPPING_KEY = "mapping";

    /**
     * 充电状态
     */
    String CHARGING_KEY = "charging";

    /**
     * 更新时间
     */
    String UPDATE_TIME_KEY = "update_time";
}
