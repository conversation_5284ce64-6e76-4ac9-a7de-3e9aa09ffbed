package com.yx.robot.modules.admin.jike.enums;

/**
 * 订单状态枚举类
 * @date 2021/12/16
 * <AUTHOR>
 */
public enum OrderStateEnum {

    UN_START(1,"未开始"),

    DOING(2,"进行中"),

    FINISH(3, "已完成"),

    UN_FINISH(4,"未完成"),

    FAIL(5,"失败"),

    STOP(6,"暂停中");

    private Integer value;

    private String label;

    OrderStateEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
