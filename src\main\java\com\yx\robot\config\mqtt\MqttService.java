package com.yx.robot.config.mqtt;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class MqttService implements ApplicationRunner {

    private static MqttClient client;

    /**
     * MQTT连接属性配置对象
     */
    @Autowired
    public MqttConfig mqttConfig;

    @Override
    public void run(ApplicationArguments args) throws Exception {
        log.info("初始化启动MQTT连接");
        this.connect();
    }

    public void publish(String topic , String message) throws Exception {
        MqttMessage mqttMessage = new MqttMessage();
        mqttMessage.setQos(0);
        mqttMessage.setRetained(false);
        mqttMessage.setPayload(message.getBytes());
        MqttTopic mqttTopic = client.getTopic(topic);
        MqttDeliveryToken token = mqttTopic.publish(mqttMessage);
        token.waitForCompletion();
        log.info("message is published completely! " + token.isComplete());
    }

    /**
     *  用来连接服务器
     */
    private void connect() throws Exception{
        client = new MqttClient(mqttConfig.getHostUrl(),mqttConfig.getClientId(), new MemoryPersistence());
        MqttConnectOptions options = new MqttConnectOptions();
        options.setCleanSession(true);
        options.setUserName(mqttConfig.getUsername());
        options.setPassword(mqttConfig.getPassword().toCharArray());
        // 设置超时时间
        options.setConnectionTimeout(mqttConfig.getCompletionTimeout());
        // 设置会话心跳时间
        options.setKeepAliveInterval(20);
        try {
            String[] msgtopic = mqttConfig.getMsgTopic();
            //订阅消息
            int[] qos = new int[msgtopic.length];
            for (int i = 0; i < msgtopic.length; i++) {
                qos[i] = 0;
            }
            client.setCallback(new TopicMsgCallback(client,options,msgtopic,qos));
            client.connect(options);
            client.subscribe(msgtopic,qos);
            log.info("MQTT连接成功:" + mqttConfig.getClientId() + ":" + client);
        } catch (Exception e) {
            log.error("MQTT连接异常："+e);
        }
    }
}
