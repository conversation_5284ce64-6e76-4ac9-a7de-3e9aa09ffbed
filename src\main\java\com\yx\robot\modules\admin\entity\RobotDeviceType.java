package com.yx.robot.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yx.robot.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;

/**
 * <AUTHOR>
 */
@Data
@Entity
@Table(name = "t_robot_device_type")
@TableName("t_robot_device_type")
@ApiModel(value = "设备和点位关联信息")
public class RobotDeviceType extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("类型名称")
    private String typeName;

    @ApiModelProperty("设备类型")
    private Integer type;

    @ApiModelProperty("父设备类型")
    private Integer parentType;

    @ApiModelProperty("英文类型名称")
    private String enTypeName;

}