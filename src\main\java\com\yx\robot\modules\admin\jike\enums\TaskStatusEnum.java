package com.yx.robot.modules.admin.jike.enums;

/**
 * 任务状态枚举类
 * <AUTHOR>
 * @date 2021/12/14
 */
public enum TaskStatusEnum {

    GOTO_TARGET(1, "前往目的地"),

    ARRIVE_TARGET(2,"到达目的地，开始工作"),

    FINISH_WORK(3,"所在目的地，工作已完成"),

    BACK_ORIGIN(4,"返回出发地"),

    FAIL(5,"失败"),

    STOP(6,"暂停中");

    private Integer value;

    private String label;

    TaskStatusEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
