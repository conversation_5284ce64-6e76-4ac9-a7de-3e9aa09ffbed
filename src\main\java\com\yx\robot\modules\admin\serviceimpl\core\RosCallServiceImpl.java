package com.yx.robot.modules.admin.serviceimpl.core;

import cn.hutool.core.util.ArrayUtil;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._CallEntranceGuardReq;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosCallService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.StringUtil;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/28 15:29
 */
@Service
@Slf4j
@AllArgsConstructor
public class RosCallServiceImpl implements RosCallService {

    private RosBridgeService rosBridgeService;

    /**
     * 调用门禁服务
     *
     * @param typeCmd 类型：3设置门禁编号，1开门，2关门
     * @param params  编号
     * @return true:调用成功，false:调用失败
     */

    @Override
    public Boolean callEntranceGuard(Short typeCmd, Short[] params) {
        _CallEntranceGuardReq entranceGuardReq = new _CallEntranceGuardReq();
        entranceGuardReq.door_number = params;
        entranceGuardReq.cmd = typeCmd;
        String resultBool = rosBridgeService.callService(ServiceConstants.ENTRANCE_GUARD_SERVICE,
                Message.getMessageType(_CallEntranceGuardReq.class), JSONObject.toJSONString(entranceGuardReq));

        return StringUtil.isNotBlank(resultBool) && resultBool.contains(Boolean.TRUE.toString());
    }
}
