package ${entity.controllerPackage};

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import ${entity.entityPackage}.${entity.className};
import ${entity.servicePackage}.I${entity.className}Service;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "${entity.description}管理接口")
@RequestMapping("/yx/${entity.classNameLowerCase}")
@Transactional
public class ${entity.className}Controller {

    @Autowired
    private I${entity.className}Service i${entity.className}Service;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<${entity.className}> get(@PathVariable ${entity.primaryKeyType} id){

        ${entity.className} ${entity.classNameLowerCase} = i${entity.className}Service.getById(id);
        return new ResultUtil<${entity.className}>().setData(${entity.classNameLowerCase});
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<${entity.className}>> getAll(){

        List<${entity.className}> list = i${entity.className}Service.list();
        return new ResultUtil<List<${entity.className}>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<${entity.className}>> getByPage(@ModelAttribute PageVo page){

        IPage<${entity.className}> data = i${entity.className}Service.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<${entity.className}>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<${entity.className}> saveOrUpdate(@ModelAttribute ${entity.className} ${entity.classNameLowerCase}){

        if(i${entity.className}Service.saveOrUpdate(${entity.classNameLowerCase})){
            return new ResultUtil<${entity.className}>().setData(${entity.classNameLowerCase});
        }
        return new ResultUtil<${entity.className}>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable ${entity.primaryKeyType}[] ids){

        for(${entity.primaryKeyType} id : ids){
            i${entity.className}Service.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
