package com.yx.robot.common.constant;

/**
 * 服务调用相关常量
 *
 * <AUTHOR>
 * @date 2020/07/23
 */
public interface ServiceConstants {

    /**
     * 导航管理
     */
    String YX_NAV_CONTROL = "/nav_manage_node/yx_nav_control";

    /**
     * 地图管理
     */
    String YX_MAP_MANAGE = "/map_manage_node/yx_map_manage";

    /**
     * 充电桩控制
     * 消息类型：auto_dock.srv
     * <p>
     * int32 cmd #3：脱离充电桩
     * int32 cmd #4：结束返回充电
     * <p>
     * 注释：导航开始之前，或者建图情景下，保存充电桩服务调用成功后，需要脱离充电桩
     */
    String AUTO_DOCK = "/auto_dock_control";

    /**
     * 速度区域
     */
    String SPEED_AREA = "/speed_area";

    /**
     * 禁行区域
     */
    String FORBIDDEN_AREA = "/prohibition_area";

    /**
     * 设置速度
     */
    String SPEED_LEVEL = "/speed_level";

    /**
     * 点位校验
     */
    String POSE_CHECK_VALID = "/pose_check_valid";

    /**
     * 消毒模式
     */
    String DOCTOR_MODE = "/doctor_mode_node/doctor_mode";

    /**
     * 启动路径服务
     */
    String INIT_PATH_RECORD = "/init_path_record";

    /**
     * 局部路径
     */
    String ORIGINAL_PATH = "/original_path";

    /**
     * 梯控服务
     */
    String ELEVATOR_TASK = "/yl_elevator_collaboration_node/elevator_collaboration";

    /**
     * 更新充电桩 (暂时弃用)
     */
    String UPDATE_DOCK_POSE = "/update_dock_pose";

    /**
     * 休眠模式
     */
    String LOW_POWER = "/low_power";

    /**
     * 保存充电桩位置
     * <p>
     * 消息类型：save_dock_position.srv
     * <p>
     * 情景1：在建图服务调用成功之后，调用保存充电桩位置
     * 情景2：更换充电桩位置
     * <p>
     * int32 ret #‐1：获取位置失败
     * # 0：保存位置成功
     * geometry_msgs/Pose dock_pose #机器人在充电桩上的坐标
     * geometry_msgs/Pose dock_nav_pose #机器人返回充电导航点坐标
     * sting msg #提示信息
     */
    String SAVE_DOCK_POSITION = "/save_dock_position";

    /**
     * 开启雷达（以降低雷达功耗的方式）
     */
    String START_LIDAR = "/lidar1/start_motor";

    /**
     * 关闭雷达（以降低雷达功耗的方式）
     */
    String STOP_LIDAR = "/lidar1/stop_motor";

    /**
     * 深度相机休眠服务
     * 关闭相机：0
     * 开启相机：1
     */
    String DEPTH_CAMERA_SLEEP = "/camera/set_laser";

    /**
     * 获取路径规划
     */
    String NAV_MAKE_PLAN_PATH = "/move_base/GlobalPlanner/make_plan";

    /**
     * 自动重定位
     */
    String AUTO_RESET_POSE = "RunGlobalOptimization";

    /**
     * 深度相机获取图片
     */
    String CAPTURE_PICTURE = "/capture_picture";

    /**
     * 门禁服务
     */
    String ENTRANCE_GUARD_SERVICE = "/auto_door_control";


}
