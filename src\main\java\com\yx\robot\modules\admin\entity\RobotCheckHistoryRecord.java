package com.yx.robot.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yx.robot.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Column;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_check_history_record")
@ApiModel(value = "机器人检测历史记录")
public class RobotCheckHistoryRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "机器人类型")
    private Integer type;

    @ApiModelProperty(value = "检测类型")
    private Integer checkType;

    @ApiModelProperty(value = "总检测项")
    private Integer nums;

    @ApiModelProperty(value = "已检测项")
    private Integer hasCheckedNums;

    @ApiModelProperty(value = "待检测项")
    private Integer waitCheckedNums;

    @ApiModelProperty(value = "成功率")
    private Double successPercentage;

    @ApiModelProperty(value = "失败率")
    private Double failPercentage;

    @ApiModelProperty(value = "检测进度")
    private Double checkProgress;

    @ApiModelProperty(value = "厂商信息")
    private String factoryInfo;

    @ApiModelProperty(value = "操作用户")
    private String operationUser;

    @ApiModelProperty(value = "检测日期")
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date checkDate;

    @ApiModelProperty(value = "检测状态")
    private String status;

    @ApiModelProperty(value = "备注")
    private String comments;

    @ApiModelProperty(value = "排序值")
    @Column(precision = 10, scale = 2)
    private BigDecimal sortOrder;
}