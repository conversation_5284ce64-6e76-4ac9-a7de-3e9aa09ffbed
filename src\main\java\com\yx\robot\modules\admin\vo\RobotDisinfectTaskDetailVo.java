package com.yx.robot.modules.admin.vo;

import com.yx.robot.modules.admin.entity.RobotDisinfectTaskDetail;
import lombok.Data;

import java.util.Date;

/**
 * 消毒任务详情
 * <AUTHOR>
 * @date 2020/08/24
 */
@Data
public class RobotDisinfectTaskDetailVo {

    public RobotDisinfectTaskDetailVo(RobotDisinfectTaskDetail robotDisinfectTaskDetail) {
        this.robotTaskRecordId = robotDisinfectTaskDetail.getRobotTaskRecordId();
        this.disinfectType = robotDisinfectTaskDetail.getDisinfectType();
        this.wayDisinfectSpeed = robotDisinfectTaskDetail.getWayDisinfectSpeed();
        this.wayDisinfectSpray = robotDisinfectTaskDetail.getWayDisinfectSpray();
        this.wayDisinfectUlray = robotDisinfectTaskDetail.getWayDisinfectUlray();
        this.ulray = robotDisinfectTaskDetail.getUlray();
        this.spray = robotDisinfectTaskDetail.getSpray();
        this.xt = robotDisinfectTaskDetail.getXt();
        this.startTime = robotDisinfectTaskDetail.getStartTime();
        this.endTime = robotDisinfectTaskDetail.getEndTime();
        this.disinfectTime = robotDisinfectTaskDetail.getDisinfectTime();
    }

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 任务记录id
     */
    private String robotTaskRecordId;

    /**
     * 消毒类型
     */
    private Integer disinfectType;

    /**
     * 沿途速度
     */
    private Double wayDisinfectSpeed;

    /**
     * 沿途紫外线
     */
    private String wayDisinfectUlray;

    /**
     * 沿途喷雾
     */
    private String wayDisinfectSpray;

    /**
     * 紫外线
     */
    private String ulray;

    /**
     * 喷雾
     */
    private String spray;

    /**
     * 消毒模块
     */
    private String xt;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 消毒时间
     */
    private long disinfectTime;

}
