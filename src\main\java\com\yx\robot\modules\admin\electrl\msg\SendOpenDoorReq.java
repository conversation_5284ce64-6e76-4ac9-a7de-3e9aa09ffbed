package com.yx.robot.modules.admin.electrl.msg;

import com.yx.robot.modules.admin.electrl.constant.ElevatorFactoryConfig;
import lombok.Data;

/**
 * 开门请求
 *
 * <AUTHOR>
 * @date 2021/12/11
 */
@Data
public class SendOpenDoorReq extends BaseMsgReq {
    private String robotId = ElevatorFactoryConfig.ROBOT_ID;

    private String deviceUnique;

    /**
     * 0 松开按钮，即关门
     * 1-99 开门时长，单位秒
     */
    private String effectiveTime;

    /**
     * 位置信息
     */
    private String position;
}
