package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.RobotCheckProject;
import com.yx.robot.common.enums.RobotCheckType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.SnowFlakeUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotCheckHistoryItemMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotCheckHistoryRecordMapper;
import com.yx.robot.modules.admin.entity.RobotCheckHistoryItem;
import com.yx.robot.modules.admin.entity.RobotCheckHistoryRecord;
import com.yx.robot.modules.admin.message._SystemCheck;
import com.yx.robot.modules.admin.service.IRobotCheckHistoryRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.vo.CheckItemVo;
import com.yx.robot.modules.admin.vo.CheckReportVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 机器人检测历史记录接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotCheckHistoryRecordServiceImpl extends ServiceImpl<RobotCheckHistoryRecordMapper, RobotCheckHistoryRecord> implements IRobotCheckHistoryRecordService {

    @Autowired
    private RobotCheckHistoryRecordMapper robotCheckHistoryRecordMapper;

    @Autowired
    private RobotCheckHistoryItemMapper robotCheckHistoryItemMapper;

    @Value("${robot.factory-info}")
    private String factoryInfo;

    /**
     * 提交检测报告
     *
     * @param checkReportVo
     * @return
     */
    @Override
    public boolean submitCheckReport(CheckReportVo checkReportVo) {
        RobotCheckHistoryRecord robotCheckHistoryRecord = new RobotCheckHistoryRecord();
        robotCheckHistoryRecord.setCheckType(RobotCheckType.FACTORY_CHECK.getType());
        robotCheckHistoryRecord.setNums(checkReportVo.getNums());
        robotCheckHistoryRecord.setHasCheckedNums(checkReportVo.getHasCheckedNums());
        robotCheckHistoryRecord.setWaitCheckedNums(checkReportVo.getWaitCheckedNums());
        robotCheckHistoryRecord.setSuccessPercentage(checkReportVo.getSuccessPercentage());
        robotCheckHistoryRecord.setFailPercentage(checkReportVo.getFailPercentage());
        robotCheckHistoryRecord.setCheckProgress(checkReportVo.getCheckProgress());
        robotCheckHistoryRecord.setFactoryInfo(checkReportVo.getFactoryInfo());
        robotCheckHistoryRecord.setOperationUser(checkReportVo.getOperationUser());
        robotCheckHistoryRecord.setCheckDate(new Date());
        robotCheckHistoryRecord.setStatus(checkReportVo.isStatus() ? "1" : "0");
        robotCheckHistoryRecord.setComments(checkReportVo.getComments());
        int insert1 = robotCheckHistoryRecordMapper.insert(robotCheckHistoryRecord);
        int insert2 = 0;
        if(insert1 > 0) {
            List<CheckItemVo> checkItemVos = checkReportVo.getCheckItems();
            if(CollectionUtil.isNotEmpty(checkItemVos)) {
                for(CheckItemVo checkItemVo : checkItemVos) {
                    RobotCheckHistoryItem robotCheckHistoryItem = new RobotCheckHistoryItem();
                    robotCheckHistoryItem.setHistoryId(robotCheckHistoryRecord.getId());
                    robotCheckHistoryItem.setTitle(checkItemVo.getTitle());
                    robotCheckHistoryItem.setStep(checkItemVo.getStep());
                    robotCheckHistoryItem.setResponse(checkItemVo.getResponse());
                    robotCheckHistoryItem.setChecked(checkItemVo.isChecked() ? "1" : "0");
                    robotCheckHistoryItem.setStatus(checkItemVo.isStatus() ? "1" : "0");
                    insert2 = robotCheckHistoryItemMapper.insert(robotCheckHistoryItem);
                }
            }
        }
        if(insert1 > 0 && insert2 > 0) {
            return true;
        }else{
            return false;
        }
    }

    /**
     * 检测报告详情
     *
     * @return
     */
    @Override
    public List<CheckItemVo> checkReportDetail(String id) {
        List<CheckItemVo> checkItemVoList = new ArrayList<>();
        List<RobotCheckHistoryItem> robotCheckHistoryItems = robotCheckHistoryItemMapper.selectList(new LambdaQueryWrapper<RobotCheckHistoryItem>().eq(RobotCheckHistoryItem::getHistoryId, id));
        if(CollectionUtil.isNotEmpty(robotCheckHistoryItems)) {
            for(RobotCheckHistoryItem robotCheckHistoryItem : robotCheckHistoryItems) {
                CheckItemVo checkItemVo = new CheckItemVo();
                checkItemVo.setTitle(robotCheckHistoryItem.getTitle());
                checkItemVo.setResponse(robotCheckHistoryItem.getResponse());
                checkItemVo.setStep(robotCheckHistoryItem.getStep());
                String checked = robotCheckHistoryItem.getChecked();
                String status = robotCheckHistoryItem.getStatus();
                boolean checkedBool = checked == null ? false : checked.equals("1");
                boolean statusBool = status == null ? false : status.equals("1");
                checkItemVo.setChecked(checkedBool);
                checkItemVo.setStatus(statusBool);
                checkItemVoList.add(checkItemVo);
            }
        }
        return checkItemVoList;
    }

    /**
     * 获取检测数据
     *
     * @return
     */
    @Override
    public JSONObject getCheckData() {
        Jedis jedis = null;
        JSONObject result = null;
        try {
            jedis = RedisUtil.getJedis();
            String systemCheckStr = jedis.get(TOPIC + "::" + TopicConstants.SYSTEM_CHECK);
            if(StrUtil.isNotEmpty(systemCheckStr)) {
                result = JSONObject.parseObject(systemCheckStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return result;
    }

    /**
     * 处理节点检测
     * @param systemCheck
     * @param robotCheckHistoryId
     */
    private void handleNodesCheck(_SystemCheck systemCheck, String robotCheckHistoryId) {
        // 先判断节点
        String[] sensor_nodes = systemCheck.sensor_nodes;
        String[] task_nodes = systemCheck.task_nodes;
        // navigation 节点必须在地图加载成功才能正常启动
        //String[] navigation_nodes = systemCheck.navigation_nodes;
        // slam 节点必须在导航启动才能正常启动
        //String[] slam_nodes = systemCheck.slam_nodes;
        // 全部为空节点才算节点启动正常
        String[] nodes = ArrayUtil.addAll(sensor_nodes, task_nodes);
        RobotCheckHistoryItem robotCheckHistoryItem = new RobotCheckHistoryItem();
        robotCheckHistoryItem.setHistoryId(robotCheckHistoryId);
        robotCheckHistoryItem.setTitle(RobotCheckProject.NODES_CHECK.getTitle());
        robotCheckHistoryItem.setChecked("1");
        robotCheckHistoryItem.setStep(RobotCheckProject.NODES_CHECK.getStep());
        if(ArrayUtil.isNotEmpty(nodes)) {
            robotCheckHistoryItem.setResponse(ArrayUtil.join(nodes,","));
            robotCheckHistoryItem.setStatus("0");
        }else{
            robotCheckHistoryItem.setResponse(new String());
            robotCheckHistoryItem.setStatus("1");
        }
        robotCheckHistoryItemMapper.insert(robotCheckHistoryItem);
    }

    /**
     * 从检测信息中获取响应
     * @param systemCheck
     * @param robotCheckProject
     * @return
     */
    private String getResponseFromCheck(_SystemCheck systemCheck, RobotCheckProject robotCheckProject) {
        String response = "";
        if(robotCheckProject.getTitle().equals(RobotCheckProject.LIDAR_CHECK.getTitle())) {
            response = systemCheck.lidar;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.DEPTH_CHECK.getTitle())) {
            response = systemCheck.depth_camera;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.IMU_CHECK.getTitle())) {
            response = systemCheck.imu;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.MOTOR_CHECK.getTitle())) {
            response = systemCheck.motor;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.BMS_CHECK.getTitle())) {
            response = systemCheck.bms;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.COLLISION_CHECK.getTitle())) {
            response = systemCheck.collision;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.STOP_BTN_CHECK.getTitle())) {
            response = systemCheck.stop_btn;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.AUTO_CHARGE_CHECK.getTitle())) {
            response = systemCheck.auto_charge;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.SINGLECHIP_BOTTOM_CHECK.getTitle())) {
            response = systemCheck.singlechip_bottom;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.TURN_LIGHT_CHECK.getTitle())) {
            response = systemCheck.turn_light;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.POWER_KEY_CHECK.getTitle())) {
            response = systemCheck.power_key;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.SYSTEM_CHARGE_CHECK.getTitle())) {
            response = systemCheck.system_charge;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.EYE_CHECK.getTitle())) {
            response = systemCheck.eye;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.AD_CHECK.getTitle())) {
            response = systemCheck.ad;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.TOUCH_CHECK.getTitle())) {
            response = systemCheck.touch;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.DOOR_CHECK.getTitle())) {
            response = systemCheck.door;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.LED_LIGHT_CHECK.getTitle())) {
            response = systemCheck.led_light;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.INFRA_LIGHT_CHECK.getTitle())) {
            response = systemCheck.infra_light;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.SINGLECHIP_TOP_CHECK.getTitle())) {
            response = systemCheck.singlechip_top;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.PULSE_CHECK.getTitle())) {
            response = systemCheck.pulse;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.ULRAY_CHECK.getTitle())) {
            response = systemCheck.ulray;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.SPRAY_CHECK.getTitle())) {
            response = systemCheck.spray;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.FAN_CHECK.getTitle())) {
            response = systemCheck.fan;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.PUMP_CHECK.getTitle())) {
            response = systemCheck.pump;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.WIQUID_CHECK.getTitle())) {
            response = systemCheck.wiquid;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.DOCTOR_CHECK.getTitle())) {
            response = systemCheck.doctor;
        } else if(robotCheckProject.getTitle().equals(RobotCheckProject.ROLL_LED.getTitle())) {
            response = systemCheck.roll_led;
        }
        return response;
    }

    /**
     * 处理机器人相关设备检测
     * @param systemCheck
     * @param robotCheckHistoryId
     */
    private void handleRobotDeviceCheck(_SystemCheck systemCheck, String robotCheckHistoryId) {
        for(RobotCheckProject robotCheckProject : RobotCheckProject.values()) {
            if(robotCheckProject.getType().equals(1) || robotCheckProject.getType().equals(2) || robotCheckProject.getType().equals(3)) {
                RobotCheckHistoryItem robotCheckHistoryItem = new RobotCheckHistoryItem();
                robotCheckHistoryItem.setHistoryId(robotCheckHistoryId);
                robotCheckHistoryItem.setTitle(robotCheckProject.getTitle());
                robotCheckHistoryItem.setStep(robotCheckProject.getStep());
                String response = getResponseFromCheck(systemCheck,robotCheckProject);
                if(StrUtil.isEmpty(response)) {
                    robotCheckHistoryItem.setChecked("0");
                }else{
                    robotCheckHistoryItem.setChecked("1");
                    robotCheckHistoryItem.setResponse(response);
                    if(response.contains("S")) {
                        robotCheckHistoryItem.setStatus("1");
                    }else{
                        robotCheckHistoryItem.setStatus("0");
                    }
                }
                robotCheckHistoryItemMapper.insert(robotCheckHistoryItem);
            }
        }
    }

    /**
     * 处理机器人自检信息
     * @param robotCheckHistoryId
     */
    private void handleRobotCheckHistoryRecord(String robotCheckHistoryId) {
        RobotCheckHistoryRecord robotCheckHistoryRecord = new RobotCheckHistoryRecord();
        robotCheckHistoryRecord.setId(robotCheckHistoryId);
        robotCheckHistoryRecord.setCheckType(RobotCheckType.POWER_ON_CHECK.getType());
        List<RobotCheckHistoryItem> robotCheckHistoryItems = robotCheckHistoryItemMapper.selectList(new LambdaQueryWrapper<RobotCheckHistoryItem>().eq(RobotCheckHistoryItem::getHistoryId, robotCheckHistoryId));
        Integer nums = 0;
        Integer hasChecked = 0;
        Integer waitChecked = 0;
        Integer successNums = 0;
        Integer failNums = 0;
        if(CollectionUtil.isNotEmpty(robotCheckHistoryItems)) {
            nums = robotCheckHistoryItems.size();
            for(RobotCheckHistoryItem robotCheckHistoryItem : robotCheckHistoryItems) {
                if(robotCheckHistoryItem.getChecked().equals("1")) {
                    hasChecked++;
                }
                if(robotCheckHistoryItem.getChecked().equals("0")) {
                    waitChecked++;
                }
                if(robotCheckHistoryItem.getStatus() != null && robotCheckHistoryItem.getStatus().equals("1")) {
                    successNums++;
                }
                if(robotCheckHistoryItem.getStatus() == null || robotCheckHistoryItem.getStatus().equals("0")) {
                    failNums++;
                }
            }
        }
        robotCheckHistoryRecord.setNums(nums);
        robotCheckHistoryRecord.setHasCheckedNums(hasChecked);
        robotCheckHistoryRecord.setWaitCheckedNums(waitChecked);
        robotCheckHistoryRecord.setSuccessPercentage(new BigDecimal((double) successNums/nums * 100).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        robotCheckHistoryRecord.setFailPercentage(new BigDecimal((double) failNums/nums * 100).setScale(2, BigDecimal.ROUND_HALF_UP).doubleValue());
        robotCheckHistoryRecord.setCheckProgress(100.0d);
        robotCheckHistoryRecord.setFactoryInfo(factoryInfo);
        robotCheckHistoryRecord.setCheckDate(new Date());
        if(successNums == nums) {
            robotCheckHistoryRecord.setStatus("1");
        }else{
            robotCheckHistoryRecord.setStatus("0");
        }
        robotCheckHistoryRecord.setComments("这是机器人开机自检......");
        robotCheckHistoryRecordMapper.insert(robotCheckHistoryRecord);
    }

    /**
     * 机器人开机自检
     */
    @Override
    public void powerOnCheck() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String robotCheckHistoryId = String.valueOf(SnowFlakeUtil.getFlowIdInstance().nextId());
            String systemCheckStr = jedis.get(TOPIC + "::" + TopicConstants.SYSTEM_CHECK);
            if(StrUtil.isNotEmpty(systemCheckStr)) {
                _SystemCheck systemCheck = JSONObject.parseObject(systemCheckStr, _SystemCheck.class);
                // 处理节点检测
                handleNodesCheck(systemCheck,robotCheckHistoryId);
                // 处理机器人相关设备检测
                handleRobotDeviceCheck(systemCheck, robotCheckHistoryId);
                // 处理机器人开机自检相关信息
                handleRobotCheckHistoryRecord(robotCheckHistoryId);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 删除机器人检测历史记录
     *
     * @param id
     */
    @Override
    public void delete(String id) {
        robotCheckHistoryRecordMapper.deleteById(id);
        robotCheckHistoryItemMapper.delete(new LambdaQueryWrapper<RobotCheckHistoryItem>().eq(RobotCheckHistoryItem::getHistoryId,id));
    }
}