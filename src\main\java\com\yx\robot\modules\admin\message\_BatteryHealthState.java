package com.yx.robot.modules.admin.message;

@MessageType(string = "riki_msgs/battery_state")
public class _BatteryHealthState extends Message{
    public Header header;
    // 电压
    public float voltage;
    // 电流
    public float current;
    // 剩余容量
    public float residual_charge;
    // 标准容量
    public float nominal_capacity;
    // 循环次数
    public short cycle_index;
    // 生产日期
    public String manufacture_date;
    // 均衡状态
    public float[] equilibrium_state;
    // 均衡状态_高
    public float[] high_equilibrium_state;
    // 保护状态
    public float[] protection_state;
    // 软件版本
    public String software_version;
    // 剩余容量百分比
    public float percentage;
    // FET控制状态
    public float[] FET_control_state;
    // 温度传感器数
    public short number_NTC;
    // 温度数值
    public float[] temperature_NTC;
    // 电池单体电压
    public float[] cell_voltage;
    // 电池健康状态
    public short power_supply_health;
    // 电池类型
    public short power_supply_technology;
}
