package com.yx.robot.modules.admin.serviceimpl;

import com.yx.robot.modules.admin.dao.mapper.RobotTaskItemMapper;
import com.yx.robot.modules.admin.entity.RobotTaskItem;
import com.yx.robot.modules.admin.service.IRobotTaskItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 机器人列表接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotTaskItemServiceImpl extends ServiceImpl<RobotTaskItemMapper, RobotTaskItem> implements IRobotTaskItemService {

    @Autowired
    private RobotTaskItemMapper robotTaskItemMapper;
}