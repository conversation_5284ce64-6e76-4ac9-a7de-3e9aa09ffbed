package com.yx.robot.modules.admin.dao.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.yx.robot.modules.admin.entity.RobotPositionDevRelation;
import org.apache.ibatis.annotations.Param;

import java.util.Map;

/**
 * 设备和点位关联信息数据处理层
 *
 * <AUTHOR>
 */
public interface RobotPositionDevRelationMapper extends BaseMapper<RobotPositionDevRelation> {

    /**
     * 根据位置ID和设备类型获取设备ID
     *
     * @param params positionId 位置ID，type:类型，subType:子类型
     * @return 设备ID
     */
    String getDeviceIdByPositionAndDeviceType(@Param("params") Map<String, String> params);
}