package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_location")
@ApiModel(value = "设备标注位置")
public class RobotLocation extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "父级id")
    private String parentId;

    @ApiModelProperty(value = "位置区域")
    private String locationInfo;

    @ApiModelProperty(value = "位置编号")
    private String locationCode;

    @ApiModelProperty(value = "标定点id")
    private String positionId;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}