package com.yx.robot.modules.admin.electrl.enums;

/**
 * 加密算法类型
 *
 * <AUTHOR>
 */
public enum EncryptTypeEnum {

    /**
     * 0, "签名用md5算法，加密用AES算法"
     */
    MODE_0(0, "签名用md5算法，加密用AES算法"),

    /**
     * 1, "签名用国密SM3算法,加密用国密SM4算法"
     */
    MODE_1(1, "签名用国密SM3算法,加密用国密SM4算法");

    private final Integer value;

    private final String label;

    EncryptTypeEnum(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }
}
