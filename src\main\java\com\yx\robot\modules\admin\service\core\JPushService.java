package com.yx.robot.modules.admin.service.core;

import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.PushPayload;
import com.yx.robot.common.enums.MsgPushTemplate;

import java.util.Collection;

/**
 * 极光推送服务类
 * <AUTHOR>
 * @date 2020/02/15
 */
public interface JPushService {

    /**
     * 构建push对象
     * @return
     */
    PushPayload buildPushObjectByAlias(String alertTitle, String alertContent, String alias, Message message);

    /**
     * 自定义消息模板
     * @param msgPushTemplate
     * @param subVal
     * @return
     */
    MsgPushTemplate defineValue(MsgPushTemplate msgPushTemplate, String subVal);

//    /**
//     * 推送消息
//     * @param msgPushTemplate
//     * @return
//     */
//    boolean pushMsg(MsgPushTemplate msgPushTemplate);
}
