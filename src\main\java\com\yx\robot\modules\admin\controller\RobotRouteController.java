package com.yx.robot.modules.admin.controller;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.common.enums.RobotOperationStatus;
import com.yx.robot.common.enums.SceneType;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.RobotRouteInfoDto;
import com.yx.robot.modules.admin.entity.RobotDisinfect;
import com.yx.robot.modules.admin.entity.RobotRoute;
import com.yx.robot.modules.admin.service.IRobotDisinfectService;
import com.yx.robot.modules.admin.service.IRobotMotorService;
import com.yx.robot.modules.admin.service.IRobotRouteService;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.RobotRouteVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.yx.robot.common.constant.ControlStatusConstants.CREATE_MAP_STATUE;
import static com.yx.robot.common.constant.ControlStatusConstants.IS_NEED_GO_CHARGING_LOW_POWER;
import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人路线管理接口")
@RequestMapping("/yx/api-v1/robotRoute")
@Transactional
public class RobotRouteController {

    @Autowired
    private IRobotRouteService iRobotRouteService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Autowired
    private IRobotStatusService iRobotStatusService;


    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotRoute> get(@PathVariable String id) {
        RobotRoute robotRoute = iRobotRouteService.getById(id);
        return new ResultUtil<RobotRoute>().setData(robotRoute);
    }

    @RequestMapping(value = "/initPathRecord", method = RequestMethod.GET)
    @ApiOperation(value = "初始化路径录制服务")
    public Result<Boolean> initPathRecord(@RequestParam Integer operation) {
        //解决低电量状态下，编辑路线会触发空闲状态返回充电的BUG；
        CREATE_MAP_STATUE = true;
        // 点击的是录制路径
        if (operation == 1) {
            //解锁电机
            iRobotMotorService.motorControl(false);
        }
        if (operation == 0) {
            //解决低电量状态下，编辑路线会触发空闲状态返回充电的BUG；
            CREATE_MAP_STATUE = false;
            IS_NEED_GO_CHARGING_LOW_POWER = Boolean.TRUE;
        }
        boolean b = iRobotRouteService.initPathRecord(operation);
        if (b) {
            return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotRoute>> getAll() {
        List<RobotRoute> robotRouteList = new ArrayList<>();
        Object o = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (o != null && StrUtil.isNotBlank(o.toString())) {
            LambdaQueryWrapper<RobotRoute> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(RobotRoute::getMapId, o.toString());
            robotRouteList = iRobotRouteService.list(queryWrapper);
        }
        return new ResultUtil<List<RobotRoute>>().setData(robotRouteList);
    }

    @RequestMapping(value = "/updateDefaultRoute", method = RequestMethod.GET)
    @ApiOperation(value = "更新默认消毒路线")
    public Result<Boolean> updateDefaultRoute(@RequestParam String id) {
        RobotRoute byId = iRobotRouteService.getById(id);
        Object o = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (byId != null && o != null) {
            byId.setIsDefault(SUCCESS);
            iRobotRouteService.updateById(byId);
            List<RobotRoute> robotRoutes = iRobotRouteService.list(new LambdaQueryWrapper<RobotRoute>()
                    .eq(RobotRoute::getMapId, o.toString())
                    .eq(RobotRoute::getIsDefault, SUCCESS));
            if (CollectionUtil.isNotEmpty(robotRoutes)) {
                for (RobotRoute route : robotRoutes) {
                    // 更新之前的路径
                    if (!route.getId().equals(byId.getId())) {
                        route.setIsDefault(FAIL);
                        iRobotRouteService.updateById(route);
                    }
                }
            }
        }
        return new ResultUtil<Boolean>().setData(true);
    }

    @RequestMapping(value = "/getAllRoute", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部路径数据二级结构")
    public Result<List<RobotRouteInfoDto>> getAllRoute() {
        List<RobotRouteInfoDto> robotRouteInfoDtoList = iRobotRouteService.getAllRoute();
        return new ResultUtil<List<RobotRouteInfoDto>>().setData(robotRouteInfoDtoList);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotRoute>> getByPage(@ModelAttribute PageVo page, @RequestParam String mapId) {
        QueryWrapper<RobotRoute> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(page.getName())) {
            queryWrapper.like("name", page.getName());
        }
        queryWrapper.eq("map_id", mapId);
        IPage<RobotRoute> data = iRobotRouteService.page(PageUtil.initMpPage(page), queryWrapper);
        return new ResultUtil<IPage<RobotRoute>>().setData(data);
    }

    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @ApiOperation(value = "添加路线")
    public Result<Boolean> save(@RequestBody RobotRouteVo robotRouteVo) {
        //解决低电量状态下，编辑路线会触发空闲状态返回充电的BUG；
        CREATE_MAP_STATUE = false;
        IS_NEED_GO_CHARGING_LOW_POWER = Boolean.TRUE;
        RobotRoute robotRoute = iRobotRouteService.save(robotRouteVo);
        if (robotRoute != null) {
            rosWebService.sendVoicePrompt(SceneType.SETTING_ROUTE_TASK, null);
            return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/edit", method = RequestMethod.PUT)
    @ApiOperation(value = "编辑路线")
    public Result<Boolean> edit(@RequestBody RobotRouteVo robotRouteVo) {
        boolean res = iRobotRouteService.edit(robotRouteVo);
        if (res) {
            return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/saveOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotRoute> saveOrUpdate(@ModelAttribute RobotRoute robotRoute) {

        if (iRobotRouteService.saveOrUpdate(robotRoute)) {
            return new ResultUtil<RobotRoute>().setData(robotRoute);
        }
        return new ResultUtil<RobotRoute>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {
        if (RobotOperationStatus.RUNNING.equals(iRobotStatusService.getOperationStatus()) ||
                RobotOperationStatus.PAUSE.equals(iRobotStatusService.getOperationStatus())) {
            return new ResultUtil<>().setErrorMsg("操作失败,任务执行中不允许删除路线！");
        }
        log.info("删除路线信息：{}", JSON.toJSONString(ids));
        for (String id : ids) {
            // 删除路径
            iRobotRouteService.removeById(id);
            // 删除和路径相关的相关信息
            iRobotDisinfectService.remove(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotLocationId, id));
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
