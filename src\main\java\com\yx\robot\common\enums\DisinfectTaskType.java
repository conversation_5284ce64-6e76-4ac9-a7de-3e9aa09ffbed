package com.yx.robot.common.enums;

/**
 * 消毒任务类型
 * <AUTHOR>
 * @date 2020/07/31
 */
public enum DisinfectTaskType {

    /**
     * 0,"定点任务"
     */
    FIXED_POINT_TASK(0,"定点任务"),

    /**
     * 1,"常规任务"
     */
    ROUTINE_TASK(1,"常规任务"),

    /**
     * 2,"局部任务"
     */
    LOCAL_TASK(2,"局部任务"),

    /**
     * 3,"巡线任务"
     */
    LINE_PATROL_TASK(3,"巡线任务"),

    /**
     * 5,"自定义巡线任务"
     */
    DEFINE_LINE_TASK(5,"自定义巡线任务");

    private final Integer type;

    private final String label;

    DisinfectTaskType(Integer type, String label) {
        this.type = type;
        this.label = label;
    }

    public Integer getType() {
        return type;
    }

    public String getLabel() {
        return label;
    }
}
