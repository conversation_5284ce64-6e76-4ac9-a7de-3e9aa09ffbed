package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_position")
@ApiModel(value = "机器人")
public class RobotPosition extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "父级id")
    private String parentId;

    @ApiModelProperty(value = "点位名称")
    private String name;

    @ApiModelProperty(value = "地图id")
    private String mapId;

    @ApiModelProperty(value = "世界坐标id")
    private String worldPoseId;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}