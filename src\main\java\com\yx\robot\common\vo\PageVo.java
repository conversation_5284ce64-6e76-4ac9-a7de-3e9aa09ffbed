package com.yx.robot.common.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class PageVo implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "页号")
    private int pageNumber;

    @ApiModelProperty(value = "页面大小")
    private int pageSize;

    @ApiModelProperty(value = "页号")
    private int pageNo;

    @ApiModelProperty(value = "排序字段")
    private String sort;

    @ApiModelProperty(value = "排序方式 asc/desc")
    private String order;

    @ApiModelProperty(value = "名称")
    private String name;

    public int getPageNumber() {
        return pageNumber > pageNo ? pageNumber : pageNo;
    }

    public String getOrder() {
        return StringUtils.isBlank(order) ? "desc" : order;
    }

    public String getSort() {
        return StringUtils.isBlank(sort) ? "id" : sort;
    }

    public String getSortOrigin() {
        return sort;
    }
}
