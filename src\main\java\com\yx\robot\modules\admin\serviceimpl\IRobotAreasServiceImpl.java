package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.enums.RobotAreasType;
import com.yx.robot.common.enums.SceneType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.SecurityUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotAreasMapper;
import com.yx.robot.modules.admin.entity.RobotAreas;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.IRobotAreasService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.vo.RobotAreaVo;
import com.yx.robot.modules.base.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.ServiceConstants.*;
import static com.yx.robot.common.constant.TopicConstants.*;

/**
 * 机器人区域接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotAreasServiceImpl extends ServiceImpl<RobotAreasMapper, RobotAreas> implements IRobotAreasService {

    @Autowired
    private RobotAreasMapper robotAreasMapper;

    @Autowired
    private SecurityUtil securityUtil;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Override
    public boolean add(RobotAreaVo robotAreaVo) {
        try {
            Object mapObj = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
            String mapId = mapObj == null ? "" : mapObj.toString();
            QueryWrapper queryWrapper = new QueryWrapper();
            queryWrapper.eq("map_id", mapId);
            List<RobotAreas> robotSpeedList = robotAreasMapper.selectList(queryWrapper);
            RobotAreas robotAreas = new RobotAreas();
            User user = securityUtil.getCurrUser();
            if (null != user) {
                robotAreas.setCreateBy(user.getUsername());
            }
            robotAreas.setType(robotAreaVo.getType());
            robotAreas.setCreateTime(new Date());
            robotAreas.setOrderNumber(robotSpeedList.size() + 1 + "");
            robotAreas.setMapId(mapId);
            robotAreas.setMaxLineSpeed(robotAreaVo.getMaxLineSpeed());
            robotAreas.setMaxAngularSpeed(robotAreaVo.getMaxAngularSpeed());
            robotAreas.setPositions(JSONObject.toJSONString(robotAreaVo.getPositions()));
            int res = robotAreasMapper.insert(robotAreas);
            if (res > 0) {
                return true;
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return false;
    }

    /**
     * 同步机器人区域
     *
     * @param type
     * @return
     */
    @Override
    public boolean syncRobotAreas(Integer type) {
        Object mapObj = stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO,CURRENT_MAP);
        String mapId = mapObj == null ? "" : mapObj.toString();
        if(StrUtil.isEmpty(mapId)) {
            log.error("无法获取地图，无法进行同步");
            return false;
        }
        List<RobotAreas> robotAreasList = robotAreasMapper.selectList(
                new LambdaQueryWrapper<RobotAreas>().eq(RobotAreas::getType,type).eq(RobotAreas::getMapId,mapId)
        );
        if(robotAreasList == null) {
            robotAreasList = new ArrayList<>();
        }
        _RobotAreasReq robotAreasReq = getRobotAreasReq(robotAreasList);
        try {
            String s = "";
            if(type.equals(RobotAreasType.SPEED_AREA.getType())) {
                s = rosBridgeService.callService(SPEED_AREA,Message.getMessageType(_RobotAreasReq.class), JSON.toJSONString(robotAreasReq));
            }
            if(type.equals(RobotAreasType.FORBIDDEN_AREA.getType())) {
                s = rosBridgeService.callService(FORBIDDEN_AREA,Message.getMessageType(_RobotAreasReq.class), JSON.toJSONString(robotAreasReq));
            }
            if (StrUtil.isNotEmpty(s)) {
                _RobotAreasRep robotSpeedAreasRep = JSON.parseObject(s, _RobotAreasRep.class);
                int ret = robotSpeedAreasRep.ret;
                if(ret == 0) {
                    return true;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            log.error("调用速度区域异常");
            return false;
        }
        return false;
    }

    /**
     * 初始化机器人区域
     *
     * @param type
     */
    @Override
    public void initRobotAreas(Integer type) {
        if(type == null) {
            // 同步速度区域
            boolean result1 = syncRobotAreas(RobotAreasType.SPEED_AREA.getType());
            // 同步禁行区域
            boolean result2 = syncRobotAreas(RobotAreasType.FORBIDDEN_AREA.getType());
            // 同步膨胀区域
            boolean result3 = syncRobotAreas(RobotAreasType.EXPANSION_AREA.getType());
            if(result1) {
                log.info("同步速度区域成功");
            }
            if(result2) {
                log.info("同步禁行区域成功");
            }
            if(result3) {
                log.info("同步膨胀区域成功");
            }
        }else{
            boolean b = syncRobotAreas(type);
            if(b) {
                log.info("同步区域成功");
            }
        }
    }

    /**
     * 机器人区域列表
     * @param robotAreasList
     * @return
     */
    private _RobotAreasReq getRobotAreasReq(List<RobotAreas> robotAreasList) {
        _RobotAreasReq robotSpeedAreasReq = new _RobotAreasReq();
        List<_SpeedArea> speedAreas = new ArrayList<>();
        robotAreasList.forEach(item->{
            _SpeedArea speedArea = new _SpeedArea();
            speedArea.max_angular_speed = item.getMaxAngularSpeed();
            speedArea.max_line_speed = item.getMaxLineSpeed();
            String positions = item.getPositions();
            List<_Point> points = JSONObject.parseArray(positions, _Point.class);
            _Point[] pointArr = ArrayUtil.toArray(points, _Point.class);
            speedArea.points = pointArr;
            speedAreas.add(speedArea);
        });
        robotSpeedAreasReq.speedAreas = ArrayUtil.toArray(speedAreas,_SpeedArea.class);
        return robotSpeedAreasReq;
    }

    @Override
    public Boolean fallControl(Boolean ctrl) {
        _Bool bool = new _Bool();
        bool.data = ctrl;
        rosBridgeService.publish(ROBOT_FALL_ON, Message.getMessageType(_Bool.class), JSON.toJSONString(bool));
        return true;
    }

    @Override
    public Boolean fallState() {
        String val = RedisUtil.getTopicValue(ROBOT_FALL_STATE);
        if(StringUtils.isNotBlank(val)){
            _Bool bool = JSON.parseObject(val, _Bool.class);
            return bool.data;
        }
        return null;
    }
}