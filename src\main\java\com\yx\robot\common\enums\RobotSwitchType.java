package com.yx.robot.common.enums;

/**
 * 开关机类型
 *
 * <AUTHOR>
 * @date 2021/01/04
 */
public enum RobotSwitchType {

    /**
     * 0,"低电量"
     */
    LOW_BATTERY(0, "低电量"),

    /**
     * 1,"手动"
     */
    MANUAL(1, "手动"),

    /**
     * 2,"自动"
     */
    AUTO(2, "自动"),

    /**
     * 3,"远程"
     */
    REMOTE(3, "远程");

    private final Integer type;

    private final String label;

    public Integer getType() {
        return type;
    }

    public String getLabel() {
        return label;
    }

    RobotSwitchType(Integer type, String label) {
        this.type = type;
        this.label = label;
    }
}
