package com.yx.robot.common.utils;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.yx.robot.modules.admin.jike.constant.ApiConstants;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.web.client.AsyncRestTemplate;
import org.springframework.web.client.RestTemplate;
import java.util.Iterator;
import java.util.Map;
/**
 * 调用 Restful 接口 Util
 *
 * <AUTHOR>
 * @daxi 2019-12-30
 */
@Slf4j
public class RestUtil {

    /**
     * RestAPI 调用器
     */
    private static RestTemplate RT = new RestTemplate();

    private static AsyncRestTemplate ASRT = new AsyncRestTemplate();

    private static HttpComponentsClientHttpRequestFactory httpRequestFactory = new HttpComponentsClientHttpRequestFactory();

    static {
        httpRequestFactory.setConnectionRequestTimeout(10 * 3000);
        httpRequestFactory.setConnectTimeout(10 * 3000);
        httpRequestFactory.setReadTimeout(10 * 3000);
        RT.setRequestFactory(httpRequestFactory);
    }

    public static AsyncRestTemplate getAsyncRestTemplate() {
        return ASRT;
    }

    public static RestTemplate getRestTemplate() {
        return RT;
    }

    /**
     * 发送 get 请求
     */
    public static JSONObject get(String url) {
        return getNative(url, null, null).getBody();
    }

    /**
     * 发送 get 请求
     */
    public static JSONObject get(String url, JSONObject variables) {
        return getNative(url, variables, null).getBody();
    }

    /**
     * 发送 get 请求
     */
    public static JSONObject get(String url, JSONObject variables, JSONObject params) {
        return getNative(url, variables, params).getBody();
    }

    /**
     * 发送 get 请求，返回原生 ResponseEntity 对象
     */
    public static ResponseEntity<JSONObject> getNative(String url, JSONObject variables, JSONObject params) {
        return request(url, HttpMethod.GET, variables, params);
    }

    /**
     * 发送 Post 请求
     */
    public static JSONObject post(String url) {
        return postNative(url, null, null).getBody();
    }

    /**
     * 发送 Post 请求
     */
    public static JSONObject post(String url, JSONObject params) {
        log.info("地址：{},参数：{}",url, JSONObject.toJSONString(params));
        return postNative(url, null, params).getBody();
    }

    /**
     * 发送 Post 请求
     */
    public static JSONObject post(String url, JSONObject variables, JSONObject params) {
        log.info("地址：{},参数：{}",url, JSONObject.toJSONString(params));
        return postNative(url, variables, params).getBody();
    }


    /**
     * 发送 Post 请求
     */
    public static JSONObject post(String url, HttpHeaders httpHeaders, JSONObject variables, JSONObject params) {
        log.info("地址：{},参数：{}",url, JSONObject.toJSONString(params));
        return postNative(url, httpHeaders, variables,params).getBody();
    }

    public static ListenableFuture<ResponseEntity<JSONObject>> asyncPost(String url, HttpHeaders httpHeaders, JSONObject variables, JSONObject params) {
        log.info("地址：{},参数：{}",url, JSONObject.toJSONString(params));
        return asyncPostNative(url, httpHeaders, variables,params);
    }

    /**
     * 通过form发送post请求
     */
    public static JSONObject doPostForForm(String url, LinkedMultiValueMap<String, String> form) {
        log.info("地址：{},参数：{}",url, JSONObject.toJSONString(form));
        // 1.创建请求头
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);
        headers.add(ApiConstants.TOKEN_HEADER, ApiConstants.GET_TOKEN_BY_TOKEN);

        //将请求头部和参数合成一个请求
        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity(form, headers);

        ResponseEntity<JSONObject> response = new RestTemplate().postForEntity(url, requestEntity, JSONObject.class);

        return response.getBody();
    }

    /**
     * 发送 POST 请求，返回原生 ResponseEntity 对象
     */
    public static ResponseEntity<JSONObject> postNative(String url, JSONObject variables, JSONObject params) {
        return request(url, HttpMethod.POST, variables, params);
    }

    /**
     * 发送 POST 请求，返回原生 ResponseEntity 对象
     */
    public static ResponseEntity<JSONObject> postNative(String url, HttpHeaders httpHeaders, JSONObject variables, JSONObject params) {
        return request(url, HttpMethod.POST, httpHeaders, variables, params);
    }

    public static ListenableFuture<ResponseEntity<JSONObject>> asyncPostNative(String url, HttpHeaders httpHeaders, JSONObject variables, JSONObject params) {
        return asyncRequest(url, HttpMethod.POST, httpHeaders, variables, params);
    }

    /**
     * 发送 put 请求
     */
    public static JSONObject put(String url) {
        return putNative(url, null, null).getBody();
    }

    /**
     * 发送 put 请求
     */
    public static JSONObject put(String url, JSONObject params) {
        return putNative(url, null, params).getBody();
    }

    /**
     * 发送 put 请求
     */
    public static JSONObject put(String url, JSONObject variables, JSONObject params) {
        return putNative(url, variables, params).getBody();
    }

    /**
     * 发送 put 请求，返回原生 ResponseEntity 对象
     */
    public static ResponseEntity<JSONObject> putNative(String url, JSONObject variables, JSONObject params) {
        return request(url, HttpMethod.PUT, variables, params);
    }

    /**
     * 发送 delete 请求
     */
    public static JSONObject delete(String url) {
        return deleteNative(url, null, null).getBody();
    }

    /**
     * 发送 delete 请求
     */
    public static JSONObject delete(String url, JSONObject variables, JSONObject params) {
        return deleteNative(url, variables, params).getBody();
    }

    /**
     * 发送 delete 请求，返回原生 ResponseEntity 对象
     */
    public static ResponseEntity<JSONObject> deleteNative(String url, JSONObject variables, JSONObject params) {
        return request(url, HttpMethod.DELETE, null, variables, params, JSONObject.class);
    }

    /**
     * 发送请求
     */
    public static ResponseEntity<JSONObject> request(String url, HttpMethod method, JSONObject variables, JSONObject params) {
        return request(url, method, getHeaderApplicationJson(), variables, params, JSONObject.class);
    }

    /**
     * 发送请求
     */
    public static ResponseEntity<JSONObject> request(String url, HttpMethod method, HttpHeaders httpHeaders, JSONObject variables, JSONObject params) {
        httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE));
        httpHeaders.add("Accept", MediaType.APPLICATION_JSON_UTF8_VALUE);
        return request(url, method, httpHeaders, variables, params, JSONObject.class);
    }

    public static ListenableFuture<ResponseEntity<JSONObject>> asyncRequest(String url, HttpMethod method, HttpHeaders httpHeaders, JSONObject variables, JSONObject params) {
        httpHeaders.setContentType(MediaType.parseMediaType(MediaType.APPLICATION_JSON_UTF8_VALUE));
        httpHeaders.add("Accept", MediaType.APPLICATION_JSON_UTF8_VALUE);
        return asyncToRequest(url, method, httpHeaders, variables, params,JSONObject.class);
    }



    /**
     * 发送请求
     *
     * @param url          请求地址
     * @param method       请求方式
     * @param headers      请求头  可空
     * @param variables    请求url参数 可空
     * @param params       请求body参数 可空
     * @return ResponseEntity<responseType>
     */
    public static <T> ListenableFuture<ResponseEntity<T>> asyncToRequest(String url, HttpMethod method, HttpHeaders headers, JSONObject variables, JSONObject params,Class<T> responseType) {
        if (StringUtils.isEmpty(url)) {
            throw new RuntimeException("url 不能为空");
        }
        if (method == null) {
            throw new RuntimeException("method 不能为空");
        }
        if (headers == null) {
            headers = new HttpHeaders();
        }
        // 请求体
        String body = "";
        if (params != null) {
            body = params.toJSONString();
        }
        // 拼接 url 参数
        if (variables != null) {
            url += ("?" + asUrlVariables(variables));
        }
        // 发送请求
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        ListenableFuture<ResponseEntity<T>> exchange = ASRT.exchange(url, method, request, responseType);
        return exchange;
    }

    public static <T> ResponseEntity<T> request(String url, HttpMethod method, HttpHeaders headers, JSONObject variables, JSONObject params, Class<T> responseType) {
        if (StringUtils.isEmpty(url)) {
            throw new RuntimeException("url 不能为空");
        }
        if (method == null) {
            throw new RuntimeException("method 不能为空");
        }
        if (headers == null) {
            headers = new HttpHeaders();
        }
        // 请求体
        String body = "";
        if (params != null) {
            body = params.toJSONString();
        }
        // 拼接 url 参数
        if (variables != null) {
            url += ("?" + asUrlVariables(variables));
        }
        // 发送请求
        HttpEntity<String> request = new HttpEntity<>(body, headers);
        return RT.exchange(url, method, request, responseType);
    }

    /**
     * 获取JSON请求头
     */
    private static HttpHeaders getHeaderApplicationJson() {
        return getHeader(MediaType.APPLICATION_JSON_UTF8_VALUE);
    }

    /**
     * 获取请求头
     */
    private static HttpHeaders getHeader(String mediaType) {
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.parseMediaType(mediaType));
        headers.add("Accept", mediaType);
        return headers;
    }

    /**
     * 将 JSONObject 转为 a=1&b=2&c=3...&n=n 的形式
     */
    public static String asUrlVariables(JSONObject variables) {
        Map<String, Object> source = variables.getInnerMap();
        Iterator<String> it = source.keySet().iterator();
        StringBuilder urlVariables = new StringBuilder();
        while (it.hasNext()) {
            String key = it.next();
            String value = "";
            Object object = source.get(key);
            if (object != null) {
                if (!StringUtils.isEmpty(object.toString())) {
                    value = object.toString();
                }
            }
            urlVariables.append("&").append(key).append("=").append(value);
        }
        // 去掉第一个&
        return urlVariables.substring(1);
    }
}
