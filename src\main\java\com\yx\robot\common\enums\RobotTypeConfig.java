package com.yx.robot.common.enums;

/**
 * 机器人类型
 *
 * <AUTHOR>
 * @date 2020/07/30
 */
public enum RobotTypeConfig {

    /**
     * 机器人类型
     */
    X1("X1", 0.3),

    X2("X2", 0.3),

    X3("X3", 0.3),

    X4("X4", 0.3),

    X5("X5", 0.3),

    M1("M1", 0.3),

    M2("M2", 0.3),

    M3("M3", 0.3),

    M4("M4", 0.3),

    M5("M5", 0.3),

    U1("U1", 0.3),

    U2("U2", 0.3);

    private final String type;

    private final double speed;

    RobotTypeConfig(String type, double speed) {
        this.type = type;
        this.speed = speed;
    }

    public String getType() {
        return this.type;
    }

    public double getSpeed() {
        return this.speed;
    }
}
