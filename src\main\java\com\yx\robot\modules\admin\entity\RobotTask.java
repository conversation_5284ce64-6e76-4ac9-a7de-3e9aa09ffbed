package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("t_robot_task")
@ApiModel(value = "机器人任务信息")
public class RobotTask extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "uuid")
    private String uuId;

    @ApiModelProperty(value = "任务名称")
    private String name;

    @ApiModelProperty(value = "地图Id")
    private String mapId;

    @ApiModelProperty(value = "类型")
    private Integer type;

    @ApiModelProperty(value = "子类型")
    private Integer subType;

    @ApiModelProperty(value = "等级(常规任务,紧急任务)")
    private Integer level;

    @ApiModelProperty(value = "是否定时")
    private Integer isFixedTime;

    @ApiModelProperty(value = "星期数")
    private String weekdays;

    @ApiModelProperty(value = "开始时间")
    private String startTime;

    @ApiModelProperty(value = "定时周期")
    private String period;

    @ApiModelProperty(value = "循环次数")
    private Integer loops;

    @ApiModelProperty(value = "是否默认：0 是/1 否")
    private String isDefault;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;

    @ApiModelProperty(value = "任务可执行，开始时间")
    private String executableStartTime;

    @ApiModelProperty(value = "任务可执行，结束时间")
    private String executableEndTime;
}