package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.modules.admin.dao.mapper.RobotDeviceTypeMapper;
import com.yx.robot.modules.admin.entity.RobotDeviceType;
import com.yx.robot.modules.admin.service.IRobotDeviceTypeService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 设备和点位关联信息接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class IRobotDeviceTypeServiceImpl extends ServiceImpl<RobotDeviceTypeMapper, RobotDeviceType> implements IRobotDeviceTypeService {

    /**
     * 根据父级类型获取设备类型列表
     *
     * @param parentType 父级类型，规定：值为null时，获取所有设备类型，值为-1时获取所有一级类型
     * @return list
     */
    @Override
    public List<RobotDeviceType> getList(Integer parentType) {
        QueryWrapper<RobotDeviceType> queryWrapper = new QueryWrapper<>();
        if (ObjectUtil.isNotNull(parentType)) {
            queryWrapper.eq("parent_type", parentType);
        }
        return this.list(queryWrapper);
    }
}