package com.yx.robot.common.utils;

import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._EmptyReq;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import okhttp3.*;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import javax.net.ssl.*;
import java.security.SecureRandom;
import java.security.cert.X509Certificate;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <p>HTTP 请求工具类<p>
 * @version 1.0
 * <AUTHOR>
 * @date 2018年11月7日
 */
@Slf4j
@Component
public class HttpSendUtil {

    @Autowired
    private RosBridgeService rosBridgeService;

    /**
     * 网络重启开始
     */
    public void handleRestartNetWork() {
        log.info("网络重启开始");
        try {
            _EmptyReq emptyReq = new _EmptyReq();
            rosBridgeService.publish(TopicConstants.WIRELESS_RESTART, Message.getMessageType(_EmptyReq.class),JSONObject.toJSONString(emptyReq));
            Thread.sleep(5000);
        } catch (Exception e) {
            e.printStackTrace();
        }
        log.info("网络重启完成");
    }

    /**
     * 获取这个SSLSocketFactory
     * @return SSLSocketFactory
     */
    public static SSLSocketFactory getSSLSocketFactory() {
        try {
            SSLContext sslContext = SSLContext.getInstance("SSL");
            sslContext.init(null, getTrustManager(), new SecureRandom());
            return sslContext.getSocketFactory();
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取TrustManager
     * @return TrustManager
     */
    private static TrustManager[] getTrustManager() {
        TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public X509Certificate[] getAcceptedIssuers() {
                        return new X509Certificate[]{};
                    }
                }
        };
        return trustAllCerts;
    }

    /**
     * 获取HostnameVerifier
     * @return HostnameVerifier
     */
    public static HostnameVerifier getHostnameVerifier() {
        HostnameVerifier hostnameVerifier = new HostnameVerifier() {
            @Override
            public boolean verify(String s, SSLSession sslSession) {
                return true;
            }
        };
        return hostnameVerifier;
    }

    public static Response okhttpPost(String url, Map<String,String> formParams){
        OkHttpClient client = new OkHttpClient()
                .newBuilder()
                //配置
                .sslSocketFactory(getSSLSocketFactory())
                //配置
                .hostnameVerifier(getHostnameVerifier())
                .connectTimeout(5, TimeUnit.SECONDS)
                .readTimeout(5, TimeUnit.SECONDS)
                .writeTimeout(10, TimeUnit.SECONDS)
                .build();
        FormBody.Builder builder = new FormBody.Builder();

        for (String ky : formParams.keySet()) {
            //追加表单信息
            builder.add(ky, formParams.get(ky));
        }
        RequestBody formBody=builder.build();
        Request request = new Request.Builder()
                .url(url)
                .post(formBody)
                .build();
        try {
            return client.newCall(request).execute();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }
}