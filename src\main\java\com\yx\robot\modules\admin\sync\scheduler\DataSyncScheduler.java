package com.yx.robot.modules.admin.sync.scheduler;

import com.yx.robot.modules.admin.sync.service.DataSyncService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@Order(2)
public class DataSyncScheduler implements CommandLineRunner {
    @Autowired
    private DataSyncService dataSyncService;

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1,new BasicThreadFactory.Builder().namingPattern("scheduled-pool-%d").daemon(true).build());

    @Override
    public void run(String... args) throws Exception {
        executorService.scheduleWithFixedDelay(()->{
            try {
                dataSyncService.syncDeviceInfoData();
                dataSyncService.syncTaskRecordData();
//              同步充电记录
                dataSyncService.syncChargingRecordData();
                dataSyncService.syncSwitchRecordData();
                dataSyncService.syncDisinfectTaskDetailData();
                dataSyncService.syncOrdersStateDataToYun();
            } catch (Exception e) {
                e.printStackTrace();
            }
        },0,3, TimeUnit.SECONDS);
        log.info("机器人设备信息推送中......");
    }
}
