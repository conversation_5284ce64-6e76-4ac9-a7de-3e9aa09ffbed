package com.yx.robot.common.constant;

/**
 * Redis相关的常量
 */
public interface RosWebConstants {
    /**
     * 成功(0)
     */
    String SUCCESS = "0";

    /**
     * 失败(1)
     */
    String FAIL = "1";

    /**
     * 进水
     */
    String INLET = "1";

    /**
     * 出水
     */
    String EFFLUENT = "-1";

    /**
     * 开始
     */
    String START = "1";

    /**
     * 停止
     */
    String END = "0";

    /**
     * 位置区域
     */
    String NEXT_LOCATION_INFO = "nextLocationInfo";

    /**
     * 位置编号
     */
    String NEXT_LOCATION_CODE = "nextLocationCode";

    /**
     * 下一个目标点
     */
    String NEXT_TARGET = "nextTarget";
    /**
     * 导航状态
     */
    String STATUS_CODE = "status_code";

    /**
     * 任务运行状态
     */
    String IS_RUNNING = "is_running";

    /**
     * 紫外线
     */
    String IS_ULRAY = "is_ulray";

    /**
     * 喷雾
     */
    String IS_SPRAY = "is_spray";

    /**
     * 消毒模块
     */
    String IS_XT = "is_xt";

    /**
     * 消毒时间
     */
    String DISINFECT_TIME = "disinfect_time";

    /**
     * 是否执行了暂停
     */
    String IS_STOPPING = "is_stopping";

    /**
     * ROS系统相关信息key
     */
    String ROBOT_SYS_INFO = "robot_sys_info";

    /**
     * 设置预充电点
     */
    String AUTO_DOCK_SETTING = "auto_dock_setting";

    /**
     * host
     */
    String HOST = "host";

    /**
     * ROS连接状态（0代表成功，1代表失败）
     */
    String ROS_STATUS = "ros_status";

    /**
     * 定位状态
     */
    String POSITION_STATUS = "position_status";

    /**
     * 定位控制状态
     */
    String POSITION_CTRL_STATUS = "position_ctrl_status";

    /**
     * 里程信息
     */
    String MILEAGE = "mileage";

    /**
     * 运行时间
     */
    String RUNTIME = "runtime";

    /**
     * 建图方式(gmapping)
     */
    String GMAPPING = "gmapping";

    /**
     * 建图方式(cartographer)
     */
    String CARTOGRAPHER = "cartographer";

    /**
     * 当前地图
     */
    String CURRENT_MAP = "current_map";

    /**
     * 目标地图
     */
    String NEXT_MAP = "next_map";

    /**
     * 临时地图id
     */
    String TEMP_MAP_ID = "00000000";

    /**
     * 当前服务地点
     */
    String CURRENT_SERVER_ADDR = "current_server_addr";

    /**
     * 当前楼层
     */
    String CURRENT_FLOOR = "current_floor";

    /**
     * 序列号
     */
    String SERIAL_NUMBER = "serial_number";

    /**
     * 监控信息
     */
    String MONITOR_INFO = "monitor_info";

    /**
     * 剩余电量
     */
    String REMAIN_BATTERY = "remain_battery";

    /**
     * 准备关机
     */
    String PRE_SHUTDOWN = "pre_shutdown";

    /**
     * 剩余充电时间
     */
    String REMAIN_CHARGING_TIME = "remainChargingTime";

    /**
     * 剩余消毒液的量
     */
    String REMAIN_DISINFECTANT = "remainDisinfectant";

    /**
     * 话题key
     */
    String TOPIC = "topic";

    /**
     * 任务类型
     */
    String TASK_TYPE = "taskType";

    /**
     * 导航点列表
     */
    String TO_DO_LIST = "to_do_list";

    /**
     * 剩余导航点列表
     */
    String HAS_TO_DO_LIST = "has_to_do_list";

    /**
     * 导航路线列表
     */
    String ROUTE_TO_DO_LIST = "route_to_do_list";

    /**
     * 门禁区域ID
     */
    String ENTRANCE_GUARD_ID = "entrance_guard_ID";

    /**
     * 门禁点位ID
     */
    String ENTRANCE_POSITION_ID = "entrance_position_id";

    /**
     * 电梯点位
     */
    String ELEVATOR_POSITION_ID = "elevator_position_id";

    /**
     * 电梯信息
     */
    String ELEVATOR_PROMISE = "elevator_promise";

    /**
     * 任务信息
     */
    String TASK_INFO = "task_info";

    /**
     * 循环次数
     */
    String LOOPS = "loops";

    /**
     * 是否在充电
     */
    String IS_CHARGING = "is_charging";

    /**
     * 提示信息
     */
    String ALERT_MSG = "alert_msg";

    /**
     * 任务数据
     */
    String TASK_DATA = "task_data";

    /**
     * 背景音乐
     */
    String BACKGROUND_MUSIC = "background_music";

    /**
     * 屏幕文字显示
     */
    String SCREEN_TEXT = "screen_text";

    /**
     * 语音列表
     */
    String SPEECH_LIST = "speech_list";

    /**
     * 节日
     */
    String FESTIVAL = "festival";

    /**
     * 导航栏
     */
    String NAV_BAR = "nav_bar";

    /**
     * 取物模式
     */
    String EXTRACT_MODE = "extract_mode";

    /**
     * 锁屏密码
     */
    String LOCK_SCREEN_PWD = "lock_screen_pwd";

    /**
     * 活物关闭紫外线
     */
    String LIVING_THINGS_ULRAY = "living_things_ulray";

    /**
     * 语音提醒
     */
    String VOICE_WARNING = "voice_warning";

    /**
     * 消毒次数
     */
    String DISINFECT_COUNTS = "disinfect_counts";

    /**
     * 沿途消毒速度
     */
    String DISINFECT_SPEED = "disinfect_speed";

    /**
     * 紫外线
     */
    String ULRAY = "ulray";

    /**
     * 喷雾
     */
    String SPRAY = "spray";

    /**
     * 消毒模块
     */
    String XT = "xt";

    /**
     * 最后一次进出水操作
     */
    String LAST_DISINFECTANT_RECORD_ID_1 = "last_disinfectant_record_id_1";

    String LAST_DISINFECTANT_RECORD_ID_2 = "last_disinfectant_record_id_2";

    /**
     * 任务记录uuid
     */
    String LAST_TASK_RECORD_ID = "last_record_id";

    /**
     * 充电任务记录id
     */
    String LAST_CHARGING_TASK_RECORD_ID = "last_charging_record_id";

    /**
     * 最后一次消毒详情
     */
    String LAST_DISINFECT_DETAIL_ID = "last_disinfect_detail_id";

    /**
     * 当前任务id
     */
    String CURRENT_TASK_ID = "current_task_id";

    /**
     * 充电触发条件
     */
    String CHARGING_CONDITION = "charging_condition";

    /**
     * 是否已经执行充电
     */
    String HAS_CHARGING = "has_charging";

    /**
     * 自动充电步骤（0：未开始 1：进行中 2：充电成功 3：充电失败）
     */
    String AUTO_CHARGING_STATUS = "auto_charging_status";

    /**
     * 时间
     */
    String AUTO_START_TIME = "auto_start_time";

    /**
     * 脱离充电桩
     */
    String END_CHARGING = "end_charging";

    /**
     * 脱离充电桩id
     */
    String END_CHARGING_MAP_ID = "end_charging_map_id";

    /**
     * 西铭风扇等级
     */
    String XM_FAN_LEVEL = "xm_fan_level";

    /**
     * 是否在定点消毒
     */
    String MANUAL_DISINFECT = "manual_disinfect";

    /**
     * 是否在巡线消毒
     */
    String LINE_DISINFECT = "line_disinfect";

    /**
     * 记录最后一次导航点记录
     */
    String LAST_TO_DO_LIST = "last_to_do_list";

    /**
     * 环形灯的状态
     */
    String RING_LIGHT_STATE = "ring_light_state";

    /**
     * 设备所属服务地点id
     */
    String SERVER_ID = "server_id";

    /**
     * 路径消毒开始时间
     */
    String ROUTE_DISINFECT_START_TIME = "route_disinfect_start_time";

    /**
     * 避障类型
     */
    String AVOID_TYPE = "avoid_type";

    /**
     * 休眠模式状态
     */
    String LOW_POWER_STATUS = "low_power_status";

    /**
     * 是否继续任务
     */
    String IS_CONTINUE_TASK = "is_continue_task";

    /**
     * 局部任务消毒点信息
     */
    String DISINFECT_POINT_LOCAL_TASK = "DISINFECT_POINT_LOCAL_TASK";

    /**
     * 当前任务是否包含喷雾消毒
     */
    String TASK_IS_CONTAINS_SPRAY = "isContainsSpray";

    /**
     * 功能
     */
    String ROBOT_FUNCTION = "ROBOT_FUNCTION";

    /**
     * 雷达功能休眠 true:开启，false:关闭
     */
    String FUNCTION_RADAR_SLEEP = "radar_sleep";

    /**
     * 防跌落功能开关状态
     */
    String FALL_DOWN_STATE = "fall_down_state";

    /**
     * 网络状态
     */
    String NETWORK_STATUS = "network_status";

    /**
     * 消息数据
     */
    String MESSAGE_INFO = "message_info";

    /**
     * 防撞条开启状态
     */
    String COLLISION_OPEN_STATE = "collision_open_state";

    /**
     * 液位传感器开启状态
     */
    String WATER_LEVEL_OPEN_STATE = "water_level_open_state";

    /**
     * 语音对话功能开启状态
     */
    String VOICE_MODEL_OPEN_STATE = "voice_model_open_atate";

    /**
     * 高速
     */
    String HIGH_SPEED = "3";

    /**
     * 中速
     */
    String SECONDARY_SPEED = "2";

    /**
     * 低速
     */
    String LOW_SPEED = "1";

    /**
     * 当前速度等级
     * 低速：1，中速：2，高速：3
     */
    String CURRENT_SPEED = "current_speed";

    /**
     * TRUE 切换地图中
     */
    String ROBOT_CHANGE_MAP = "robot:change_map";
}
