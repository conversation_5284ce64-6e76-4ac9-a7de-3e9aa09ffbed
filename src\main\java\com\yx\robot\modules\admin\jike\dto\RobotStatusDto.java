package com.yx.robot.modules.admin.jike.dto;

import cn.hutool.core.date.DateUtil;
import com.yx.robot.common.utils.RDes;
import com.yx.robot.modules.admin.jike.constant.JikeConstants;
import com.yx.robot.modules.admin.jike.enums.RobotStatusEnum;
import com.yx.robot.modules.admin.jike.enums.RobotTypeEnum;
import lombok.Data;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * 机器人状态
 * <AUTHOR>
 * @date 2021/12/13
 */
@Data
public class RobotStatusDto {
    private String robotBrandCode = JikeConstants.FAC_CODE;
    private String robotNo = JikeConstants.ROBOT_NO;
    private Integer robotType = RobotTypeEnum.X1.getType();
    private Integer robotStatus = RobotStatusEnum.WAIT.getValue();
    private String reportTime = DateUtil.format(new Date(), NORM_DATETIME_PATTERN);

    private String positionId = "";
    private String taskId = "";
    private String remark = "";
}
