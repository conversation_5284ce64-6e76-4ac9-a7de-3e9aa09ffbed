package com.yx.robot.modules.admin.electrl.util;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.util.CharsetUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.SmUtil;
import cn.hutool.crypto.symmetric.SM4;
import cn.hutool.crypto.symmetric.SymmetricCrypto;

import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

import java.nio.charset.StandardCharsets;
import java.util.*;

import static cn.hutool.crypto.SecureUtil.md5;

/**
 * 电梯工具类
 *
 * <AUTHOR>
 * @date 2021/12/13
 */
public class ElevatorUtil {

    /**
     * <desc>
     * 云端设备 AES 加密方案
     * </desc>
     *
     * @param sSrc
     * @param sKey
     * @return
     * @throws Exception
     */
    public static String aesEncryptSDK(String sSrc, String sKey) throws Exception {
        if (sKey == null) {
            System.out.print("Key 为空 null");
            return null;
        }

        // 判断 Key 是否为 16 位
        if (sKey.length() != 16) {
            System.out.print("Key 长度不是 16 位");
            return null;
        }

        byte[] raw = sKey.getBytes("utf-8");
        SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");//"算 法/模式/补码方式"
        cipher.init(Cipher.ENCRYPT_MODE, skeySpec);
        byte[] encrypted = cipher.doFinal(sSrc.getBytes("utf-8"));
        StringBuilder stringBuilder = new StringBuilder();
        if (encrypted == null || encrypted.length <= 0) {
            return null;
        }
        for (int i = 0; i < encrypted.length; i++) {
            int v = encrypted[i] & 0xFF;
            String hv = Integer.toHexString(v);
            if (hv.length() < 2) {
                stringBuilder.append(0);
            }
            stringBuilder.append(hv);
        }
        String base64Str = new Base64().encode(encrypted);//此处使用 BASE64做转码功能，同时能起到 2 次加密的作用。
        return base64Str;
    }

    /**
     * <desc>
     * 云端设备 AES 解密
     * </desc>
     *
     * @param sSrc
     * @param sKey
     * @return
     * @throws Exception
     */
    public static String aesDecrypt(String sSrc, String sKey) throws Exception {
        try {
            // 判断 Key 是否正确
            if (sKey == null) {
                System.out.print("Key 为空 null");
                return null;
            }
            // 判断 Key 是否为 16 位
            if (sKey.length() != 16) {
                System.out.print("Key 长度不是 16 位");
                return null;
            }
            if (sSrc.length() < 32) {
                System.out.print("密文串错误");
                return null;
            }
            byte[] raw = sKey.getBytes("utf-8");
            SecretKeySpec skeySpec = new SecretKeySpec(raw, "AES");
            Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
            cipher.init(Cipher.DECRYPT_MODE, skeySpec);
            byte[] encrypted1 = new Base64().decode(sSrc);//先用 base64 解密
            try {
                byte[] original = cipher.doFinal(encrypted1);
                String originalString = new String(original, "utf-8");
                return originalString;
            } catch (Exception e) {
                System.out.println(e.toString());
                return null;
            }
        } catch (Exception ex) {
            System.out.println(ex.toString());
            return null;
        }
    }

    /**
     * <desc>
     * sdkv5 计算 sign
     * </desc>
     *
     * @param params
     * @param appsecret
     * @return
     */
    public static String getSDKV5Sign(Map<String, Object> params, String appsecret, String... values) {
        if (params == null || StrUtil.isBlank(appsecret)) {
            return null;
        }
        StringBuffer stringA = new StringBuffer();
        List<Map.Entry<String, Object>> mappingList = new
                ArrayList<>(params.entrySet());
        Collections.sort(mappingList, new Comparator<Map.Entry<String,
                                Object>>() {
            @Override
            public int compare(Map.Entry<String, Object> mappingF,
                               Map.Entry<String, Object> mappingS) {
                return
                        mappingF.getKey().toLowerCase().compareTo(mappingS.getKey().toLowerCase
                                ());
            }
        });
        for (Map.Entry<String, Object> mapping : mappingList) {
            stringA.append(mapping.getValue() == null ? "" :
                    mapping.getValue());
        }
        for (String value : values) {
            stringA.append(value);
        }
        stringA.append(appsecret);
        return md5(stringA.toString());
    }

    /**
     * <desc>
     * SM4 加密
     * </desc>
     *
     * @param sSrc
     * @param sKey
     * @return
     * @throws Exception
     */
    public static String encryptHex(String sSrc, String sKey) {
        SymmetricCrypto sm4 = new SM4(Mode.ECB,
                Padding.ZeroPadding, sKey.getBytes(StandardCharsets.UTF_8));
        try {
            return sm4.encryptHex(sSrc, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return null;
        }
    }

    /**
     * <desc>
     * SM4 解密
     * </desc>
     *
     * @param sSrc
     * @param sKey
     * @return
     * @throws Exception
     */
    public static String decryptStr(String sSrc, String sKey) {
        SymmetricCrypto sm4 = new SM4(Mode.ECB,
                Padding.ZeroPadding, sKey.getBytes(StandardCharsets.UTF_8));
        try {
            return sm4.decryptStr(sSrc, CharsetUtil.CHARSET_UTF_8);
        } catch (Exception e) {
            System.out.println(e.getMessage());
            return null;
        }
    }

    /**
     * <desc>
     * sdkv5 计算 sign
     * </desc>
     *
     * @param params
     * @param appsecret
     * @return
     */
    public static String getSDKV3Sign(Map<String, Object> params, String appsecret, String... values) {
        if (params == null || StrUtil.isBlank(appsecret)) {
            return null;
        }
        StringBuffer stringA = new StringBuffer();
        List<Map.Entry<String, Object>> mappingList = new
                ArrayList<Map.Entry<String, Object>>(params.entrySet());
        Collections.sort(mappingList, new Comparator<Map.Entry<String,
                Object>>() {
            @Override
            public int compare(Map.Entry<String, Object> mappingF,
                               Map.Entry<String, Object> mappingS) {
                return
                        mappingF.getKey().toLowerCase().compareTo(mappingS.getKey().toLowerCase
                                ());
            }
        });
        for (Map.Entry<String, Object> mapping : mappingList) {
            stringA.append(mapping.getValue() == null ? "" :
                    mapping.getValue());
        }
        for (String value : values) {
            stringA.append(value);
        }
        stringA.append(appsecret);
        return SmUtil.sm3(stringA.toString());
    }
}
