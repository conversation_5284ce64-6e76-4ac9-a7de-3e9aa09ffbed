package com.yx.robot.modules.admin.controller;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotDisinfectantRecord;
import com.yx.robot.modules.admin.service.IRobotDisinfectantRecordService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人消毒液变化记录管理接口")
@RequestMapping("/yx/api-v1/robotDisinfectantRecord")
@Transactional
public class RobotDisinfectantRecordController {

    @Autowired
    private IRobotDisinfectantRecordService iRobotDisinfectantRecordService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotDisinfectantRecord> get(@PathVariable String id){

        RobotDisinfectantRecord robotDisinfectantRecord = iRobotDisinfectantRecordService.getById(id);
        return new ResultUtil<RobotDisinfectantRecord>().setData(robotDisinfectantRecord);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotDisinfectantRecord>> getAll(){
        List<RobotDisinfectantRecord> list = iRobotDisinfectantRecordService.list(
                new LambdaQueryWrapper<RobotDisinfectantRecord>().orderByDesc(RobotDisinfectantRecord::getBeginChangeTime).gt(RobotDisinfectantRecord::getConsumeTime,0));
        return new ResultUtil<List<RobotDisinfectantRecord>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotDisinfectantRecord>> getByPage(@ModelAttribute PageVo page){

        IPage<RobotDisinfectantRecord> data = iRobotDisinfectantRecordService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotDisinfectantRecord>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotDisinfectantRecord> saveOrUpdate(@ModelAttribute RobotDisinfectantRecord robotDisinfectantRecord){

        if(iRobotDisinfectantRecordService.saveOrUpdate(robotDisinfectantRecord)){
            return new ResultUtil<RobotDisinfectantRecord>().setData(robotDisinfectantRecord);
        }
        return new ResultUtil<RobotDisinfectantRecord>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotDisinfectantRecordService.removeById(id);
        }
        return new ResultUtil<>().setSuccessMsg("批量通过id删除数据成功");
    }
}
