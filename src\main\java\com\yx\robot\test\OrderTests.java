package com.yx.robot.test;

import com.alibaba.fastjson.JSON;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/9 16:35
 */
public class OrderTests {

    public static void main(String[] args) {
        Map<String, Integer> idToSumMap = new HashMap<>();
        idToSumMap.put("a", 432);
        idToSumMap.put("b", 12);
        idToSumMap.put("c", 9);
        idToSumMap.put("d", 92);
        List<String> ids = idToSumMap.entrySet().stream().sorted(Map.Entry.comparingByValue()).map(Map.Entry::getKey).collect(Collectors.toList());
        System.out.println(JSON.toJSONString(ids));
    }
}
