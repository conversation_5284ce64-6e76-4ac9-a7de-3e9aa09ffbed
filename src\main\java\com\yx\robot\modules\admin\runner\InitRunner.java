package com.yx.robot.modules.admin.runner;

import com.yx.robot.modules.admin.service.IRobotBaseInfoService;
import com.yx.robot.modules.admin.service.core.InitService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date
 */
@Component
@Slf4j
@Order(0)
@AllArgsConstructor
public class InitRunner implements CommandLineRunner {

    private InitService initService;

    private IRobotBaseInfoService iRobotBaseInfoService;

    @Override
    public void run(String... args) throws Exception {

        // 初始化机器人基础信息
        iRobotBaseInfoService.initRobotBaseInfo();
        //获取设备所属店铺id
        initService.initServerId();
        //初始化任务
        initService.initTask();
        //初始化ros服务
        initService.initRos();
        //初始化话题服务
        initService.initTopic();
        //初始化地图服务
        initService.initMap();
        // 根据标定点重定位
        initService.initPose(true, null);
        //运行时间
        initService.sumRunTime();
        //日志推送
//        initService.initLogPush();
        //初始化场景语音列表
        initService.initSceneSpeech();
        //初始化设备信息
        initService.initDeviceInfo();
    }
}
