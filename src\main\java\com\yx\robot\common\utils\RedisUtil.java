package com.yx.robot.common.utils;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.yx.robot.modules.base.utils.SendMessageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.JedisPoolConfig;

import javax.annotation.PostConstruct;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;
import java.util.Optional;

import static com.yx.robot.common.constant.RosWebConstants.TOPIC;

@Slf4j
@Component
public class RedisUtil {

    private static JedisPool jedisPool = null;
    private static String host;
    private static int port;
    private static String password;

    @Value("${spring.redis.host}")
    public void setHost(String host) {
        RedisUtil.host = host;
    }

    @Value("${spring.redis.port}")
    public void setPort(int port) {
        RedisUtil.port = port;
    }

    @Value("${spring.redis.password}")
    public void setPassword(String password) {
        RedisUtil.password = "".equals(password) ? null : password;
    }

    @Autowired
    private SendMessageUtil sendMessageUtil;

    /**
     * 初始化Redis连接池
     */
    @PostConstruct
    public void init() {
        // 创建jedis池配置实例
        JedisPoolConfig config = new JedisPoolConfig();
        //连接耗尽时是否阻塞, false报异常,ture阻塞直到超时, 默认true
        config.setBlockWhenExhausted(true);
        //设置的逐出策略类名, 默认DefaultEvictionPolicy(当连接超过最大空闲时间,或连接数超过最大空闲连接数)
        config.setEvictionPolicyClassName("org.apache.commons.pool2.impl.DefaultEvictionPolicy");
        //是否启用pool的jmx管理功能, 默认true
        config.setJmxEnabled(true);
        //MBean ObjectName = new ObjectName("org.apache.commons.pool2:type=GenericObjectPool,name=" + "pool" + i); 默 认为"pool", JMX不熟,具体不知道是干啥的...默认就好.
        config.setJmxNamePrefix("pool");
        //是否启用后进先出, 默认true
        config.setLifo(true);
        //最大空闲连接数, 默认8个
        config.setMaxIdle(8);
        //最大连接数, 默认8个
        config.setMaxTotal(8);
        //获取连接时的最大等待毫秒数(如果设置为阻塞时BlockWhenExhausted),如果超时就抛异常, 小于零:阻塞不确定的时间,  默认-1
        config.setMaxWaitMillis(-1);
        //逐出连接的最小空闲时间 默认1800000毫秒(30分钟)
        config.setMinEvictableIdleTimeMillis(1800000);
        //最小空闲连接数, 默认0
        config.setMinIdle(0);
        //每次逐出检查时 逐出的最大数目 如果为负数就是 : 1/abs(n), 默认3
        config.setNumTestsPerEvictionRun(3);
        //对象空闲多久后逐出, 当空闲时间>该值 且 空闲连接>最大空闲数 时直接逐出,不再根据MinEvictableIdleTimeMillis判断  (默认逐出策略)
        config.setSoftMinEvictableIdleTimeMillis(1800000);
        //在获取连接的时候检查有效性, 默认false
        config.setTestOnBorrow(false);
        //在空闲时检查有效性, 默认false
        config.setTestWhileIdle(false);
        //逐出扫描的时间间隔(毫秒) 如果为负数,则不运行逐出线程, 默认-1
        config.setTimeBetweenEvictionRunsMillis(-1);
        try {

            // 设置池配置项值
            jedisPool = new JedisPool(config, host, port, 30000, password);
            log.info("初始化Redis连接池success");
        } catch (Exception e) {
            log.error("初始化Redis连接池出错！", e);
            sendMessageUtil.sendShortMessage("初始化本地Redis连接池出错！");
        }
    }

    /**
     * 获取Jedis实例
     *
     * @return jedis
     */
    public synchronized static Jedis getJedis() {
        try {
            if (jedisPool != null) {
                return jedisPool.getResource();
            } else {
                return null;
            }
        } catch (Exception e) {
            log.info("redis 连接异常:{}", e.getMessage());
            return null;
        }
    }

    /**
     * 保存hash值
     *
     * @param key   key
     * @param field field
     * @param value value
     */
    public static void hset(String key, String field, String value) {
        Jedis jedis = getJedis();
        try {
            if (ObjectUtil.isNotNull(jedis)) {
                jedis.hset(key, field, value);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeJedis(jedis);
        }
    }

    /**
     * 保存hash值
     *
     * @param key   key
     * @param field field
     */
    public static void hdel(String key, String field) {
        Jedis jedis = getJedis();
        try {
            if (ObjectUtil.isNotNull(jedis)) {
                jedis.hdel(key, field);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeJedis(jedis);
        }
    }

    public static void lPush(String key, String val) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.lpush(key, val);
                int expire = 60 * 60 * 24 * 30;
                jedis.expire(key, expire);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    public static void rPush(String key, String val) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.rpush(key, val);
                int expire = 60 * 60 * 24 * 30;
                jedis.expire(key, expire);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 有序不重复集合
     *
     * @param key key
     * @param val val
     */
    public static void zadd(String key, String val) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.zadd(key, Integer.parseInt(DateUtil.format(new Date(), DatePattern.PURE_TIME_PATTERN)), val);
                int expire = 60 * 60 * 24 * 30;
                jedis.expire(key, expire);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 获取字符串
     *
     * @param key 键
     * @return value
     */
    public static String getValue(String key) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                return jedis.get(key);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 获取话题里面的值
     *
     * @param key key
     * @return value
     */
    public static String getTopicValue(String key) {
        return getValue(TOPIC + "::" + key);
    }

    /**
     * 删除话题产生的值
     *
     * @param key key
     * @return value
     */
    public static void delTopicValue(String key) {
        del(TOPIC + "::" + key);
    }

    /**
     * 设置键失效
     *
     * @param key       话题名称
     * @param expireSec 有效时间
     */
    public static void setKeyExpire(String key, int expireSec) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.expire(key, expireSec);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    public static Boolean judgeHasKey(String key) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                return jedis.exists(key);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        return false;
    }


    /**
     * 设置键失效
     *
     * @param key       话题名称
     * @param expireSec 有效时间
     */
    public static void setTopicExpire(String key, int expireSec) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.expire(TOPIC + "::" + key, 2);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    public static void hRemovePatterns(String key, String patterns) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                Map<String, String> map = jedis.hgetAll(key);
                if (ObjectUtil.isNotNull(map) && map.size() > 0) {
                    map.forEach((k, v) -> {
                        jedis.hdel(key, k);
                    });
                }
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    public static void hRemoveFiled(String key, String fileds) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.hdel(key, fileds);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 设置键失效
     *
     * @param key 话题名称
     * @param val 值
     */
    public static void setTopic(String key, String val) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.set(TOPIC + "::" + key, val);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 设置键失效
     *
     * @param key 话题名称
     * @param val 值
     * @param exp 过期时间
     */
    public static void setValue(String key, String val, Integer exp) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.setex(key, exp, val);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 保存字符串信息
     *
     * @param key key
     * @param val val
     */
    public static void setValue(String key, String val) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.set(key, val);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 删除字符串的值
     *
     * @param key
     */
    public static void del(String key) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.del(key);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 获取队列长度
     *
     * @param key 键
     * @return value
     */
    public static Long getSize(String key) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                Optional<Long> optionalLong = Optional.of(jedis.llen(key));
                return optionalLong.orElse(0L);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        return 0L;
    }

    /**
     * 获取hash 值
     *
     * @param key   键1
     * @param field 键2
     * @return value
     */
    public static String getHash(String key, String field) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                return jedis.hget(key, field);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 移除值
     *
     * @param key   键1
     * @param field 键2
     * @return value
     */
    public static void delHash(String key, String field) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                jedis.hdel(key.getBytes(StandardCharsets.UTF_8), field.getBytes(StandardCharsets.UTF_8));
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
    }

    /**
     * 关闭 jedis
     *
     * @param jedis jedis
     */
    public static void closeJedis(Jedis jedis) {
        try {
            if (jedis != null) {
                jedis.close();
            }
        } catch (Exception e) {
            closeBrokenResource(jedis);
        }
    }

    /**
     * Return jedis connection to the pool, call different return methods depends on whether the connection is broken
     */
    public static void closeBrokenResource(Jedis jedis) {
        try {
            jedisPool.returnBrokenResource(jedis);
        } catch (Exception e) {
            destroyJedis(jedis);
        }
    }

    /**
     * 在 Jedis Pool 以外强行销毁 Jedis
     */
    public static void destroyJedis(Jedis jedis) {
        if (jedis != null) {
            try {
                jedis.quit();
            } catch (Exception e) {
                log.error(">>> RedisUtil-jedis.quit() : " + e);
            }

            try {
                jedis.disconnect();
            } catch (Exception e) {
                log.error(">>> RedisUtil-jedis.disconnect() : " + e);
            }
        }
    }

    /**
     * 获取数组中指定下表的值
     *
     * @param key   key
     * @param index 小标
     * @return value
     */
    public static String getIndexList(String key, long index) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                return jedis.lindex(key, index);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        return null;
    }

    /**
     * 获取数组长度
     *
     * @param key key
     * @return value
     */
    public static long getListLength(String key) {
        Jedis jedis = getJedis();
        if (ObjectUtil.isNotNull(jedis)) {
            try {
                return jedis.llen(key);
            } catch (Exception e) {
                e.printStackTrace();
            } finally {
                jedis.close();
            }
        }
        return 0;
    }
}
