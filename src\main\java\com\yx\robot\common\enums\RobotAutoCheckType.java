package com.yx.robot.common.enums;

/**
 * 自检类型
 *
 * <AUTHOR>
 * @date 2020/09/18
 */
public enum RobotAutoCheckType {

    /**
     * 1,"通信状态"
     */
    COMMUNICATION_STATUS(1, "通信状态"),

    /**
     * 2,"定位状态"
     */
    POSITION_STATUS(2, "定位状态"),

    /**
     * 3,"设备自检"
     */
    DEVICE_STATUS(3, "设备自检"),

    /**
     * 4,"配置状态"
     */
    CONFIG_STATUS(4, "配置状态");

    private final Integer type;

    private final String value;

    RobotAutoCheckType(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return this.type;
    }

    public String getValue() {
        return this.value;
    }
}
