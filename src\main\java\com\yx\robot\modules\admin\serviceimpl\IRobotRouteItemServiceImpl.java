package com.yx.robot.modules.admin.serviceimpl;

import com.yx.robot.modules.admin.dao.mapper.RobotRouteItemMapper;
import com.yx.robot.modules.admin.entity.RobotRouteItem;
import com.yx.robot.modules.admin.service.IRobotRouteItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 机器人路线条目接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotRouteItemServiceImpl extends ServiceImpl<RobotRouteItemMapper, RobotRouteItem> implements IRobotRouteItemService {

    @Autowired
    private RobotRouteItemMapper robotRouteItemMapper;
}