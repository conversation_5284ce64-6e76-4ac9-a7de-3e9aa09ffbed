package com.yx.robot.common.hk;

import cn.hutool.core.io.FileUtil;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.ByteByReference;
import com.sun.jna.ptr.IntByReference;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/30 13:37
 */
@Slf4j
public class RealDataCallBackV30 implements HCNetSDK.FRealDataCallBack_V30 {

    private PlayCtrl playControl;

    private IntByReference mlPort;

    public RealDataCallBackV30(PlayCtrl playControl, IntByReference mlPort) {
        this.playControl = playControl;
        this.mlPort = mlPort;
    }

    /**
     * 预览回调
     *
     * @param lRealHandle 句柄
     * @param dwDataType  数据类型
     * @param pBuffer     buffer
     * @param dwBufSize   size
     * @param pUser       user
     */
    @Override
    public void invoke(int lRealHandle, int dwDataType, ByteByReference pBuffer, int dwBufSize, Pointer pUser) {
        // 播放库解码
//        log.info("码流数据回调, 数据类型: " + dwDataType + ", 数据长度:" + dwBufSize);
        switch (dwDataType) {
            //系统头
            case HCNetSDK.NET_DVR_SYSHEAD:
                //获取播放库未使用的通道号
                if (!playControl.PlayM4_GetPort(mlPort)) {
//                    System.out.println("获取播放库未使用的通道号");
                    break;
                }
                if (dwBufSize > 0) {
                    //设置实时流播放模式
                    if (!playControl.PlayM4_SetStreamOpenMode(mlPort.getValue(), PlayCtrl.STREAME_REALTIME)) {
//                        System.out.println("设置实时流播放模式");
                        break;
                    }
                    //打开流接口
                    if (!playControl.PlayM4_OpenStream(mlPort.getValue(), pBuffer, dwBufSize, 1024 * 1024)) {
//                        System.out.println("打开流接口");
                        break;
                    }
                    //播放开始
                    if (!playControl.PlayM4_Play(mlPort.getValue(), null)) {
//                        System.out.println("播放开始");
                        break;
                    }

                }
                //码流数据
            case HCNetSDK.NET_DVR_STREAMDATA:
                if ((dwBufSize > 0) && (mlPort.getValue() != -1)) {
                    //输入流数据
                    if (!playControl.PlayM4_InputData(mlPort.getValue(), pBuffer, dwBufSize)) {
//                        System.out.println(pBuffer.getPointer().getByteArray(0, dwBufSize));
//                        System.out.println("输入流数据");
                        break;
                    }
                    FileUtil.writeBytes(pBuffer.getPointer().getByteArray(0, dwBufSize), "E:/test1.mp4");
                }
            default:
//                log.info("未匹配到数据");
        }
    }
}
