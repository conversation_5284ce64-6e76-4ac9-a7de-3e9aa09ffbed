package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_disinfect_task_detail")
@ApiModel(value = "机器人消毒任务详情")
public class RobotDisinfectTaskDetail extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务记录id")
    private String robotTaskRecordId;

    @ApiModelProperty(value = "消毒类型")
    private Integer disinfectType;

    @ApiModelProperty(value = "消毒沿途速度")
    private Double wayDisinfectSpeed;

    @ApiModelProperty(value = "消毒沿途紫外")
    private String wayDisinfectUlray;

    @ApiModelProperty(value = "消毒沿途喷雾")
    private String wayDisinfectSpray;

    @ApiModelProperty(value = "紫外")
    private String ulray;

    @ApiModelProperty(value = "喷雾")
    private String spray;

    @ApiModelProperty(value = "消毒模块")
    private String xt;

    @ApiModelProperty(value = "开始时间")
    private Date startTime;

    @ApiModelProperty(value = "结束时间")
    private Date endTime;

    @ApiModelProperty(value = "消毒时间")
    private long disinfectTime;

    @ApiModelProperty(value = "同步状态 0:未同步 1:已同步")
    private String syncStatus;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}