package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.RobotMapDto;
import com.yx.robot.modules.admin.dto.YxRobotMapDto;
import com.yx.robot.modules.admin.dto.YxRobotMapInfoDto;
import com.yx.robot.modules.admin.entity.RobotMap;
import com.yx.robot.modules.admin.message._MapMetadata;
import com.yx.robot.modules.admin.vo.ScanSimple;
import com.yx.robot.modules.admin.message._LaserScan;
import com.yx.robot.modules.admin.vo.FloorMapVo;
import com.yx.robot.modules.admin.vo.RobotMapCorrectVo;
import com.yx.robot.modules.admin.vo.RobotMapInfoVo;
import com.yx.robot.modules.admin.vo.RobotMapVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Set;

/**
 * 机器人列表接口
 *
 * <AUTHOR>
 */
public interface IRobotMapService extends IService<RobotMap> {

    /**
     * 获取本地地图列表
     *
     * @return
     */
    List<RobotMapDto> getMapList();

    /**
     * 获取临时地图列表
     *
     * @return
     */
    Set<String> getTempMapList();

    /**
     * 获取远程地图列表
     *
     * @param deviceId
     * @return
     */
    List<YxRobotMapDto> getRemoteMapList(String deviceId);

    /**
     * 编辑地图
     *
     * @param robotMapInfoVo
     * @return
     */
    boolean editMap(RobotMapInfoVo robotMapInfoVo);

    /**
     * 复制地图
     *
     * @param robotMapInfoVo
     * @return
     */
    boolean copyMap(RobotMapInfoVo robotMapInfoVo);

    /**
     * 预览地图
     *
     * @param name
     * @return
     */
    void viewMap(String name, Integer type, HttpServletResponse httpServletResponse);

    /**
     * 删除地图
     *
     * @param id
     * @return
     */
    Result<Boolean> delMap(String id);

    /**
     * 上传地图到云端
     *
     * @param ids
     * @return
     */
    boolean uploadMapsToYun(String ids);

    /**
     * 从云端下载地图到本地
     *
     * @param mapAddrs
     * @return
     */
    boolean downloadMapsToLocal(String mapAddrs);

    /**
     * 更新地图
     *
     * @param mapName
     * @return
     */
    boolean updateMap(String mapName);

    /**
     * 楼层列表
     *
     * @return
     */
    List<FloorMapVo> floorList();

    /**
     * 获取选中的地图
     *
     * @return
     */
    YxRobotMapInfoDto getSelectMap();

    /**
     * 地图参数校准
     *
     * @return
     */
    void correctMapParam(RobotMapCorrectVo robotMapCorrectVo);

    /**
     * 根据地图名来查询地图,用于建图时判断地图重名
     *
     * @param robotMapVo
     * @return
     */
    RobotMap getRobotMap(RobotMapVo robotMapVo);

    /**
     * 获取地图雷达信息
     *
     * @return 雷达信息
     */
    _LaserScan getRobotScan();

    /**
     * 获取地图雷达信息字符串
     *
     * @return 雷达信息
     */
    String getRobotScanJson();

    /**
     * 获取雷达信息
     *
     * @return 雷达简洁信息
     */
    ScanSimple getRobotScanSimple();

    /**
     * 获取地图数据
     *
     * @return _MapMetadata
     */
    _MapMetadata getMapMetaData();

    /**
     * 自动重定位功能
     *
     * @return true:成功，false:失败
     */
    boolean autoResetPose();
}