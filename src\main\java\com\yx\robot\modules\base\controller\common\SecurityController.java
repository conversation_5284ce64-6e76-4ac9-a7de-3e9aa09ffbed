package com.yx.robot.modules.base.controller.common;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.SecurityConstant;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.utils.IpInfoUtil;
import com.yx.robot.common.utils.RDes;
import com.yx.robot.common.utils.RestUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.message._Bool;
import com.yx.robot.modules.admin.service.IRobotMotorService;
import com.yx.robot.modules.admin.service.core.InitService;
import com.yx.robot.modules.base.service.SystemHealthService;
import com.yx.robot.modules.base.vo.RobotCheckVo;
import com.yx.robot.modules.base.vo.StartCheckVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.SecurityConstant.SYS_IS_UP;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "Security相关接口")
@RequestMapping("/yx/common")
@Transactional
public class SecurityController {

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private InitService initService;

    @Autowired
    private IpInfoUtil ipInfoUtil;

    @Autowired
    private SystemHealthService systemHealthService;

    @Autowired
    private IRobotMotorService iRobotMotorService;

    @Value("${yx-yun.host}")
    private String yxYunHost;

    @Value("${yx-yun.port}")
    private Integer yxYunPort;

    @Value("${yx-yun.context-path}")
    private String yxYunContextPath;

    @Value("${yx-yun.url.sysActive}")
    private String sysActive;

    private static ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    @RequestMapping(value = "/needLogin", method = RequestMethod.GET)
    @ApiOperation(value = "没有登录")
    public Result<Object> needLogin() {
        return new ResultUtil<Object>().setErrorMsg(401, "您还未登录");
    }

    @RequestMapping(value = "/ipCheck", method = RequestMethod.GET)
    @ApiOperation(value = "ip地址验证")
    public Result<Boolean> ipCheck(@RequestParam String ip) {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, HOST);
        if (o != null && o.toString().equals(ip)) {
            return new ResultUtil<Boolean>().setData(true, "IP地址验证成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("IP地址设置异常");
        }
    }

    @RequestMapping(value = "/setPinCode/operation/{code}", method = RequestMethod.PUT)
    @ApiOperation(value = "设置操作密码")
    public Result<Boolean> setOperationCode(@PathVariable String code) {
        redisTemplate.opsForValue().set(SecurityConstant.OPERATION_CODE, code);
        return new ResultUtil<Boolean>().setData(true, "设置成功");
    }

    @RequestMapping(value = "/pinCode/operation/{code}", method = RequestMethod.GET)
    @ApiOperation(value = "操作密码验证")
    public Result<Boolean> operationCodeCheck(@PathVariable String code) {
        String storeCode = redisTemplate.opsForValue().get(SecurityConstant.OPERATION_CODE);
        if (code.equals(storeCode)) {
            //电机矢能
            iRobotMotorService.motorControl(false);
            return new ResultUtil<Boolean>().setData(true, "验证通过");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("验证失败,请重新输入");
        }
    }

    @RequestMapping(value = "/setPinCode/fetch/{code}", method = RequestMethod.PUT)
    @ApiOperation(value = "设置取物密码")
    public Result<Boolean> setFetchCode(@PathVariable String code) {
        redisTemplate.opsForValue().set(SecurityConstant.FETCH_CODE, code);
        return new ResultUtil<Boolean>().setData(true, "设置成功");
    }

    @RequestMapping(value = "/pinCode/fetch/{code}", method = RequestMethod.GET)
    @ApiOperation(value = "取物密码验证")
    public Result<Boolean> fetchCodeCheck(@PathVariable String code) {
        String storeCode = redisTemplate.opsForValue().get(SecurityConstant.FETCH_CODE);
        if (code.equals(storeCode)) {
            return new ResultUtil<Boolean>().setData(true, "验证通过");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("验证失败,请重新输入");
        }
    }

    @RequestMapping(value = "/isActivity", method = RequestMethod.GET)
    @ApiOperation(value = "系统是否激活")
    public Result<Boolean> sysIsActivity() {
        Object serialNumber = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SERIAL_NUMBER);
        if (serialNumber != null && StrUtil.isNotEmpty(serialNumber.toString())) {
            return new ResultUtil<Boolean>().setData(true, "系统已激活");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("系统尚未激活");
        }
    }

    @RequestMapping(value = "/getSerialNumber", method = RequestMethod.GET)
    @ApiOperation(value = "获取序列号")
    public Result<String> getSerialNumber() {
        Object serialNumber = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SERIAL_NUMBER);
        if (serialNumber == null || StrUtil.isEmpty(serialNumber.toString())) {
            return new ResultUtil<String>().setData("");
        }
        return new ResultUtil<String>().setData(serialNumber.toString());
    }

    @RequestMapping(value = "/sysActivity", method = RequestMethod.GET)
    @ApiOperation(value = "系统激活")
    public Result<Boolean> sysActivity(@RequestParam String code) {
        Future<JSONObject> future = null;
        try {
            String decrypt = RDes.decrypt(code);
            if (StrUtil.isNotEmpty(decrypt)) {
                String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + sysActive;
                JSONObject variables = new JSONObject();
                variables.put("activeInfo", decrypt);
                JSONObject result = null;
                future = executorService.submit(() -> {
                    try {
                        return RestUtil.get(url, variables);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                    return null;
                });
                result = future.get(10000, TimeUnit.MILLISECONDS);
                if (result != null && result.get("success").toString().equals(Boolean.TRUE.toString())) {
                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, SERIAL_NUMBER, code);
                    return new ResultUtil<Boolean>().setData(true, "激活成功");
                }
            }
        } catch (Exception e) {
            future.cancel(true);
            log.error("激活异常");
        }
        return new ResultUtil<Boolean>().setErrorMsg("激活失败");
    }

    @RequestMapping(value = "/sysRecover", method = RequestMethod.GET)
    @ApiOperation(value = "系统恢复")
    public Result<Boolean> restart() {
        boolean res;
        // 初始化话题数据
        initService.initTopic();
        // 初始化任务相关数据
        initService.initTask();
        // 检测定位，如果定位异常则进行则进行标签定位
        res = initService.initPose(false, null);
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败，请检查机器人位置是否在标签或者起始点位置附近");
        }

    }

    @RequestMapping(value = "/sysIsUp", method = RequestMethod.GET)
    @ApiOperation(value = "系统是否准备就绪")
    public Result<Boolean> sysIsUp() {
        Object sysIsUp = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SYS_IS_UP);
        if (null != sysIsUp && "true".equals(sysIsUp.toString())) {
            return new ResultUtil<Boolean>().setData(true, "系统准备就绪");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("系统尚未准备就绪");
        }
    }


    @RequestMapping(value = "/startCheck", method = RequestMethod.GET)
    @ApiOperation(value = "开机检测检测")
    public Result<StartCheckVo> batteryCheck() {
        StartCheckVo startCheckVo = new StartCheckVo();
        Object sysIsUp = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SYS_IS_UP);
        if (null != sysIsUp && "true".equals(sysIsUp.toString())) {
            // 网络监测(外网状态)
            boolean netWorkState = false;
            Future<Boolean> future = executorService.submit(new Callable<Boolean>() {
                @Override
                public Boolean call() {
//                    判断外网状态
                    return ipInfoUtil.extraNetState("www.baidu.com");
                }
            });
            try {
                netWorkState = future.get(5, TimeUnit.SECONDS);
            } catch (Exception e) {
            }
            startCheckVo.setNetworkState(netWorkState);

            // 定位检测
            Object positionStatusObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, POSITION_STATUS);
            if (positionStatusObj != null && StrUtil.isNotBlank(positionStatusObj.toString())) {
                boolean res = positionStatusObj.toString().equals(SUCCESS);
                startCheckVo.setPositionState(res);
            }
            // 业务待扩展
            String imuResult = "true";
            String odomResult = "true";
            String scanResult = "true";
            String depthResult = "true";
            if (StrUtil.isNotBlank(imuResult) && StrUtil.isNotBlank(odomResult)
                    && StrUtil.isNotBlank(scanResult) && StrUtil.isNotBlank(depthResult)) {
                startCheckVo.setSensorState(true);
            }
            // 急停开关检测(业务待扩展)
            startCheckVo.setEmergencyState(!iRobotMotorService.getEmergencyState());
            // 配置检测
            boolean configureState = true;
            startCheckVo.setConfigureState(configureState);
            return new ResultUtil<StartCheckVo>().setData(startCheckVo);
        }
        return new ResultUtil<StartCheckVo>().setErrorMsg("系统尚未就绪,无法获取自检信息");
    }


    @RequestMapping(value = "robotCheck", method = RequestMethod.GET)
    @ApiOperation(value = "第二版机器人自检")
    public Result<RobotCheckVo> robotCheck() {
        Object sysIsUp = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SYS_IS_UP);
        if (null != sysIsUp && "true".equals(sysIsUp.toString())) {
            RobotCheckVo robotCheckVo = systemHealthService.robotCheck();
            return new ResultUtil<RobotCheckVo>().setData(robotCheckVo);
        }
        return new ResultUtil<RobotCheckVo>().setErrorMsg("系统尚未就绪,无法获取自检信息");
    }

    @RequestMapping(value = "/swagger/login", method = RequestMethod.GET)
    @ApiOperation(value = "Swagger接口文档专用登录接口 方便测试")
    public Result<Object> swaggerLogin(@RequestParam String username, @RequestParam String password,
                                       @ApiParam("验证码") @RequestParam(required = false) String code,
                                       @ApiParam("图片验证码ID") @RequestParam(required = false) String captchaId,
                                       @ApiParam("可自定义登录接口地址")
                                       @RequestParam(required = false, defaultValue = "http://127.0.0.1:8888/yx/login")
                                               String loginUrl) {

        Map<String, Object> params = new HashMap<>(16);
        params.put("username", username);
        params.put("password", password);
        params.put("code", code);
        params.put("captchaId", captchaId);
        String result = HttpUtil.post(loginUrl, params);
        return new ResultUtil<Object>().setData(result);
    }
}
