package com.yx.robot.common.enums;

/**
 * 自动充电状态
 *
 * <AUTHOR>
 * @date 2021/01/15
 */
public enum AutoChargingStatus {

    /**
     * 0,"未开始"
     */
    UNDO(0, "未开始"),

    /**
     * 1, "进行中"
     */
    DOING(1, "进行中"),

    /**
     * 2, "成功"
     */
    SUCCESS(2, "成功"),

    /**
     * 3, "失败"
     */
    FAIL(3, "失败");

    public Integer value;

    private String label;

    AutoChargingStatus(Integer value, String label) {
        this.value = value;
        this.label = label;
    }

    public Integer getValue() {
        return value;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public void setLabel(String label) {
        this.label = label;
    }
}
