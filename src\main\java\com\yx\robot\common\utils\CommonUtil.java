package com.yx.robot.common.utils;

import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.StrUtil;
import org.python.util.PythonInterpreter;

import java.io.*;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.UUID;

/**
 * <AUTHOR>
 */
public class CommonUtil {

    /**
     * 以UUID重命名
     *
     * @param fileName
     * @return
     */
    public static String renamePic(String fileName) {
        String extName = fileName.substring(fileName.lastIndexOf("."));
        return UUID.randomUUID().toString().replace("-", "") + extName;
    }

    /**
     * 随机6位数生成
     */
    public static String getRandomNum() {

        Random random = new Random();
        int num = random.nextInt(999999);
        //不足六位前面补0
        String str = String.format("%06d", num);
        return str;
    }

    /**
     * 批量递归删除时 判断target是否在ids中 避免重复删除
     *
     * @param target
     * @param ids
     * @return
     */
    public static Boolean judgeIds(String target, String[] ids) {

        Boolean flag = false;
        for (String id : ids) {
            if (id.equals(target)) {
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * 获取某个范围内的随机整数
     *
     * @param min
     * @param max
     * @return
     */
    public static int getRandom(int min, int max) {
        Random random = new Random();
        int s = random.nextInt(max) % (max - min + 1) + min;
        return s;
    }

    /**
     * 将字符串转为指定编码的16进制
     *
     * @param str
     * @return
     */
    public static List<String> encode(String str)  {
        List<String> resultArr = new ArrayList<>();
        try {
            String hexString = "0123456789ABCDEF";
            //根据编码获取字节数组
            byte[] bytes = str.getBytes("GBK");
            StringBuilder sb = new StringBuilder(bytes.length * 2);
            //将字节数组中每个字节拆解成2位16进制整数
            for (int i = 0; i < bytes.length; i++) {
                resultArr.add((hexString.charAt((bytes[i] & 0xf0) >> 4) + "") + (hexString.charAt((bytes[i] & 0x0f) >> 0) + ""));
            }
            return resultArr;
        } catch (Exception e) {
            return null;
        }
    }

    public static short[] defineStr2ShortArr(String str) {
        if(StrUtil.isEmpty(str)) {
            return new short[0];
        }
        List<String> encode = encode(str);
        short[] result = new short[encode.size()];
        for(int i = 0; i < encode.size(); i++) {
            result[i] = Short.valueOf(encode.get(i),16);
        }
        return result;
    }
    /**
     * 将16进制字符串转换为byte[]
     *
     * @param str
     * @return
     */
    public static byte[] toBytes(String str) {
        if (str == null || "".equals(str.trim())) {
            return new byte[0];
        }

        byte[] bytes = new byte[str.length() / 2];

        for (int i = 0; i < str.length() / 2; i++) {
            String subStr = str.substring(i * 2, i * 2 + 2);
            bytes[i] = (byte) Integer.parseInt(subStr, 16);
        }

        return bytes;
    }


    /**
     * 替换文本文件中的 非法字符串
     * @param path
     * @param srcStr
     * @param replaceStr
     * @throws IOException
     */
    public static void replaceTextContent(String path, String srcStr, String replaceStr) throws IOException {
        // 读
        File file = new File(path);
        FileReader in = new FileReader(file);
        BufferedReader bufIn = new BufferedReader(in);
        // 内存流, 作为临时流
        CharArrayWriter tempStream = new CharArrayWriter();
        // 替换
        String line = null;
        while ( (line = bufIn.readLine()) != null) {
            // 替换每行中, 符合条件的字符串
            line = line.replaceAll(srcStr, replaceStr);
            // 将该行写入内存
            tempStream.write(line);
            // 添加换行符
            tempStream.append(System.getProperty("line.separator"));
        }
        // 关闭 输入流
        bufIn.close();
        // 将内存中的流 写入 文件
        FileWriter out = new FileWriter(file);
        tempStream.writeTo(out);
        out.close();
    }

    /**
     * 执行python脚本
     * @param path
     */
    public static void exePythonShell(String path) {
        PythonInterpreter interpreter = new PythonInterpreter();
        System.out.println("python脚本地址" + path);
        InputStream filepy = null;
        try {
            filepy = new FileInputStream(path);
            interpreter.execfile(filepy);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if(filepy != null) {
                try {
                    filepy.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void main(String[] args) {
        int random = CommonUtil.getRandom(1, 3);
        System.out.println(random);
    }
}
