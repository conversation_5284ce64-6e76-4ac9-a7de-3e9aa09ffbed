package com.yx.robot.modules.admin.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.yx.robot.base.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/8/15
 * description：用于存储发送短信的电话号码；
 */
@Data
@TableName("t_user")
@ApiModel(value = "机器人用户表")
public class RobotUser extends BaseEntity {

    private String address;

    private String avatar;

    private String description;

    private String email;

    /**
     * 电话号码
     */
    private String mobile;

    private String nickName;

    private String password;

    private String sex;

    /**
     * 用户名：把他当成负责人；
     */
    private String username;

    private String departmentId;

    private String passStrength;

    private String street;

    private Integer status;

    private Integer type;



}
