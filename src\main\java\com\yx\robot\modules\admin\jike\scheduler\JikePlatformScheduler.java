package com.yx.robot.modules.admin.jike.scheduler;

import com.yx.robot.modules.admin.jike.service.JikePlatformCore;
import com.yx.robot.modules.admin.jike.service.JikePlatformService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * 科技化平台调度
 * <AUTHOR>
 * @date 2021/04/29
 */
@Component
@Slf4j
@Order(value = 3)
public class JikePlatformScheduler implements CommandLineRunner {

    @Value("${platform.jike.enable}")
    private boolean jikeEnable;

    @Autowired
    private JikePlatformCore jikePlatformCore;

    @Autowired
    private JikePlatformService jikePlatformService;

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1,new BasicThreadFactory.Builder().namingPattern("scheduled-pool-%d").daemon(true).build());

    @Override
    public void run(String... args) throws Exception {
        if(jikeEnable) {
            executorService.scheduleWithFixedDelay(()->{
                jikePlatformCore.handleRefreshJikeToken();
                jikePlatformCore.handleRefreshUvrToken();
                jikePlatformService.handleRobotStatus();
            },0,30, TimeUnit.SECONDS);
        }
    }
}
