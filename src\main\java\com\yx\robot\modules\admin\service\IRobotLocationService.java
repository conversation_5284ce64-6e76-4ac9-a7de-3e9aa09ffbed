package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.RobotLocationInfoDto;
import com.yx.robot.modules.admin.entity.RobotLocation;
import com.yx.robot.modules.admin.vo.RobotLocationVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 机器人标注位置接口
 * <AUTHOR>
 */
public interface IRobotLocationService extends IService<RobotLocation> {

    /**
     * 获取所有机器人标注位置
     * @return
     */
    List<RobotLocationInfoDto> getAllRobotLocationByType(Integer type);

    /**
     * 获取机器人巡检标注位置
     */
    List<RobotLocationVo> getAllCruiseRobotLocation();

    /**
     * 判断同一个区域标注位置是否重复
     * @param locationInfo
     * @param locationCode
     * @return
     */
    boolean isRepeat(String locationInfo,String locationCode);

    /**
     * 获取所有声音播报文件
     * @return
     */
    List<String> getAllVoiceContent();
}