<?xml version="1.0" encoding="UTF-8"?>
<module org.jetbrains.idea.maven.project.MavenProjectsManager.isMavenModule="true" type="JAVA_MODULE" version="4">
  <component name="FacetManager">
    <facet type="Spring" name="Spring">
      <configuration />
    </facet>
    <facet type="web" name="Web">
      <configuration>
        <webroots />
      </configuration>
    </facet>
  </component>
  <component name="NewModuleRootManager" LANGUAGE_LEVEL="JDK_1_8">
    <output url="file://E:/jar/U3/classes" />
    <output-test url="file://E:/jar/U3/test-classes" />
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/src/main/java" isTestSource="false" />
      <sourceFolder url="file://$MODULE_DIR$/src/main/resources" type="java-resource" />
      <excludeFolder url="file://$MODULE_DIR$/target" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" name="Maven: net.java.dev.jna:jna:5.2.0" level="project" />
    <orderEntry type="library" name="Maven: net.java.dev.jna:jna:5.2.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-starter-test:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-logging:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-classic:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: ch.qos.logback:logback-core:1.2.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-to-slf4j:2.11.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.logging.log4j:log4j-api:2.11.2" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:jul-to-slf4j:1.7.26" level="project" />
    <orderEntry type="library" name="Maven: javax.annotation:javax.annotation-api:1.3.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.springframework.boot:spring-boot-test-autoconfigure:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.jayway.jsonpath:json-path:2.4.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:json-smart:2.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.minidev:accessors-smart:1.2" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.ow2.asm:asm:5.0.4" level="project" />
    <orderEntry type="library" name="Maven: junit:junit:4.12" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.assertj:assertj-core:3.11.1" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.mockito:mockito-core:2.23.4" level="project" />
    <orderEntry type="library" name="Maven: net.bytebuddy:byte-buddy:1.9.16" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: net.bytebuddy:byte-buddy-agent:1.9.16" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.objenesis:objenesis:2.6" level="project" />
    <orderEntry type="library" name="Maven: org.hamcrest:hamcrest-core:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.hamcrest:hamcrest-library:1.3" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.skyscreamer:jsonassert:1.5.0" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: com.vaadin.external.google:android-json:0.0.20131108.vaadin1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-core:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jcl:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" scope="TEST" name="Maven: org.xmlunit:xmlunit-core:2.6.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-web:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-json:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jdk8:2.9.9" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.datatype:jackson-datatype-jsr310:2.9.9" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.module:jackson-module-parameter-names:2.9.9" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-tomcat:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-core:9.0.22" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-el:9.0.22" level="project" />
    <orderEntry type="library" name="Maven: org.apache.tomcat.embed:tomcat-embed-websocket:9.0.22" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.validator:hibernate-validator:6.0.17.Final" level="project" />
    <orderEntry type="library" name="Maven: javax.validation:validation-api:2.0.1.Final" level="project" />
    <orderEntry type="library" name="Maven: org.jboss.logging:jboss-logging:3.3.2.Final" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-web:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-beans:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webmvc:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-expression:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-security:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aop:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-config:5.1.6.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-core:5.1.6.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.security:spring-security-web:5.1.6.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-aop:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.aspectj:aspectjweaver:1.9.4" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-actuator:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator-autoconfigure:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-actuator:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.micrometer:micrometer-core:1.1.5" level="project" />
    <orderEntry type="library" name="Maven: org.hdrhistogram:HdrHistogram:2.1.9" level="project" />
    <orderEntry type="library" name="Maven: org.latencyutils:LatencyUtils:2.0.3" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-starter-server:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-server:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-webflux:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-reactor-netty:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish:javax.el:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-webflux:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.synchronoss.cloud:nio-multipart-parser:1.1.0" level="project" />
    <orderEntry type="library" name="Maven: org.synchronoss.cloud:nio-stream-storage:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-thymeleaf:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf:thymeleaf-spring5:3.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf:thymeleaf:3.0.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.attoparser:attoparser:2.0.5.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.unbescape:unbescape:1.1.6.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.thymeleaf.extras:thymeleaf-extras-java8time:3.0.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor.addons:reactor-extra:3.2.3.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.projectreactor:reactor-core:3.2.11.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.reactivestreams:reactive-streams:1.0.2" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-server-ui:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-server-cloud:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-starter-client:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: de.codecentric:spring-boot-admin-client:2.1.6" level="project" />
    <orderEntry type="library" name="Maven: org.jolokia:jolokia-core:1.6.2" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid-spring-boot-starter:1.1.18" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:druid:1.1.18" level="project" />
    <orderEntry type="library" name="Maven: org.slf4j:slf4j-api:1.7.26" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-autoconfigure:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: io.jsonwebtoken:jjwt:0.9.1" level="project" />
    <orderEntry type="library" name="Maven: mysql:mysql-connector-java:5.1.47" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-jpa:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-jdbc:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.zaxxer:HikariCP:3.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-jdbc:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: javax.transaction:javax.transaction-api:1.3" level="project" />
    <orderEntry type="library" name="Maven: javax.xml.bind:jaxb-api:2.3.1" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:javax.activation-api:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate:hibernate-core:5.3.10.Final" level="project" />
    <orderEntry type="library" name="Maven: javax.persistence:javax.persistence-api:2.2" level="project" />
    <orderEntry type="library" name="Maven: org.javassist:javassist:3.23.2-GA" level="project" />
    <orderEntry type="library" name="Maven: antlr:antlr:2.7.7" level="project" />
    <orderEntry type="library" name="Maven: org.jboss:jandex:2.0.5.Final" level="project" />
    <orderEntry type="library" name="Maven: org.dom4j:dom4j:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.hibernate.common:hibernate-commons-annotations:5.0.4.Final" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-jpa:2.1.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-commons:2.1.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-orm:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-tx:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-aspects:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-boot-starter:3.1.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus:3.1.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-extension:3.1.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-core:3.1.2" level="project" />
    <orderEntry type="library" name="Maven: com.baomidou:mybatis-plus-annotation:3.1.2" level="project" />
    <orderEntry type="library" name="Maven: com.github.jsqlparser:jsqlparser:1.2" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis:3.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.mybatis:mybatis-spring:2.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-data-redis:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-redis:2.1.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.data:spring-data-keyvalue:2.1.10.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-oxm:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-context-support:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: redis.clients:jedis:2.9.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-pool2:2.6.2" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-ui:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spring-web:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: org.reflections:reflections:0.9.11" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger2:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-annotations:1.5.13" level="project" />
    <orderEntry type="library" name="Maven: io.swagger:swagger-models:1.5.13" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-spi:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-core:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-schema:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: io.springfox:springfox-swagger-common:2.7.0" level="project" />
    <orderEntry type="library" name="Maven: com.google.guava:guava:18.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml:classmate:1.4.0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-core:1.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.plugin:spring-plugin-metadata:1.2.0.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.mapstruct:mapstruct:1.1.0.Final" level="project" />
    <orderEntry type="library" name="Maven: com.google.code.gson:gson:2.8.5" level="project" />
    <orderEntry type="library" name="Maven: cn.hutool:hutool-all:5.6.3" level="project" />
    <orderEntry type="library" name="Maven: commons-io:commons-io:2.6" level="project" />
    <orderEntry type="library" name="Maven: org.projectlombok:lombok:1.18.8" level="project" />
    <orderEntry type="library" name="Maven: com.qiniu:qiniu-java-sdk:7.2.29" level="project" />
    <orderEntry type="library" name="Maven: com.github.ulisesbocchio:jasypt-spring-boot-starter:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.ulisesbocchio:jasypt-spring-boot:2.1.1" level="project" />
    <orderEntry type="library" name="Maven: com.melloware:jasypt:1.9.4" level="project" />
    <orderEntry type="library" name="Maven: com.ibeetl:beetl:2.9.10" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr4-runtime:4.2" level="project" />
    <orderEntry type="library" name="Maven: org.abego.treelayout:org.abego.treelayout.core:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.antlr:antlr4-annotations:4.2" level="project" />
    <orderEntry type="library" name="Maven: org.greenrobot:eventbus:3.0.0" level="project" />
    <orderEntry type="library" name="Maven: org.java-websocket:Java-WebSocket:1.4.0" level="project" />
    <orderEntry type="library" name="Maven: com.googlecode.json-simple:json-simple:1.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-websocket:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-messaging:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-websocket:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: commons-net:commons-net:3.6" level="project" />
    <orderEntry type="library" name="Maven: com.jcraft:jsch:0.1.54" level="project" />
    <orderEntry type="library" name="Maven: sshtools:j2ssh-core:0.2.2" level="project" />
    <orderEntry type="library" name="Maven: com.alibaba:fastjson:1.2.47" level="project" />
    <orderEntry type="library" name="Maven: com.aliyun:aliyun-java-sdk-core:4.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpclient:4.5.9" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpcore:4.4.11" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.bind:jaxb-core:2.3.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.sun.xml.bind:jaxb-impl:2.3.2" level="project" />
    <orderEntry type="library" name="Maven: javax.activation:activation:1.1.1" level="project" />
    <orderEntry type="library" name="Maven: org.jacoco:org.jacoco.agent:runtime:0.8.3" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-databind:2.9.8" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-annotations:2.9.0" level="project" />
    <orderEntry type="library" name="Maven: com.fasterxml.jackson.core:jackson-core:2.9.9" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml:3.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi:3.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-collections4:4.1" level="project" />
    <orderEntry type="library" name="Maven: org.apache.poi:poi-ooxml-schemas:3.17" level="project" />
    <orderEntry type="library" name="Maven: org.apache.xmlbeans:xmlbeans:2.6.0" level="project" />
    <orderEntry type="library" name="Maven: stax:stax-api:1.0.1" level="project" />
    <orderEntry type="library" name="Maven: com.github.virtuald:curvesapi:1.04" level="project" />
    <orderEntry type="library" name="Maven: net.sourceforge.javacsv:javacsv:2.0" level="project" />
    <orderEntry type="library" name="Maven: com.googlecode.soundlibs:mp3spi:1.9.5.4" level="project" />
    <orderEntry type="library" name="Maven: com.googlecode.soundlibs:jlayer:1.0.1.4" level="project" />
    <orderEntry type="library" name="Maven: com.googlecode.soundlibs:tritonus-share:0.3.7.4" level="project" />
    <orderEntry type="library" name="Maven: org.jflac:jflac-codec:1.5.2" level="project" />
    <orderEntry type="library" name="Maven: org.apache.rocketmq:rocketmq-client:4.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.rocketmq:rocketmq-common:4.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.rocketmq:rocketmq-remoting:4.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-lang3:3.8.1" level="project" />
    <orderEntry type="library" name="Maven: org.springframework:spring-test:5.1.9.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-test:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot:2.1.7.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.jvnet.hudson:ganymed-ssh2:build210-hudson-1" level="project" />
    <orderEntry type="library" name="Maven: com.belerweb:pinyin4j:2.5.0" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish:javax.json:1.0.4" level="project" />
    <orderEntry type="library" name="Maven: edu.wpi.rail:jrosbridge:0.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.tyrus:tyrus-client:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.tyrus:tyrus-core:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.tyrus:tyrus-spi:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: javax.websocket:javax.websocket-api:1.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.tyrus:tyrus-websocket-core:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.tyrus:tyrus-container-grizzly:1.2.1" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.grizzly:grizzly-framework:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.grizzly:grizzly-http-server:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.grizzly:grizzly-http:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: org.glassfish.grizzly:grizzly-rcm:2.3.3" level="project" />
    <orderEntry type="library" name="Maven: edu.brown.cs.burlap:java_rosbridge:2.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty.websocket:websocket-server:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty.websocket:websocket-common:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty.websocket:websocket-api:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-util:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-io:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty.websocket:websocket-client:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-client:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-xml:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty.websocket:websocket-servlet:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: javax.servlet:javax.servlet-api:4.0.1" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-servlet:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-security:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-server:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.jetty:jetty-http:9.4.19.v20190610" level="project" />
    <orderEntry type="library" name="Maven: com.google.protobuf:protobuf-java:3.5.1" level="project" />
    <orderEntry type="library" name="Maven: org.python:jython:2.5.3" level="project" />
    <orderEntry type="library" name="Maven: org.yaml:snakeyaml:1.23" level="project" />
    <orderEntry type="library" name="Maven: cn.jpush.api:jpush-client:3.3.10" level="project" />
    <orderEntry type="library" name="Maven: cn.jpush.api:jiguang-common:1.1.3" level="project" />
    <orderEntry type="library" name="Maven: io.netty:netty-all:4.1.38.Final" level="project" />
    <orderEntry type="library" name="Maven: com.github.oshi:oshi-core:3.12.2" level="project" />
    <orderEntry type="library" name="Maven: net.java.dev.jna:jna-platform:5.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.eclipse.paho:org.eclipse.paho.client.mqttv3:1.2.0" level="project" />
    <orderEntry type="library" name="Maven: org.fusesource.mqtt-client:mqtt-client:1.14" level="project" />
    <orderEntry type="library" name="Maven: org.fusesource.hawtdispatch:hawtdispatch-transport:1.22" level="project" />
    <orderEntry type="library" name="Maven: org.fusesource.hawtdispatch:hawtdispatch:1.22" level="project" />
    <orderEntry type="library" name="Maven: org.fusesource.hawtbuf:hawtbuf:1.11" level="project" />
    <orderEntry type="library" name="Maven: commons-codec:commons-codec:1.11" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15to18:1.68" level="project" />
    <orderEntry type="library" name="Maven: org.apache.httpcomponents:httpmime:4.5.5" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okhttp3:okhttp:4.9.0" level="project" />
    <orderEntry type="library" name="Maven: com.squareup.okio:okio:2.8.0" level="project" />
    <orderEntry type="library" name="Maven: io.minio:minio:8.4.3" level="project" />
    <orderEntry type="library" name="Maven: com.carrotsearch.thirdparty:simple-xml-safe:2.7.1" level="project" />
    <orderEntry type="library" name="Maven: org.bouncycastle:bcprov-jdk15on:1.69" level="project" />
    <orderEntry type="library" name="Maven: org.apache.commons:commons-compress:1.21" level="project" />
    <orderEntry type="library" name="Maven: org.xerial.snappy:snappy-java:1.1.8.4" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains.kotlin:kotlin-stdlib:1.3.50" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains.kotlin:kotlin-stdlib-common:1.3.50" level="project" />
    <orderEntry type="library" name="Maven: org.jetbrains:annotations:13.0" level="project" />
    <orderEntry type="library" name="Maven: yl_hk:jna:1_1_0" level="project" />
    <orderEntry type="library" name="Maven: yl_hk:examples:1_1_0" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.boot:spring-boot-starter-amqp:2.5.5" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.amqp:spring-rabbit:2.1.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.amqp:spring-amqp:2.1.8.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: org.springframework.retry:spring-retry:1.2.4.RELEASE" level="project" />
    <orderEntry type="library" name="Maven: com.rabbitmq:amqp-client:5.4.3" level="project" />
  </component>
</module>