package com.yx.robot.common.constant;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/6 15:40
 */
public interface RabbitMqConstants {

    String VERSION_UPDATE_QUEUE = "VERSION_UPDATE";

    /**
     * 交换器
     */
    String EXCHANGE_NAME = "exchange_robot";

    /**
     * 机器人状态信息消息队列路由键
     */
    String ROUTING_KEY_ROBOT_INFO = "routingkey_info";

    /**
     * 机器人消息通知消息队列路由键
     */
    String ROUTING_KEY_ROBOT_NOTIFY = "routingkey_notify";

    /**
     * 机器人状态信息消息队列
     */
    String QUEUE_ROBOT_INFO_VO = "queue_robot_info_vo";

    /**
     * 机器人消息通知消息队列
     */
    String QUEUE_ROBOT_MESSAGE_NOTIFY = "queue_robot_message_notify";

    /**
     * IP地址
     */
//    String IP_ADDRESS = "*************";

    /**
     * RabbitMQ 服务端默认端口号为 5672
     */
    Integer PORT = 5672;

    /**
     * 用户名
     */
    String USER_NAME = "admin";

    /**
     * 密码
     */
//    String PASSWORD = "123456";

}
