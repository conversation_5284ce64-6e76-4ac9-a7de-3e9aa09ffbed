package com.yx.robot.common.utils;

import cn.hutool.core.io.FileUtil;
import com.alibaba.fastjson.JSONArray;
import com.yx.robot.modules.admin.message._Image;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.PixelGrabber;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/16 16:03
 */
public class ImageUtils {

    public static void main(String[] args) throws Exception {
        _Image imageInfo = new _Image();
        imageInfo.height = 480;
        imageInfo.width = 640;
        String data = FileUtil.readString("E:/imagedata.txt", StandardCharsets.UTF_8);
        List<Integer> dataList = JSONArray.parseArray(data, Integer.class);
        byte[] dataBytes = new byte[dataList.size()];
        System.out.println(dataList.size());
        for (int i = 0; i < dataList.size(); i++) {
            dataBytes[i] = dataList.get(i).byteValue();
            if (i < 100) {
                System.out.println(dataBytes[i]);
            }
        }
        imageInfo.data = dataBytes;
        rgbBytesToJpg(imageInfo.data, imageInfo.width, imageInfo.height, "E:/3.jpg");
    }


    private static int[] rgb24ToPixel(byte[] rgb24, int width, int height) {
        int[] pix = new int[rgb24.length / 3];
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                int idx = width * i + j;
                int rgbIdx = idx * 3;
                int red = rgb24[rgbIdx];
                int green = rgb24[rgbIdx + 1];
                int blue = rgb24[rgbIdx + 2];
                int color = (blue & 0x000000FF) | (green << 8 & 0x0000FF00) | (red << 16 & 0x00FF0000);
                pix[idx] = color;
            }
        }
        return pix;
    }

    public static void rgbBytesToJpg(_Image imageInfo, String path) throws IOException {
        rgbBytesToJpg(imageInfo.data, imageInfo.width, imageInfo.height, path);
    }

    public static void rgbBytesToJpg(byte[] rgb, int width, int height, String path) throws IOException {
        BufferedImage bufferedImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        bufferedImage.setRGB(0, 0, width, height, rgb24ToPixel(rgb, width, height), 0, width);
        File file = new File(path);
        ImageIO.write(bufferedImage, "jpg", file);
    }


    private static _Image loadTestImage(String path) throws Exception {
        BufferedImage image = ImageIO.read(new File(path));
        if (image == null) {
            return null;
        }

        final int width = image.getWidth();
        final int height = image.getHeight();
        int[] pix = new int[width * height];
        PixelGrabber pg = new PixelGrabber(image, 0, 0, width, height, pix, 0, width);
        if (!pg.grabPixels()) {
            return null;
        }

        _Image imageInfo = new _Image();
        imageInfo.width = width;
        imageInfo.height = height;
        byte[] rgb24 = pixelToRgb24(pix, width, height);
        imageInfo.data = rgb24;
        return imageInfo;
    }

    private static byte[] pixelToRgb24(int[] pix, int width, int height) {
        byte[] rgb24 = new byte[width * height * 3];
        for (int i = 0; i < height; i++) {
            for (int j = 0; j < width; j++) {
                int idx = width * i + j;
                //获取像素
                int color = pix[idx];
                int red = ((color & 0x00FF0000) >> 16);
                int green = ((color & 0x0000FF00) >> 8);
                int blue = color & 0x000000FF;

                int rgbIdx = idx * 3;
                rgb24[rgbIdx] = (byte) red;
                rgb24[rgbIdx + 1] = (byte) green;
                rgb24[rgbIdx + 2] = (byte) blue;
            }
        }
        return rgb24;
    }
}
