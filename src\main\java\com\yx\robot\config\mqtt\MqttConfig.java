package com.yx.robot.config.mqtt;

import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.modules.admin.electrl.constant.ElevatorTopicConstants;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MqttConfig {

    @Value("${spring.mqtt.username}")
    private String username;

    @Value("${spring.mqtt.password}")
    private String password;

    @Value("${spring.mqtt.url}")
    private String hostUrl;

    @Value("${spring.mqtt.client.id}")
    private String clientId;

    @Value("${spring.mqtt.completionTimeout}")
    private int completionTimeout ;


    /**
     * 获取用户名
     * @return
     */
    public String getUsername() {
        return this.username;
    }
    /**
     * 获取密码
     * @return
     */
    public String getPassword() {
        return this.password;
    }

    /**
     * 获取服务器连接地址
     * @return
     */
    public String getHostUrl() {
        return this.hostUrl;
    }

    /**
     * 获取客户端ID
     * @return
     */
    public String getClientId() {
        return this.clientId;
    }

    /**
     * 获取主题
     * @return
     */
    public String[] getMsgTopic() {
        return new String[]{};
    }

    /***
     * 获取连接超时时间
     * @return
     */
    public int getCompletionTimeout() {
        return this.completionTimeout;
    }
}
