package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_map")
@ApiModel(value = "机器人地图")
public class RobotMap extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "地图名称")
    private String name;

    @ApiModelProperty(value = "店铺名称")
    private String storeName;

    @ApiModelProperty(value = "楼层")
    private Integer floor;

    @ApiModelProperty(value = "楼层高度")
    private double floorHeight;

    @ApiModelProperty(value = "地图文件名")
    private String fileName;

    @ApiModelProperty(value = "地图文件路径")
    private String filePath;

    @ApiModelProperty(value = "是否默认")
    private String isDefault;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}