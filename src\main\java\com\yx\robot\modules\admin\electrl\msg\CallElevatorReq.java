package com.yx.robot.modules.admin.electrl.msg;

import com.yx.robot.modules.admin.electrl.constant.ElevatorFactoryConfig;
import lombok.Data;
/**
 * 预约单云电梯请求
 * <AUTHOR>
 * @date 2021/12/11
 */
@Data
public class CallElevatorReq extends BaseMsgReq{

    private String robotId = ElevatorFactoryConfig.ROBOT_ID;

    private String deviceUnique = ElevatorFactoryConfig.DEVICE_UNIQUE;

    private String fromFloor;

    private String toFloor;

    private String openTime = ElevatorFactoryConfig.OPEN_TIME;

}
