package com.yx.robot.common.enums;

/**
 * <AUTHOR>
 * @date 2022/9/28
 * description：液位灯状态定义
 */
public enum LiquidLightDefine {

    /**
     * 0, "缺液", "红色灯", 1
     */
    LACK_LIQUID(0, "缺液", "红色灯", 1),

    /**
     * 1,"有液","蓝色灯", 1
     */
    HAVE_LIQUID(1, "有液", "蓝色灯", 1),

    /**
     * 2,"满液", "蓝色闪烁灯",1
     */
    FULL_LIQUID(2, "满液", "蓝色闪烁灯", 1);


    /**
     * 指令值
     */
    private final Integer value;

    /**
     * 状态场景
     */
    private final String state;

    /**
     * 灯光定义
     */
    private final String light;

    /**
     * 优先等级
     */
    private final Integer level;

    LiquidLightDefine(Integer value, String state, String light, Integer level) {
        this.value = value;
        this.state = state;
        this.light = light;
        this.level = level;
    }

    public Integer getValue() {
        return value;
    }

    public String getState() {
        return state;
    }

    public String getLight() {
        return light;
    }

    public Integer getLevel() {
        return level;
    }

}
