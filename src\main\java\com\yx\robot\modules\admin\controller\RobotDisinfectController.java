package com.yx.robot.modules.admin.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.enums.RobotType;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotDisinfect;
import com.yx.robot.modules.admin.service.IRobotDisinfectService;
import com.yx.robot.modules.admin.service.IRobotDisinfectantRecordService;
import com.yx.robot.modules.admin.vo.DisinfectPointVo;
import com.yx.robot.modules.admin.vo.PositionsVo;
import com.yx.robot.modules.admin.vo.RouteVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人消毒任务条目管理接口")
@RequestMapping("/yx/api-v1/robotDisinfect")
@Transactional
public class RobotDisinfectController {

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotDisinfectantRecordService iRobotDisinfectantRecordService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotDisinfect> get(@PathVariable String id){

        RobotDisinfect robotDisinfect = iRobotDisinfectService.getById(id);
        return new ResultUtil<RobotDisinfect>().setData(robotDisinfect);
    }

    @RequestMapping(value = "/getLocalDisinfectList", method = RequestMethod.GET)
    @ApiOperation(value = "获取局部消毒列表")
    public Result<List<DisinfectPointVo>> getLocalDisinfectTaskInfo() {
        List<DisinfectPointVo> localDisinfectTaskInfo = iRobotDisinfectService.getLocalDisinfectTaskInfo();
        return new ResultUtil<List<DisinfectPointVo>>().setData(localDisinfectTaskInfo);
    }

    @RequestMapping(value = "/updateLocalDisinfect", method = RequestMethod.PUT)
    @ApiOperation(value = "更新局部消毒")
    public Result<Boolean> updateLocalDisinfect(@RequestBody DisinfectPointVo disinfectPointVo) {
        boolean res = iRobotDisinfectService.updateLocalDisinfect(disinfectPointVo);
        if(res) {
            return new ResultUtil<Boolean>().setData(true);
        }else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotDisinfect>> getAll(){
        List<RobotDisinfect> list = iRobotDisinfectService.list();
        return new ResultUtil<List<RobotDisinfect>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotDisinfect>> getByPage(@ModelAttribute PageVo page){

        IPage<RobotDisinfect> data = iRobotDisinfectService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotDisinfect>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotDisinfect> saveOrUpdate(@ModelAttribute RobotDisinfect robotDisinfect){

        if(iRobotDisinfectService.saveOrUpdate(robotDisinfect)){
            return new ResultUtil<RobotDisinfect>().setData(robotDisinfect);
        }
        return new ResultUtil<RobotDisinfect>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotDisinfectService.removeById(id);
        }
        return new ResultUtil<>().setSuccessMsg("批量通过id删除数据成功");
    }

    @RequestMapping(value = "/waterCtrl",method = RequestMethod.GET)
    @ApiOperation(value = "进出水控制")
    public Result<Boolean> waterCtrl(@RequestParam String operation, @RequestParam String action) {
        if(RobotType.X1.getType().equals(RobotBaseInfoConstant.type)){
            iRobotDisinfectantRecordService.handleRobotDisinfectant(operation, action);
        }
        if(RobotType.U3.getType().equals(RobotBaseInfoConstant.type)){
            log.info("排水开始或关闭！");
            //1：开始排水；0：结束排水
            iRobotDisinfectantRecordService.outWaterControl(action);
        }
        return new ResultUtil<Boolean>().setData(true,"操作成功");
    }

    /**
     * 根据任务ID获取消毒点位ID信息
     */
    @RequestMapping(value = "/getPoseInfoBytaskId/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<List<PositionsVo>> getPoseInfoBytaskId(@PathVariable String id){
        List<PositionsVo> positionsVos = iRobotDisinfectService.getPoseInfoBytaskId(id);
        return new ResultUtil<List<PositionsVo>>().setData(positionsVos);
    }

    /**
     * 根据任务ID获取消毒点位ID信息
     */
    @RequestMapping(value = "/getRouteInfoByTaskId/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RouteVo> getRouteInfoByTaskId(@PathVariable String id){
        RouteVo routeVo = iRobotDisinfectService.getRouteInfoByTaskId(id);
        return new ResultUtil<RouteVo>().setData(routeVo);
    }

}
