package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.dto.RobotDisinfectTaskDetailDto;
import com.yx.robot.modules.admin.entity.RobotDisinfectTaskDetail;

import java.util.List;

/**
 * 机器人消毒任务详情接口
 * <AUTHOR>
 */
public interface IRobotDisinfectTaskDetailService extends IService<RobotDisinfectTaskDetail> {

    /**
     * 添加消毒任务详情
     * @param robotDisinfectTaskDetailDto
     */
    void saveRobotDisinfectTaskDetail(RobotDisinfectTaskDetailDto robotDisinfectTaskDetailDto);

    /**
     * 更新消毒任务详情
     */
    void updateRobotDisinfectTaskDetail();
}