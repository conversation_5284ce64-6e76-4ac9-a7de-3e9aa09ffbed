package com.yx.robot.modules.admin.service;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotCheckHistoryRecord;
import com.yx.robot.modules.admin.vo.CheckItemVo;
import com.yx.robot.modules.admin.vo.CheckReportVo;

import java.util.List;

/**
 * 机器人检测历史记录接口
 * <AUTHOR>
 */
public interface IRobotCheckHistoryRecordService extends IService<RobotCheckHistoryRecord> {

    /**
     * 机器人开机自检
     */
    void powerOnCheck();

    /**
     * 删除机器人检测历史记录
     * @param id
     */
    void delete(String id);

    /**
     * 提交检测报告
     * @param checkReportVo
     * @return
     */
    boolean submitCheckReport(CheckReportVo checkReportVo);

    /**
     * 检测报告详情
     * @return
     */
    List<CheckItemVo> checkReportDetail(String id);

    /**
     * 获取检测数据
     * @return
     */
    JSONObject getCheckData();
}