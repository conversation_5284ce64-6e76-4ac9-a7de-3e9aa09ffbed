package com.yx.robot.modules.admin.serviceimpl;

import com.yx.robot.modules.admin.dao.mapper.RobotCheckHistoryItemMapper;
import com.yx.robot.modules.admin.entity.RobotCheckHistoryItem;
import com.yx.robot.modules.admin.service.IRobotCheckHistoryItemService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;

/**
 * 机器人检测历史记录条目接口实现
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotCheckHistoryItemServiceImpl extends ServiceImpl<RobotCheckHistoryItemMapper, RobotCheckHistoryItem> implements IRobotCheckHistoryItemService {

    @Autowired
    private RobotCheckHistoryItemMapper robotCheckHistoryItemMapper;
}