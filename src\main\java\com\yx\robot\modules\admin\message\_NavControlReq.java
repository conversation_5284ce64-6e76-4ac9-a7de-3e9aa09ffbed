package com.yx.robot.modules.admin.message;

/**
 * 导航控制请求
 */
@MessageType(string = "frame_id cmd pose_name pose_type nav_mode pose_info")
public class _NavControlReq extends Message {
    public String frame_id;
    /**
     * 1:开始导航 4：取消导航 6：定位控制开启 7: 定位控制关闭
     */
    public int cmd;
    /**
     * 点位名称
     */
    public String pose_name;
    /**
     * 点位类型  参考：RobotPositionType
     */
    public int pose_type;
    /**
     * 导航模式  0 自主规划 1 固定路线  目前基本使用 0
     */
    public int nav_mode;
    /**
     * 点位信息
     */
    public _Pose pose_info;
}
