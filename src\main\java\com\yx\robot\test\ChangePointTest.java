package com.yx.robot.test;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.yx.robot.common.utils.RDes;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.sync.constant.DataSyncConstant;
import com.yx.robot.modules.admin.sync.util.SyncRedisUtil;
import com.yx.robot.modules.admin.vo.RobotPositionVo;
import com.yx.robot.modules.admin.vo.RobotTaskRecordVo;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 更新楼层脚本
 * 深圳
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/20 15:05
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ChangePointTest {

    @Autowired
    private IRobotLocationService iRobotLocationService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private IRobotWorldPositionService iRobotWorldPositionService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotRouteService iRobotRouteService;

    @Autowired
    private IRobotTaskItemService iRobotTaskItemService;

    private Set<String> mapIdSet = new HashSet<>();

    /**
     * 使用的时候，将事务删除
     */
    @Test
    @Transactional(rollbackFor = Exception.class)
    public void change() {
        System.out.println("开始更新");
        QueryWrapper<RobotLocation> queryWrapperList = new QueryWrapper<>();
        queryWrapperList.like("location_code", "B2-19F");
        List<RobotLocation> list = iRobotLocationService.list(queryWrapperList);
        updateLocation(list, "B2-19F", "B2-17F");
        if (!mapIdSet.isEmpty()) {
            mapIdSet.forEach(item -> {
                updateTask(item, "B2-19F", "B2-17F");
                updateRoute(item, "B2-19F", "B2-17F");
            });
        }
        System.out.println("完成更新");

    }

    public void updateLocation(List<RobotLocation> list, String old, String newName) {
        List<RobotLocation> addList = new ArrayList<>();
        list.forEach(item -> {
            iRobotLocationService.removeById(item.getId());
            updateRobotDisinfect(item.getId(), old, newName);
            updatePosition(old, newName, item.getLocationCode(), item.getPositionId());

            if (!item.getPositionId().contains(old)) {
                item.setPositionId(old + item.getPositionId());
                item.setId(old + item.getId());
                iRobotLocationService.save(item);
            }

            item.setId(item.getId().replace(old, newName));
            item.setLocationCode(item.getLocationCode().replace(old, newName));
            item.setPositionId(item.getPositionId().replace(old, newName));
            addList.add(item);
        });
        iRobotLocationService.saveBatch(addList);
    }

    public void updatePosition(String oldName, String newName, String localCode, String positionId) {

        RobotPosition robotPosition = iRobotPositionService.getById(positionId);
        if (ObjectUtil.isNull(robotPosition)) {
            return;
        }
        updateWorldPosition(oldName, newName, robotPosition.getWorldPoseId());
        String mapId = robotPosition.getMapId();
        mapIdSet.add(mapId);
        if (!robotPosition.getId().contains(oldName)) {
            robotPosition.setId(oldName + positionId);
            robotPosition.setName("自定义区域" + localCode);
            robotPosition.setMapId(oldName + mapId);
            robotPosition.setWorldPoseId(oldName + robotPosition.getWorldPoseId());

            iRobotPositionService.removeById(positionId);
            iRobotPositionService.save(robotPosition);
        }

        robotPosition.setId(robotPosition.getId().replace(oldName, newName));
        robotPosition.setName(robotPosition.getName().replace(oldName, newName));
//        robotPosition.setMapId(robotPosition.getMapId().replace(oldName, newName));
        robotPosition.setMapId("B2-17F528725056772116480");
        robotPosition.setWorldPoseId(robotPosition.getWorldPoseId().replace(oldName, newName));
        iRobotPositionService.save(robotPosition);
    }

    public void updateMap(String oldName, String newName) {

//        B2-17F528725056772116480

    }

    public void updateWorldPosition(String old, String newName, String id) {
        RobotWorldPosition robotWorldPosition = iRobotWorldPositionService.getById(id);

        if (!robotWorldPosition.getId().contains(old)) {
            robotWorldPosition.setId(old + robotWorldPosition.getId());
            iRobotWorldPositionService.removeById(id);
            iRobotWorldPositionService.save(robotWorldPosition);
        }

        robotWorldPosition.setId(robotWorldPosition.getId().replace(old, newName));
        iRobotWorldPositionService.save(robotWorldPosition);
    }

    public void updateRobotDisinfect(String locationId, String old, String newName) {
        QueryWrapper<RobotDisinfect> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("robot_location_id", locationId);
        List<RobotDisinfect> list = iRobotDisinfectService.list(queryWrapper);
        if (ArrayUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (!item.getRobotLocationId().contains(old)) {
                    iRobotDisinfectService.removeById(item.getId());
                    item.setRobotLocationId(old + item.getRobotLocationId());
                    item.setRobotTaskId(old + item.getRobotTaskId());
                    item.setId(old + item.getId());
                    iRobotDisinfectService.save(item);
                }
                item.setId(item.getId().replace(old, newName));
                item.setRobotTaskId(item.getRobotTaskId().replace(old, newName));
                item.setRobotLocationId(item.getRobotLocationId().replace(old, newName));
                iRobotDisinfectService.save(item);
            });
        }
    }

    public void updateTask(String mapId, String oldName, String newName) {

        QueryWrapper<RobotTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", mapId);
        List<RobotTask> list = iRobotTaskService.list(queryWrapper);
        if (ArrayUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                updateTaskItem(item.getId(), oldName, newName);
                if (!item.getId().contains(oldName)) {
                    iRobotTaskService.removeById(item.getId());
                    item.setMapId(oldName + item.getMapId());
                    item.setId(oldName + item.getId());
                    iRobotTaskService.save(item);
                }

                item.setMapId("B2-17F528725056772116480");
                item.setId(item.getId().replace(oldName, newName));
                iRobotTaskService.save(item);
            });
        }
    }

    public void updateRoute(String mapId, String old, String newName) {
        QueryWrapper<RobotRoute> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", mapId);
        System.out.println("============================" + mapId);
        List<RobotRoute> list = iRobotRouteService.list(queryWrapper);
        if (ArrayUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (!item.getMapId().contains(old)) {
                    iRobotRouteService.removeById(item.getId());
                    item.setMapId(old + item.getMapId());
                    item.setId(old + item.getId());
                    iRobotRouteService.save(item);
                }
                item.setId(item.getId().replace(old, newName));
                item.setMapId(item.getMapId().replace(old, newName));
                iRobotRouteService.save(item);
            });
        }
    }

    public void updateTaskItem(String taskId, String old, String newName) {

        QueryWrapper<RobotTaskItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        List<RobotTaskItem> list = iRobotTaskItemService.list(queryWrapper);
        if (ArrayUtil.isNotEmpty(list)) {
            list.forEach(item -> {
                if (!item.getTaskId().contains(old)) {
                    iRobotTaskItemService.removeById(item.getId());
                    item.setTaskId(old + item.getTaskId());
                    item.setPositionId(old + item.getPositionId());
                    item.setId(old + item.getId());
                    iRobotTaskItemService.save(item);
                }
                item.setId(item.getId().replace(old, newName));
                item.setPositionId(item.getPositionId().replace(old, newName));
                item.setTaskId(item.getTaskId().replace(old, newName));
                iRobotTaskItemService.save(item);
            });
        }

    }

    @Autowired
    private RedisTemplate redisTemplate;

    @Test
    public void pub() {

        while (true) {
            redisTemplate.convertAndSend("msg", System.currentTimeMillis());
            ThreadUtil.sleep(3000);
        }

    }

    /**
     * 测试获取全部数据根据地图ID
     * 将世界点位信息加入到了RobotPositionVo中
     */
    @Test
    public void test01(){
        QueryWrapper<RobotPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", "532322295167848448");
        List<RobotPosition> robotPositionList = iRobotPositionService.list(queryWrapper);
        List<RobotPositionVo> robotPositionVoList = new ArrayList<>();
        for (RobotPosition robotPosition : robotPositionList) {
            RobotPositionVo robotPositionVo = iRobotPositionService.robotPositionToRobotPositionVo(robotPosition);
            robotPositionVoList.add(robotPositionVo);
        }
        System.out.println("试获取全部数据根据地图ID======================="+new ResultUtil<List<RobotPositionVo>>().setData(robotPositionVoList));
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试根据地图艾迪获取充电桩点位
     */
    @Test
    public void test02(){
        QueryWrapper<RobotPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", "532322295167848448").eq("name","充电桩");
        RobotPosition one = iRobotPositionService.getOne(queryWrapper);
        RobotPositionVo robotPositionVo = iRobotPositionService.robotPositionToRobotPositionVo(one);
        System.out.println("测试根据地图艾迪获取充电桩点位==================="+new ResultUtil<RobotPositionVo>().setData(robotPositionVo));
        try {
            Thread.sleep(100000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    @Autowired
    private IRobotTaskRecordService robotTaskRecordService;

    /**
     * 测试同步任务记录数据
     */
    @Test
    public void testSyncTaskRecordData(){

    }
}
