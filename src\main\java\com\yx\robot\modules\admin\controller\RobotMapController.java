package com.yx.robot.modules.admin.controller;

import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.enums.RobotType;
import com.yx.robot.common.enums.SceneType;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.RobotMapDto;
import com.yx.robot.modules.admin.dto.ScanDto;
import com.yx.robot.modules.admin.dto.YxRobotMapDto;
import com.yx.robot.modules.admin.dto.YxRobotMapInfoDto;
import com.yx.robot.modules.admin.entity.RobotMap;
import com.yx.robot.modules.admin.message._Pose;
import com.yx.robot.modules.admin.service.IRobotMapService;
import com.yx.robot.modules.admin.service.core.InitService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.ROBOT_POSE;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api("机器人地图列表管理接口")
@RequestMapping("/yx/api-v1/robotMap")
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class RobotMapController {

    private IRobotMapService iRobotMapService;

    private RosWebService rosWebService;

    private InitService initService;

    private StringRedisTemplate redisTemplate;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotMap> get(@PathVariable String id) {

        RobotMap robotMap = iRobotMapService.getById(id);
        return new ResultUtil<RobotMap>().setData(robotMap);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotMap>> getAll() {
        List<RobotMap> list = iRobotMapService.list();
        return new ResultUtil<List<RobotMap>>().setData(list);
    }

    @RequestMapping(value = "/getSelectMap", method = RequestMethod.GET)
    @ApiOperation(value = "获取选中的地图")
    public Result<YxRobotMapInfoDto> getSelectMap() {
        YxRobotMapInfoDto selectMap = iRobotMapService.getSelectMap();
        if (selectMap != null) {
            return new ResultUtil<YxRobotMapInfoDto>().setData(selectMap);
        } else {
            return new ResultUtil<YxRobotMapInfoDto>().setErrorMsg("无法获取有效地图");
        }
    }

    @RequestMapping(value = "/initPose", method = RequestMethod.PUT)
    @ApiOperation(value = "初始化定位")
    public Result<Boolean> initPose(@RequestBody(required = false) _Pose pose) {
        log.info("手动重定位");
        boolean b = initService.initPose(false, pose);
        long startTime = System.currentTimeMillis();
        new Thread(() -> {
            while (true) {
                RedisUtil.delTopicValue(ROBOT_POSE);
                ThreadUtil.sleep(2000);
                if (StringUtils.isNotBlank(RedisUtil.getTopicValue(ROBOT_POSE))) {
                    RedisUtil.hset(ROBOT_SYS_INFO, POSITION_STATUS, SUCCESS);
                    break;
                }
                if (System.currentTimeMillis() - startTime > 60 * 2 * 1000) {
                    break;
                }
            }
        }).start();
        if (b) {
            return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/mapList", method = RequestMethod.GET)
    @ApiOperation(value = "地图列表")
    public Result<List<RobotMapDto>> mapList() {
        List<RobotMapDto> robotMapDtoList = iRobotMapService.getMapList();
        return new ResultUtil<List<RobotMapDto>>().setData(robotMapDtoList);
    }

    @RequestMapping(value = "/tempMapList", method = RequestMethod.GET)
    @ApiOperation(value = "临时地图列表")
    public Result<Set<String>> tempMapList() {
        Set<String> robotMapDtoList = iRobotMapService.getTempMapList();
        return new ResultUtil<Set<String>>().setData(robotMapDtoList);
    }

    @RequestMapping(value = "/remoteMapList", method = RequestMethod.GET)
    @ApiOperation(value = "云端地图列表")
    public Result<List<YxRobotMapDto>> remoteMapList(@RequestParam String deviceId) {
        List<YxRobotMapDto> robotMapDtoList = iRobotMapService.getRemoteMapList(deviceId);
        return new ResultUtil<List<YxRobotMapDto>>().setData(robotMapDtoList);
    }

    @RequestMapping(value = "/uploadMapsToYun", method = RequestMethod.GET)
    @ApiOperation(value = "上传地图到云端")
    public Result<Boolean> uploadMapsToYun(@RequestParam String ids) {
        boolean res = iRobotMapService.uploadMapsToYun(ids);
        return new ResultUtil<Boolean>().setData(res);
    }

    @RequestMapping(value = "/downloadMapsToLocal", method = RequestMethod.GET)
    @ApiOperation(value = "下载地图到本地")
    public Result<Boolean> downloadMapsToLocal(@RequestParam String mapAddrs) {
        boolean res = iRobotMapService.downloadMapsToLocal(mapAddrs);
        return new ResultUtil<Boolean>().setData(res);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotMap>> getByPage(@ModelAttribute PageVo page,
                                             @RequestParam(required = false) String name,
                                             @RequestParam(required = false) String storeName,
                                             @RequestParam(required = false) String isDefault) {
        LambdaQueryWrapper<RobotMap> robotMapLambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (StrUtil.isNotBlank(name)) {
            robotMapLambdaQueryWrapper.like(RobotMap::getName, name);
        }
        if (StrUtil.isNotBlank(storeName)) {
            robotMapLambdaQueryWrapper.like(RobotMap::getStoreName, storeName);
        }
        if (StrUtil.isNotEmpty(isDefault)) {
            robotMapLambdaQueryWrapper.eq(RobotMap::getIsDefault, isDefault);
        }
        IPage<RobotMap> data = iRobotMapService.page(PageUtil.initMpPage(page), robotMapLambdaQueryWrapper);
        return new ResultUtil<IPage<RobotMap>>().setData(data);
    }

    @RequestMapping(value = "/edit", method = RequestMethod.POST)
    @ApiOperation(value = "编辑地图")
    public Result<Boolean> update(@RequestBody RobotMapInfoVo robotMapInfoVo) {
        boolean res = iRobotMapService.editMap(robotMapInfoVo);
        if (res) {
            return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "copy", method = RequestMethod.POST)
    @ApiOperation(value = "复制地图")
    public Result<Boolean> copy(@RequestBody RobotMapInfoVo robotMapInfoVo) {
        boolean res = iRobotMapService.copyMap(robotMapInfoVo);
        if (res) {
            return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "view", method = RequestMethod.GET)
    @ApiOperation(value = "预览地图")
    public void getImage(@RequestParam String name, @RequestParam Integer type, HttpServletResponse response) {
        iRobotMapService.viewMap(name, type, response);
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {
        for (String id : ids) {
            iRobotMapService.delMap(id);
        }
        return new ResultUtil<>().setSuccessMsg("批量通过id删除数据成功");
    }

    @RequestMapping(value = "startCreate", method = RequestMethod.GET)
    @ApiOperation(value = "开始建图")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "mapType", value = "建图类型：cartographer", dataType = "String", paramType = "query", allowableValues = "cartographer"),
            @ApiImplicitParam(name = "mapSize", value = "地图大小", dataType = "int", paramType = "query")

    })
    public Result<Boolean> startCreate(@RequestParam String mapType, @RequestParam Integer mapSize) {
        return rosWebService.startCreateMap(mapType, mapSize);
    }

    @RequestMapping(value = "saveTempMap", method = RequestMethod.GET)
    @ApiOperation(value = "临时保存地图")
    public Result<Boolean> saveTempMap() {
        return rosWebService.saveTempMap();
    }

    @RequestMapping(value = "continueMap", method = RequestMethod.GET)
    @ApiOperation(value = "继续建图")
    public Result<Boolean> continueMap() {
        return rosWebService.continueMap();
    }

    @RequestMapping(value = "delMap/{id}", method = RequestMethod.DELETE)
    @ApiOperation(value = "删除地图")
    public Result<Boolean> delMap(@PathVariable String id) {
        return iRobotMapService.delMap(id);
    }

    @RequestMapping(value = "saveMapAlert", method = RequestMethod.GET)
    @ApiOperation(value = "保存地图提醒")
    public Result<Boolean> saveMapAlert() {
        rosWebService.sendVoicePrompt(SceneType.SAVE_MAP_ALERT, null);
        return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
    }

    @RequestMapping(value = "saveMap", method = RequestMethod.POST)
    @ApiOperation(value = "保存地图")
    public Result<Boolean> saveMap(@RequestBody RobotMapVo robotMapVo) {
        return rosWebService.saveMap(robotMapVo);
    }

    @GetMapping(value = "endCreateMap")
    @ApiOperation(value = "结束建图")
    public Result<Boolean> endCreateMap() {
        return rosWebService.endCreateMap();
    }

    @RequestMapping(value = "correctMap", method = RequestMethod.POST)
    public Result<Boolean> correctMap(@RequestBody RobotMapCorrectVo robotMapCorrectVo) {
        iRobotMapService.correctMapParam(robotMapCorrectVo);
        return new ResultUtil<Boolean>().setSuccessMsg("操作成功");
    }

    @RequestMapping(value = "uploadMap", method = RequestMethod.POST)
    @ApiOperation(value = "上传地图")
    public Result<Boolean> uploadMap(@RequestParam String mapData, @RequestParam(required = false) String name) {
        return rosWebService.uploadMap(mapData, name);
    }

    @RequestMapping(value = "selectMap", method = RequestMethod.GET)
    @ApiOperation(value = "选择地图")
    public Result<Boolean> selectMap(@RequestParam String id) {
        Result<Boolean> booleanResult = rosWebService.selectMap(id);
        if (booleanResult.isSuccess()) {
            if (RobotType.X4.getType().equals(RobotBaseInfoConstant.type) || RobotType.X1.getType().equals(RobotBaseInfoConstant.type)) {
                log.info("X4,X1专用切换地图语音");
                rosWebService.sendVoicePrompt(SceneType.SELECT_MAP_X4, null);
            } else {
                rosWebService.sendVoicePrompt(SceneType.SELECT_MAP, null);
            }
        }
        return booleanResult;
    }

    @RequestMapping(value = "serverAddrList", method = RequestMethod.GET)
    @ApiOperation(value = "服务地点列表")
    public Result<List<ServerAddrVo>> serverAddrList() {
        List<ServerAddrVo> serverAddrVoList = new ArrayList<>();
        List<RobotMap> list = iRobotMapService.list();
        for (RobotMap item : list) {
            String id = item.getId();
            String storeName = item.getStoreName();
            List<String> serverAddrList = serverAddrVoList.stream().map(ServerAddrVo::getServerAddr).collect(Collectors.toList());
            if (StrUtil.isNotBlank(storeName) && !serverAddrList.contains(storeName)) {
                ServerAddrVo serverAddrVo = new ServerAddrVo();
                serverAddrVo.setId(id);
                serverAddrVo.setServerAddr(storeName);
                Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_SERVER_ADDR);
                serverAddrVo.setDefault(o != null && StrUtil.isNotEmpty(o.toString()) && o.toString().equals(id));
                serverAddrVoList.add(serverAddrVo);
            }
        }
        return new ResultUtil<List<ServerAddrVo>>().setData(serverAddrVoList);
    }

    @RequestMapping(value = "selectAddr", method = RequestMethod.GET)
    @ApiOperation(value = "选择服务地点")
    public Result<Boolean> selectAddr(@RequestParam String id) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_SERVER_ADDR, id);
        return new ResultUtil<Boolean>().setData(true, "选择服务地点成功");
    }

    @RequestMapping(value = "currentFloor", method = RequestMethod.GET)
    @ApiOperation(value = "当前楼层")
    public Result<Integer> currentFloor() {
        Object currentFloorObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_FLOOR);
        if (currentFloorObj != null && StrUtil.isNotEmpty(currentFloorObj.toString())) {
            return new ResultUtil<Integer>().setData(Integer.valueOf(currentFloorObj.toString()));
        } else {
            return new ResultUtil<Integer>().setErrorMsg("无法获取当前楼层信息");
        }
    }

    @RequestMapping(value = "floorList", method = RequestMethod.GET)
    @ApiOperation(value = "楼层列表")
    public Result<List<FloorMapVo>> floorList() {
        List<FloorMapVo> floorMapVoList = iRobotMapService.floorList();
        return new ResultUtil<List<FloorMapVo>>().setData(floorMapVoList);
    }

    @RequestMapping(value = "updateMap", method = RequestMethod.GET)
    @ApiOperation(value = "更新地图")
    public Result<Boolean> updateMap(@RequestParam String mapAddr) {
        boolean res = iRobotMapService.updateMap(mapAddr);
        return new ResultUtil<Boolean>().setData(res);
    }

    @GetMapping(value = "/autoResetPose")
    @ApiOperation(value = "自动重定位功能")
    public Result<Boolean> autoResetPose() {
        log.info("api 调用自动重定位功能");
        return new ResultUtil<Boolean>().setData(iRobotMapService.autoResetPose());
    }

    @GetMapping("/getScanInfo")
    @ApiOperation("获取雷达信息")
    public Result<ScanDto> getScanInfo() {
        Result<ScanDto> result = new Result<>();
        ScanDto scanDto = null;
        if (ObjectUtil.isNotNull(iRobotMapService.getRobotScan())) {
            scanDto = new ScanDto(iRobotMapService.getRobotScan());
            result.setSuccess(true);
        }else {
            result.setSuccess(false);
        }
        result.setResult(scanDto);
        return result;
    }
}


