package com.yx.robot.modules.admin.controller;

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotCheckHistoryItem;
import com.yx.robot.modules.admin.service.IRobotCheckHistoryItemService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人检测历史记录条目管理接口")
@RequestMapping("/yx/api-v1/robotCheckHistoryItem")
@Transactional
public class RobotCheckHistoryItemController {

    @Autowired
    private IRobotCheckHistoryItemService iRobotCheckHistoryItemService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotCheckHistoryItem> get(@PathVariable String id){

        RobotCheckHistoryItem robotCheckHistoryItem = iRobotCheckHistoryItemService.getById(id);
        return new ResultUtil<RobotCheckHistoryItem>().setData(robotCheckHistoryItem);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotCheckHistoryItem>> getAll(){

        List<RobotCheckHistoryItem> list = iRobotCheckHistoryItemService.list();
        return new ResultUtil<List<RobotCheckHistoryItem>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotCheckHistoryItem>> getByPage(@ModelAttribute PageVo page){
        if (StringUtils.isBlank(page.getSortOrigin())){
            page.setSort("id");
        }
        IPage<RobotCheckHistoryItem> data = iRobotCheckHistoryItemService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotCheckHistoryItem>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotCheckHistoryItem> saveOrUpdate(@ModelAttribute RobotCheckHistoryItem robotCheckHistoryItem){

        if(iRobotCheckHistoryItemService.saveOrUpdate(robotCheckHistoryItem)){
            return new ResultUtil<RobotCheckHistoryItem>().setData(robotCheckHistoryItem);
        }
        return new ResultUtil<RobotCheckHistoryItem>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotCheckHistoryItemService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
