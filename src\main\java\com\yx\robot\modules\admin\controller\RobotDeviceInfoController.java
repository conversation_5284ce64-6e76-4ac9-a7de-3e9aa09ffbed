package com.yx.robot.modules.admin.controller;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotDeviceInfo;
import com.yx.robot.modules.admin.entity.RobotPositionDevRelation;
import com.yx.robot.modules.admin.service.IRobotDeviceInfoService;
import com.yx.robot.modules.admin.service.IRobotDeviceOperateService;
import com.yx.robot.modules.admin.service.IRobotPositionDevRelationService;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022-04-28
 */
@Slf4j
@RestController
@Api("设备信息管理接口")
@RequestMapping("/yx/api-v1/robotDeviceInfo")
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class RobotDeviceInfoController {

    private IRobotDeviceInfoService iRobotDeviceInfoService;

    private IRobotDeviceOperateService iRobotDeviceOperateService;

    @Autowired
    private IRobotPositionDevRelationService iRobotPositionDevRelationService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotDeviceInfo> get(@PathVariable String id) {

        RobotDeviceInfo robotDeviceInfo = iRobotDeviceInfoService.getById(id);
        return new ResultUtil<RobotDeviceInfo>().setData(robotDeviceInfo);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotDeviceInfo>> getAll() {

        List<RobotDeviceInfo> list = iRobotDeviceInfoService.list();
        return new ResultUtil<List<RobotDeviceInfo>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotDeviceInfo>> getByPage(@ModelAttribute PageVo page) {

        IPage<RobotDeviceInfo> data = iRobotDeviceInfoService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotDeviceInfo>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotDeviceInfo> saveOrUpdate(@ModelAttribute RobotDeviceInfo robotDeviceInfo) {
        boolean isSuccess;
        System.out.println(JSON.toJSONString(robotDeviceInfo));
        RobotDeviceInfo robotDeviceInfoOld = iRobotDeviceInfoService.getById(robotDeviceInfo.getId());
        System.out.println(JSON.toJSONString(robotDeviceInfoOld));
        if (ObjectUtils.isNull(robotDeviceInfoOld)) {
            //如果为空则保存
            isSuccess = iRobotDeviceInfoService.saveDeviceInfo(robotDeviceInfo);
        } else {
            //如果不为空则根据id修改
            isSuccess = iRobotDeviceInfoService.updateDeviceInfo(robotDeviceInfo);
        }
        if (isSuccess) {
            return new ResultUtil<RobotDeviceInfo>().setData(robotDeviceInfo);
        }
        return new ResultUtil<RobotDeviceInfo>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids) {
        for (String id : ids) {
            //先删除设备表信息
            iRobotDeviceInfoService.removeById(id);
            QueryWrapper<RobotPositionDevRelation> wrapper = new QueryWrapper<>();
            wrapper.eq("dev_id", id);
            //再删除设备信息级联删除点位表信息
            List<RobotPositionDevRelation> list = iRobotPositionDevRelationService.list(wrapper);
            for (RobotPositionDevRelation robotPositionDevRelation : list) {
                iRobotPositionService.removeById(robotPositionDevRelation.getPositionId());
            }
            //然后删除设备信息级联删除点位设备中间表信息
            iRobotPositionDevRelationService.remove(wrapper);
        }
        return new ResultUtil<>().setSuccessMsg("批量通过id删除数据成功");
    }

    @PostMapping("/operateDevice")
    @ApiOperation("操作设备")
    public boolean operateDevice(@RequestBody RobotDeviceInfo robotDeviceInfo, @RequestParam String cmdType) {
        return iRobotDeviceOperateService.operateEntranceGuard(robotDeviceInfo, new Short(cmdType));
    }

    @PostMapping("/operateDeviceById")
    @ApiOperation("操作设备")
    public boolean operateDevice(@RequestParam String id, @RequestParam String cmdType) {
        RobotDeviceInfo robotDeviceInfo = iRobotDeviceInfoService.getById(id);
        return iRobotDeviceOperateService.operateEntranceGuard(robotDeviceInfo, new Short(cmdType));
    }

    @GetMapping("/getRobotDeviceInfoByTypeAndSubType/{type}/{subType}")
    @ApiOperation("根据父类型和子类型获取设备信息")
    public Result<List<RobotDeviceInfo>> getRobotDeviceInfoByTypeAndSubType(@PathVariable Integer type,
                                                                            @PathVariable Integer subType) {
        return iRobotDeviceInfoService.getRobotDeviceInfoByTypeAndSubType(type, subType);
    }

    @GetMapping("/getRobotDeviceInfoByPositionType/{type}")
    @ApiOperation("根据父类型和子类型获取设备信息")
    public Result<List<RobotDeviceInfo>> getRobotDeviceInfoByPositionType(@PathVariable Integer type) {
        return iRobotDeviceInfoService.getRobotDeviceInfoByPositionType(type);
    }

}
