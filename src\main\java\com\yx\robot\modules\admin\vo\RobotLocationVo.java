package com.yx.robot.modules.admin.vo;

import com.yx.robot.modules.admin.entity.RobotLocation;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class RobotLocationVo {

    private String id;
    private String locationInfo;
    private String locationCode;

    public RobotLocationVo(RobotLocation robotLocation) {
        this.id = robotLocation.getId();
        this.locationInfo = robotLocation.getLocationInfo();
        this.locationCode = robotLocation.getLocationCode();
    }
}
