package com.yx.robot.common.utils;


import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpUtil;
import com.google.gson.Gson;
import com.yx.robot.common.vo.IpInfo;
import com.yx.robot.common.vo.IpLocate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.net.InetAddress;
import java.net.UnknownHostException;


/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class IpInfoUtil {

    @Value("${yx.mob.appKey}")
    private String appKey;

    @Value("${robot.hostname}")
    private String hostname;

    @Value("${robot.host}")
    private String host;

    /**
     * 获取客户端IP地址
     * @param request 请求
     * @return
     */
    public String getIpAddr(HttpServletRequest request) {

        String ip = request.getHeader("x-forwarded-for");
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
            if ("127.0.0.1".equals(ip)) {
                //根据网卡取本机配置的IP
                InetAddress inet = null;
                try {
                    inet = InetAddress.getLocalHost();
                } catch (UnknownHostException e) {
                    e.printStackTrace();
                }
                ip = inet.getHostAddress();
            }
        }
        // 对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
        if (ip != null && ip.length() > 15) {
            if (ip.indexOf(",") > 0) {
                ip = ip.substring(0, ip.indexOf(","));
            }
        }
        if("0:0:0:0:0:0:0:1".equals(ip)){
            ip = "127.0.0.1";
        }
        return ip;
    }

    /**
     * 获取IP返回地理天气信息
     * @param ip ip地址
     * @return
     */
    public String getIpWeatherInfo(String ip){

        String GET_IP_WEATHER = "http://apicloud.mob.com/v1/weather/ip?key="+ appKey +"&ip=";
        if(StrUtil.isNotBlank(ip)){
            String url = GET_IP_WEATHER + ip;
            try {
                String result = HttpUtil.get(url);
                return result;
            }catch (Exception e) {
                System.out.println("根据ip获取天气信息异常");
            }

        }
        return null;
    }

    /**
     * 获取IP返回地理信息
     * @param ip ip地址
     * @return
     */
    public String getIpCity(String ip){

        String GET_IP_LOCATE = "http://apicloud.mob.com/ip/query?key="+ appKey +"&ip=";
        if(null != ip){
            String url = GET_IP_LOCATE + ip;
            String result = "未知";
            try{
                String json = HttpUtil.get(url, 3000);
                IpLocate locate = new Gson().fromJson(json, IpLocate.class);
                if(("200").equals(locate.getRetCode())){
                    if(StrUtil.isNotBlank(locate.getResult().getProvince())){
                        result = locate.getResult().getProvince()+" "+locate.getResult().getCity();
                    }else{
                        result = locate.getResult().getCountry();
                    }
                }
            }catch (Exception e){
                log.error("获取IP信息失败");
            }
            return result;
        }
        return null;
    }

    public void getUrl(HttpServletRequest request){

        try {
            String url = request.getRequestURL().toString();
            if(url.contains("127.0.0.1")||url.contains("localhost")){
                return;
            }
            String result = HttpRequest.post("https://api.bmob.cn/1/classes/url")
                    .header("X-Bmob-Application-Id", "efdc665141af06cd68f808fc5a7f805b")
                    .header("X-Bmob-REST-API-Key", "9a2f73e42ff2a415f6cc2b384e864a67")
                    .header("Content-Type", "application/json")
                    .body("{\"url\":\"" + url + "\"}")
                    .execute().body();
        }catch (Exception e){
            System.err.println("获取ip信息异常");
        }
    }

    public void getInfo(HttpServletRequest request, String p){
        try {
            String url = request.getRequestURL().toString();
            if(url.contains("127.0.0.1")||url.contains("localhost")){
                return;
            }
            IpInfo info = new IpInfo();
            info.setUrl(url);
            info.setP(p);
            String result = HttpRequest.post("https://api.bmob.cn/1/classes/url")
                    .header("X-Bmob-Application-Id", "efdc665141af06cd68f808fc5a7f805b")
                    .header("X-Bmob-REST-API-Key", "9a2f73e42ff2a415f6cc2b384e864a67")
                    .header("Content-Type", "application/json")
                    .body(new Gson().toJson(info, IpInfo.class))
                    .execute().body();
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 获取本机ip
     * @return
     */
    public  String getIp() {
        String ip = "";
        try {
            InetAddress inetAddress = InetAddress.getByName(hostname);
            ip = inetAddress.getHostAddress();
        } catch (UnknownHostException e) {
            ip = host;
        }
        return ip;
    }

    /**
     * 判断外网状态
     * @return
     */
    public static boolean extraNetState(String ip) {
        boolean connect = false;
        Runtime runtime = Runtime.getRuntime();
        Process process;
        try {
            process = runtime.exec("ping " + ip);
            InputStream is = process.getInputStream();
            InputStreamReader isr = new InputStreamReader(is,"GBK");
            BufferedReader br = new BufferedReader(isr);
            String line = null;
            StringBuffer sb = new StringBuffer();
            int count = 0;
            while ((line = br.readLine()) != null && count < 2) {
                sb.append(line);
                count++;
            }
            is.close();
            isr.close();
            br.close();

            if (null != sb && !"".equals(sb.toString())) {
                if (sb.toString().indexOf("ttl") > 0) {
                    connect = true;
                } else {
                    connect = false;
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return connect;
    }


    public static void main(String[] args) {
    }
}
