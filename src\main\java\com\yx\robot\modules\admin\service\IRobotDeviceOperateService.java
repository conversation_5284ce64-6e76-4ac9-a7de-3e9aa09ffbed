package com.yx.robot.modules.admin.service;

import com.yx.robot.modules.admin.entity.RobotDeviceInfo;

/**
 * 设备信息接口
 *
 * <AUTHOR>
 */
public interface IRobotDeviceOperateService {

    /**
     * 操作设备接口
     *
     * @param id          设备信息ID
     * @param operateType 0:根据类型操作设备,1:根据通讯方式（control_method）操作设备
     * @param cmdTpye     命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    boolean operateDevice(String id, Integer operateType, Short cmdTpye);

    /**
     * 操作设备接口
     *
     * @param robotDeviceInfo 设备信息
     * @param operateType     0:根据类型操作设备,1:根据通讯方式（control_method）操作设备
     * @param cmdTpye         命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    boolean operateDevice(RobotDeviceInfo robotDeviceInfo, Integer operateType, Short cmdTpye);

    /**
     * 操作设备接口
     *
     * @param robotDeviceInfo 设备信息
     * @param cmdType         命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    boolean operateDeviceByType(RobotDeviceInfo robotDeviceInfo, Short cmdType);

    /**
     * 操作设备接口
     *
     * @param robotDeviceInfo 设备信息
     * @param cmdType         命令类型，每个话题或者服务，cmdType代表含义不同，具体意义参考话题或者服务说明
     * @return true:成功，false:失败
     */
    boolean operateDeviceByControlMethod(RobotDeviceInfo robotDeviceInfo, Short cmdType);

    /**
     * 门禁操作
     *
     * @param robotDeviceInfo 设备信息
     * @param cmdType         命令类型：3 设置参数。1开门，2关门
     * @return true:成功，false:失败
     */
    boolean operateEntranceGuard(RobotDeviceInfo robotDeviceInfo, Short cmdType);
}