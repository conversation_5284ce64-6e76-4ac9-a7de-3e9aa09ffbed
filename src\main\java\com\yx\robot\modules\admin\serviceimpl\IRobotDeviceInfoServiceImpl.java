package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.constant.DeviceInfoConstants;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dao.mapper.RobotDeviceInfoMapper;
import com.yx.robot.modules.admin.entity.RobotDeviceInfo;
import com.yx.robot.modules.admin.service.IRobotDeviceInfoService;
import com.yx.robot.modules.admin.service.IRobotDeviceOperateService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.util.Asserts;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 设备信息接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class IRobotDeviceInfoServiceImpl extends ServiceImpl<RobotDeviceInfoMapper, RobotDeviceInfo> implements IRobotDeviceInfoService {

    private RobotDeviceInfoMapper robotDeviceInfoMapper;

    private IRobotDeviceOperateService iRobotDeviceOperateService;

    /**
     * 更新设备信息
     *
     * @param robotDeviceInfo 设备信息
     * @return true:成功，false:失败
     */
    @Override
    public boolean updateDeviceInfo(RobotDeviceInfo robotDeviceInfo) {
        RobotDeviceInfo robotDeviceInfoOld = robotDeviceInfoMapper.selectById(robotDeviceInfo.getId());
        System.out.println(robotDeviceInfoOld);
        Asserts.check(ObjectUtil.isNotNull(robotDeviceInfoOld), "设备信息不存在");
        Asserts.check(StringUtils.isNotBlank(robotDeviceInfo.getQueryParams()), "设备参数不能为空");
        if (!robotDeviceInfoOld.getQueryParams().equals(robotDeviceInfo.getQueryParams())) {
            boolean operateFlg = iRobotDeviceOperateService.operateDeviceByType(robotDeviceInfo,
                    new Short(DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_SET + ""));
            Asserts.check(operateFlg, "设备设置失败");
        }
        return robotDeviceInfoMapper.updateById(robotDeviceInfo) > 0;
    }

    /**
     * 保存设备信息
     *
     * @param robotDeviceInfo 设备信息
     * @return true:成功，false:失败
     */
    @Override
    public boolean saveDeviceInfo(RobotDeviceInfo robotDeviceInfo) {
        Asserts.check(StringUtils.isNotBlank(robotDeviceInfo.getQueryParams()), "设备参数不能为空");
        //根据设备类型和设备子类型查询设备信息
        List<RobotDeviceInfo> queryRobotDeviceInfos = robotDeviceInfoMapper.selectList(new QueryWrapper<RobotDeviceInfo>()
                .eq("type", robotDeviceInfo.getType()).eq("sub_type", robotDeviceInfo.getSubType()));
        //当设备类型和设备子类型相同时，请求参数不能相同，如果相同，不让保存
        if (ObjectUtils.isNotNull(queryRobotDeviceInfos)) {
            for (RobotDeviceInfo queryRobotDeviceInfo : queryRobotDeviceInfos) {
                Asserts.check(!robotDeviceInfo.getQueryParams().equals(queryRobotDeviceInfo.getQueryParams()),
                        "类型相同并且请求参数相同，不允许保存");
            }
        }
        boolean operateFlg = iRobotDeviceOperateService.operateDeviceByType(robotDeviceInfo, new Short(DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_SET + ""));
        Asserts.check(operateFlg, "设备设置失败");
        return robotDeviceInfoMapper.insert(robotDeviceInfo) > 0;
    }

    /**
     * 删除设备信息
     *
     * @param id 设备ID
     * @return true:成功,false:失败
     */
    @Override
    public boolean deleteDevice(String id) {
        return false;
    }

    /**
     * 根据类型和子类型获取设备信息
     *
     * @param type
     * @param subType
     * @return
     */
    @Override
    public Result<List<RobotDeviceInfo>> getRobotDeviceInfoByTypeAndSubType(Integer type, Integer subType) {
        QueryWrapper<RobotDeviceInfo> wrapper = new QueryWrapper<RobotDeviceInfo>().eq("type", type).eq("sub_type", subType);
        List<RobotDeviceInfo> robotDeviceInfos = robotDeviceInfoMapper.selectList(wrapper);
        return new ResultUtil<List<RobotDeviceInfo>>().setData(robotDeviceInfos);
    }

    /**
     * 根据点位类型（“门禁内或门禁外”,“电梯内或电梯外”）获取设备信息
     *
     * @param type
     * @return
     */
    @Override
    public Result<List<RobotDeviceInfo>> getRobotDeviceInfoByPositionType(Integer type) {
        List<RobotDeviceInfo> robotDeviceInfos = null;
        if (RobotPositionType.INSIDE_ENTRANCE_GUARD.getType().equals(type) || RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType().equals(type)) {
            QueryWrapper<RobotDeviceInfo> wrapper = new QueryWrapper<RobotDeviceInfo>().eq("type", 0).eq("sub_type", 0);
            robotDeviceInfos = robotDeviceInfoMapper.selectList(wrapper);
        }
        if (RobotPositionType.INSIDE_ELEVATOR_POSITION.getType().equals(type) || RobotPositionType.OUTSIDE_ELEVATOR_POSITION.getType().equals(type)) {
            QueryWrapper<RobotDeviceInfo> wrapper = new QueryWrapper<RobotDeviceInfo>().eq("type", 0).eq("sub_type", 1);
            robotDeviceInfos = robotDeviceInfoMapper.selectList(wrapper);
        }
        return new ResultUtil<List<RobotDeviceInfo>>().setData(robotDeviceInfos);
    }

}