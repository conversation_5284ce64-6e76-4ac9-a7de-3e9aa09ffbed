package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.AutoDockType;
import com.yx.robot.common.enums.RobotOperationStatus;
import com.yx.robot.common.enums.RobotType;
import com.yx.robot.common.utils.EnumUtils;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import static com.yx.robot.common.constant.ControlStatusConstants.LAST_CORRECT_BATTERY_VALUE;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 14:03
 */
@Service("IRobotStatusServiceImpl")
@Slf4j
public class IRobotStatusServiceImpl implements IRobotStatusService {

    @Autowired
    private RosBridgeService rosBridgeService;

    /**
     * 检测水箱水位状态
     * <p>
     * 从缓存中获取当前的水位是否满了，满了则为true。
     *
     * @return true:满了 / false: 未满
     */
    @Override
    public boolean checkSprayLiquidOverflow() {
        String strBool = RedisUtil.getValue(TOPIC + "::" + TopicConstants.SPRAY_LIQUID_OVERFLOW);
        if (StringUtils.isBlank(strBool)) {
            return false;
        }
        return JSONObject.parseObject(strBool, _Bool.class).data;
    }

    /**
     * 获取充电状态
     *
     * @return true:正在充电/false：未充电
     */
    @Override
    public boolean isDock() {
        String autoDockStateResult = RedisUtil.getValue(TOPIC + "::" + TopicConstants.AUTO_DOCK_STATE);
        if (StringUtils.isBlank(autoDockStateResult)) {
            return false;
        }
        _AutoDockState autoDockState = JSONObject.parseObject(autoDockStateResult, _AutoDockState.class);
        return autoDockState.type == AutoDockType.MANUAL_DOCK.getType().shortValue()
                || autoDockState.type == AutoDockType.AUTO_DOCK.getType().shortValue()
                || autoDockState.type == AutoDockType.HANDLE_PUSH_DOCK.getType().shortValue();
    }

    /**
     * 获取机器人充电状态
     * <p>
     * 0：未充电，1：充电器充电（手动充电），2：自动充电，3：手推充电
     *
     * @return true:正在充电/false：未充电
     */
    @Override
    public Short getDockState() {
        String autoDockStateResult = RedisUtil.getValue(TOPIC + "::" + TopicConstants.AUTO_DOCK_STATE);
        if (StringUtils.isBlank(autoDockStateResult)) {
            return null;
        }
        _AutoDockState autoDockState = JSONObject.parseObject(autoDockStateResult, _AutoDockState.class);
        return autoDockState.type;
    }

    /**
     * 机器人液位状态检测
     * 系统初始化时，订阅ros话题，保存到redis。
     * 通过redis,获取获取缺液状态
     *
     * @return true:缺液，false:不缺液
     */
    @Override
    public boolean isSprayLiquidLevelWarning() {
        // X1 类型
        if (RobotType.X1.getType().equals(RobotBaseInfoConstant.type)) {
            String disinfectantResult = RedisUtil.getValue(TOPIC + "::" + TopicConstants.DISINFECTANT);
            if (StringUtils.isBlank(disinfectantResult)) {
                return true;
            }
            _Disinfectant disinfectant = JSON.parseObject(disinfectantResult, _Disinfectant.class);
            return 0.0f == disinfectant.data;
        }
        // U1,U3,U3pro类型(修改：新增U3pro类型判断)
        if (RobotType.U1.getType().equals(RobotBaseInfoConstant.type) || RobotType.U3.getType().equals(RobotBaseInfoConstant.type) || RobotBaseInfoConstant.type.contains(RobotType.U3.getType())) {
            boolean result = false;
            String sprayLiquidLevelStatus = RedisUtil.getValue(TOPIC + "::" + TopicConstants.SPRAY_WIQUID_STATUS);
            if (StrUtil.isNotEmpty(sprayLiquidLevelStatus)) {
                _Bool bool = JSON.parseObject(sprayLiquidLevelStatus, _Bool.class);
                if (ObjectUtil.isNotNull(bool)) {
                    // 缺液状态
                    result = bool.data;
                }
            }
            return result;
        }
        return false;
    }

    /**
     * 液位灯光话题发布
     *
     * @param operate
     */
    @Override
    public void publishLiquidLightTopic(Integer operate) {
        String s = String.valueOf(operate);
        _Uint8 uint8 = new _Uint8();
        uint8.data = Short.valueOf(s);
        rosBridgeService.publish(LIQUID_LIGHT_CONTROL, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
    }

    /**
     * 活物检测开关
     *
     * @return true/打开 false/关闭
     */
    @Override
    public boolean isLivingThingsUltraviolet() {
        Object livingThingsUlrayObj = RedisUtil.getHash(ROBOT_SYS_INFO, LIVING_THINGS_ULRAY);
        return ObjectUtil.isNotNull(livingThingsUlrayObj) && Boolean.parseBoolean(livingThingsUlrayObj.toString());
    }

    /**
     * 人体检测状态 是否发现人
     *
     * @return true（发现）/false(未发现)
     */
    @Override
    public boolean isHumanDetectionStatus() {
        String findPeopleStr = RedisUtil.getTopicValue(FIND_PEOPLE);
        return StringUtils.isNotBlank(findPeopleStr) && JSON.parseObject(findPeopleStr, _Bool.class).data;
    }

    /**
     * 是否开启紫外
     *
     * @return true（开启）/false(未开始)
     */
    @Override
    public boolean isUltravioletStatus() {
        String ulrayStatusStr = RedisUtil.getTopicValue(ULRAY_STATUS);
        return StringUtils.isNotBlank(ulrayStatusStr) && JSON.parseObject(ulrayStatusStr, _Bool.class).data;
    }

    /**
     * 是否开启喷雾
     *
     * @return true（开启）/false(未开始)
     */
    @Override
    public boolean isSprayStatus() {
        String sprayStatusStr = RedisUtil.getTopicValue(SPRAY_STATUS);
        return StringUtils.isNotBlank(sprayStatusStr) && JSON.parseObject(sprayStatusStr, _Bool.class).data;
    }

    /**
     * 是否开启脉冲
     *
     * @return true（开启）/false(未开始)
     */
    @Override
    public boolean isPulseStatus() {
        String pulseStatusStr = RedisUtil.getTopicValue(PULSE_STATUS);
        return StringUtils.isNotBlank(pulseStatusStr) && JSON.parseObject(pulseStatusStr, _Bool.class).data;
    }

    /**
     * 是否开启风扇
     *
     * @return true（开启）/false(未开始)
     */
    @Override
    public boolean isFanStatus() {
        String fanStatusStr = RedisUtil.getTopicValue(FAN_STATUS);
        return StringUtils.isNotBlank(fanStatusStr) && JSON.parseObject(fanStatusStr, _Bool.class).data;
    }

    /**
     * 是否打开语音告警
     *
     * @return true(打开) / false(未打开)
     */
    @Override
    public boolean isOpenVoiceWarning() {
        Object voiceWarningObj = RedisUtil.getHash(ROBOT_SYS_INFO, VOICE_WARNING);
        return ObjectUtil.isNotNull(voiceWarningObj) && Boolean.parseBoolean(voiceWarningObj.toString());
    }

    /**
     * 升降杆状态
     *
     * @return true(打开) / false(未打开)
     */
    @Override
    public boolean isShieldingStatus() {
        String val = RedisUtil.getTopicValue(SHIELDING_STATUS);
        return StringUtils.isNotBlank(val) && JSON.parseObject(val, _Bool.class).data;
    }

    /**
     * 获取机器人运行状态
     *
     * @return 机器人运行状态
     */
    @Override
    public RobotOperationStatus getOperationStatus() {
        RobotOperationStatus operationByKey = EnumUtils.getOperationByKey(getTaskStatus());
        return operationByKey;
    }

    /**
     * 检测任务状态
     *
     * @return true:开始任务 / false: 任务停止
     */
    @Override
    public boolean checkTaskStatus() {
        boolean res = true;
        String isRunning = RedisUtil.getHash(TASK_INFO, IS_RUNNING);
        String isStopping = RedisUtil.getHash(TASK_INFO, IS_STOPPING);
        if (Boolean.FALSE.toString().equalsIgnoreCase(isRunning)) {
            res = false;
        }
        if (Boolean.TRUE.toString().equalsIgnoreCase(isStopping)) {
            res = false;
        }
        return res;
    }

    @Override
    public Integer getTaskStatus(){
        String isRunning = RedisUtil.getHash(TASK_INFO, IS_RUNNING);
        String isStopping = RedisUtil.getHash(TASK_INFO, IS_STOPPING);
        Integer status = 0;
        if (StringUtils.isBlank(RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID)) || isDock()) {
            status =  RobotOperationStatus.SPARE.getValue();
        }
        if (StringUtils.isNotBlank(RedisUtil.getTopicValue(UDRIVE_AUTO_DOCK_STATE))) {
            status =  RobotOperationStatus.DOCKING.getValue();
        }
        if (Boolean.TRUE.toString().equalsIgnoreCase(isRunning) && StringUtils.isNotBlank(RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID))) {
            status = RobotOperationStatus.RUNNING.getValue();
        }
        if (Boolean.TRUE.toString().equalsIgnoreCase(isStopping) && StringUtils.isNotBlank(RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID))) {
            status = RobotOperationStatus.PAUSE.getValue();
        }
        return status;
    }

    /**
     * 获取机器人定位状态
     *
     * @return true:定位成功，false:定位失败
     */
    @Override
    public boolean getPositionStatus() {
        String result = RedisUtil.getHash(ROBOT_SYS_INFO, POSITION_STATUS);
        return StringUtils.isNotBlank(result) && SUCCESS.equals(result);
    }

    /**
     * 获取电池电量信息
     *
     * @return 电池全部信息
     */
    @Override
    public _BatteryState getBatteryState() {
        String topicBatteryResult = RedisUtil.getTopicValue(TopicConstants.BATTERY_STATE);
        _BatteryState batteryState = null;
        if (StrUtil.isNotEmpty(topicBatteryResult)) {
            batteryState = JSON.parseObject(topicBatteryResult, _BatteryState.class);
            if (!checkBatteryValue(batteryState)) {
                batteryState.percentage = LAST_CORRECT_BATTERY_VALUE;
                log.warn("获取当前电量值异常:{}", topicBatteryResult);
                log.warn("返回最近一次正常值:{}", batteryState.percentage);
                return batteryState;
            }
        } else {
            log.warn("电池信息获取异常,值为空");
        }
        return batteryState;
    }

    /**
     * 获取电池电量百分比
     *
     * @return 电量百分比
     */
    @Override
    public Float getBatteryPercentage() {
        _BatteryState batteryState = getBatteryState();
        if (ObjectUtil.isNull(batteryState)) {
            return null;
        } else {
            return batteryState.percentage;
        }
    }

    /**
     * 检查获取到的电量信息是否正常
     * 上一次正常值和当前值的差值在 5 以内则认为当前值是正常
     *
     * @param batteryState 当前电池电量信息
     * @return true(正常)/false(异常)
     */
    private boolean checkBatteryValue(_BatteryState batteryState) {
        if (ObjectUtil.isNull(LAST_CORRECT_BATTERY_VALUE)) {
            LAST_CORRECT_BATTERY_VALUE = batteryState.percentage;
        }
        boolean isCorrect = Math.abs(LAST_CORRECT_BATTERY_VALUE - batteryState.percentage) < 5.0f;
        // 保存当前的正常值，作为最近一次的正常值
        if (isCorrect) {
            LAST_CORRECT_BATTERY_VALUE = batteryState.percentage;
        } else {
            log.warn("电量波动较大，当前电量值异常:{}", batteryState.percentage);
        }
        return batteryState.percentage > 1.0f;
    }

    /**
     * 获取防跌落状态
     *
     * @return true:防跌落触发,false: 防跌落暂停
     */
    @Override
    public Boolean getMotorLock() {
        String motorLockStr = RedisUtil.getTopicValue(MOTOR_LOCK);
        if (StringUtils.isBlank(motorLockStr)) {
            return false;
        }
        Boolean isStop = isStopping();
        // 急停被按下或者充电状态下，防跌落，不会发消息
        if (isDock() || isStop) {
            RedisUtil.delTopicValue(MOTOR_LOCK);
        }
        _MotorLock motorLockData = JSONObject.parseObject(motorLockStr, _MotorLock.class);
        return motorLockData.data;
    }

    /**
     * 急停是否触发
     *
     * @return true/false
     */
    public boolean isStopping() {
        Jedis jedis = null;
        boolean result = false;
        try {
            jedis = RedisUtil.getJedis();
            String emergencyButton = jedis.get(TOPIC + "::" + EMERGENCY_BUTTON);
            if (StrUtil.isNotEmpty(emergencyButton)) {
                _Bool bool = JSON.parseObject(emergencyButton, _Bool.class);
                result = bool.data;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return result;
    }

    /**
     * 判断机器人是否接触到充电片
     *
     * @return true(是)/false(否)
     */
    @Override
    public Boolean getSystemChargeState() {
        String val = RedisUtil.getTopicValue(SYSTEM_CHARGE_STATE);
        return StringUtils.isNotBlank(val) && JSON.parseObject(val, _Bool.class).data;
    }
}
