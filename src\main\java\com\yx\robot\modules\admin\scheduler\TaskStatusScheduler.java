package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.config.wesocket.WebSocketServer;
import com.yx.robot.modules.admin.message._BatteryState;
import com.yx.robot.modules.admin.message._Disinfectant;
import com.yx.robot.modules.admin.message._DoctorModeStatus;
import com.yx.robot.modules.admin.message._RobotNavStatus;
import com.yx.robot.modules.admin.service.IRobotDisinfectService;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.IRobotTaskService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.serviceimpl.IRobotChargingRecordServiceImpl;
import com.yx.robot.modules.admin.vo.DisinfectTaskVo;
import com.yx.robot.modules.admin.vo.PositionsVo;
import com.yx.robot.modules.admin.vo.RouteVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.concurrent.BasicThreadFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.core.annotation.Order;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import static com.yx.robot.common.constant.ControlStatusConstants.CREATE_MAP_STATUE;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.DOCTOR_MODE_STATE;
import static com.yx.robot.common.constant.WebSocketConstants.MEALS;
import static com.yx.robot.common.enums.DisinfectTaskType.LINE_PATROL_TASK;
import static com.yx.robot.common.enums.DisinfectTaskType.ROUTINE_TASK;
import static com.yx.robot.common.enums.NavType.*;

@Component
@Slf4j
@Order(2)
public class TaskStatusScheduler implements CommandLineRunner {

    private static final ScheduledExecutorService executorService = new ScheduledThreadPoolExecutor(
            1, new BasicThreadFactory.Builder().namingPattern("scheduler-taskStatus-%d").daemon(true).build());

    private final WebSocketServer webSocketServer;

    private final RosWebService rosWebService;

    private final IRobotStatusService iRobotStatusService;

    private final IRobotTaskService iRobotTaskService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    private static boolean flag = false;

    /**
     * 是否发送满电或者空闲状态灯光次数
     */
    private static boolean flag1 = false;

    public TaskStatusScheduler(WebSocketServer webSocketServer, RosWebService rosWebService,
                               IRobotStatusService iRobotStatusService, IRobotTaskService iRobotTaskService) {
        this.webSocketServer = webSocketServer;
        this.rosWebService = rosWebService;
        this.iRobotStatusService = iRobotStatusService;
        this.iRobotTaskService = iRobotTaskService;
    }

    @Override
    public void run(String... args) {
        executorService.scheduleWithFixedDelay(() -> {
            this.stopNotExecuteTask();
            this.taskStatus();
        }, 1, 1, TimeUnit.SECONDS);
        log.info("任务状态检测中......");
    }

    /**
     * 检测不在可执行时间段内的任务
     */
    private void stopNotExecuteTask() {
        String taskId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        iRobotTaskService.cancelNotInExecutableTimeTask(taskId);
    }

    private void taskStatus() {
        Map<String, Object> result = new HashMap<>();
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            if (jedis == null) {
                return;
            }

            String topicNavResult = jedis.get(TOPIC + "::" + TopicConstants.NAV_STATUS);
            if (StrUtil.isNotEmpty(topicNavResult)) {
                _RobotNavStatus robotNavStatus = JSON.parseObject(topicNavResult, _RobotNavStatus.class);
                result.put(STATUS_CODE, robotNavStatus.statusCode);
            }
            _BatteryState batteryState = iRobotStatusService.getBatteryState();
            float remainBattery = 100f;
            boolean charging = rosWebService.isCharging();
            if (ObjectUtil.isNotNull(batteryState)) {
                remainBattery = batteryState.percentage;
                if (remainBattery <= 100f && remainBattery >= 97f && charging) {
                    String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
                    RedisUtil.hset(ROBOT_SYS_INFO, END_CHARGING_MAP_ID, currentMap);
                    rosWebService.sendRingLightPrompt(RingLightDefine.FULL_BATTERY_STATE);
                    flag1 = false;
                    // 在此加一个是否在建图状态的判断，如果在建图就不发充电中（开始建图不会弹出充电桩）
                } else if (charging && !CREATE_MAP_STATUE) {
                    String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
                    RedisUtil.hset(ROBOT_SYS_INFO, END_CHARGING_MAP_ID, currentMap);
                    rosWebService.sendRingLightPrompt(RingLightDefine.CHARGING_STATE);
                } else {
                    if (!flag1) {
                        Integer taskType = iRobotTaskService.getCurrentTaskType();
                        if (ObjectUtil.isNull(taskType) || TaskType.CHARGING.getType().equals(taskType) || TaskType.ORIGIN.getType().equals(taskType)) {
                            rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
                            flag1 = true;
                        }
                    }
                }
                result.put(REMAIN_BATTERY, remainBattery);
                if (charging) {
                    //语音
                    if (!flag) {
                        // 防止满电存在误报问题
                        if (remainBattery <= 95F) {
                            rosWebService.sendVoicePrompt(SceneType.CHARGING, null);
                            // 如果充上电，将对接充电桩失败和前往充电失败以及路径规划失败短信控制条件重置
                            IRobotChargingRecordServiceImpl.charingDockFailureMessage = true;
                            IRobotChargingRecordServiceImpl.cycleOutChargingMessage = true;
                            RobotTaskScheduler.pathPlanningFailedMessage = true;
                        }
                        // 关掉广告屏
                        rosWebService.adControl(false);
                        flag = true;
                    }
                } else {
                    if (flag) {
                        // 环形灯
                        Integer taskType = iRobotTaskService.getCurrentTaskType();
                        if (ObjectUtil.isNull(taskType) || TaskType.CHARGING.getType().equals(taskType) || TaskType.ORIGIN.getType().equals(taskType)) {
                            rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
                        }
                        // 打开广告屏
                        rosWebService.adControl(true);
                        flag = false;
                    }
                }
            }
            result.put(REMAIN_BATTERY, remainBattery);
            // 电量充满剩余时间,默认从空电状态充满需要6小时
            remainBattery = Float.parseFloat(result.get(REMAIN_BATTERY).toString());
            int remainChargingTime = (int) ((100f - remainBattery) / 100f) * 6 * 60;
            result.put(REMAIN_CHARGING_TIME, remainChargingTime);
            String nextLocationInfoStr = jedis.hget(TASK_INFO, NEXT_LOCATION_INFO);
            String nextLocationCodeStr = jedis.hget(TASK_INFO, NEXT_LOCATION_CODE);
            String nextTargetStr = jedis.hget(TASK_INFO, NEXT_TARGET);
            String taskTypeStr = jedis.hget(TASK_INFO, TASK_TYPE);
            result.put(NEXT_LOCATION_INFO, nextLocationInfoStr);
            result.put(NEXT_LOCATION_CODE, nextLocationCodeStr);
            result.put("next_lay", "");
            result.put(NEXT_TARGET, nextTargetStr);
            result.put(TASK_TYPE, taskTypeStr);
            result.put("is_clear", "false");
            result.put(IS_CHARGING, Boolean.valueOf(charging).toString());
            String alertMsg = getAlertMsg(result.get(STATUS_CODE) == null ? "" : result.get(STATUS_CODE).toString(),
                    taskTypeStr,
                    nextTargetStr,
                    result.get("next_lay").toString(),
                    nextLocationInfoStr,
                    nextLocationCodeStr);
            String autoChargingStatus = jedis.hget(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS);
            // 对接充电桩流程
            // 处于充电模式中
            if (autoChargingStatus.equals(AutoChargingStatus.DOING.getValue().toString())) {
                result.put(TASK_TYPE, TaskType.CHARGING.getType());
                alertMsg = "对接充电桩中......";
                result.put(ALERT_MSG, alertMsg);
                result.put(STATUS_CODE, NavType.NAV_DOING_TYPE.getType());
            }
            if (autoChargingStatus.equals(AutoChargingStatus.SUCCESS.getValue().toString()) || autoChargingStatus.equals(AutoChargingStatus.FAIL.getValue().toString())) {
                if (charging) {
                    result.put(TASK_TYPE, TaskType.CHARGING.getType());
                    result.put(STATUS_CODE, NavType.NAV_FINISH_TYPE.getType());
                }
            }
            // 脱离充电桩流程
            String endCharging = jedis.hget(ROBOT_SYS_INFO, END_CHARGING);
            if (StrUtil.isNotEmpty(endCharging) && Boolean.TRUE.toString().equalsIgnoreCase(endCharging)) {
                result.put(TASK_TYPE, TaskType.CHARGING.getType());
                result.put(STATUS_CODE, NavType.NAV_DOING_TYPE.getType());
                alertMsg = "脱离充电桩中......";
                result.put(ALERT_MSG, alertMsg);
            }
            result.put(ALERT_MSG, alertMsg);
            float remainDisinfectant = 100f;
            String disinfectantResult = jedis.get(TOPIC + "::" + TopicConstants.DISINFECTANT);
            if (StrUtil.isNotEmpty(disinfectantResult)) {
                _Disinfectant disinfectant = JSON.parseObject(disinfectantResult, _Disinfectant.class);
                remainDisinfectant = disinfectant.data * 100;
            }
            remainDisinfectant = Float.parseFloat(String.format("%.2f", remainDisinfectant));
            result.put(REMAIN_DISINFECTANT, remainDisinfectant);
            String preShutdown = jedis.hget(ROBOT_SYS_INFO, PRE_SHUTDOWN);
            result.put(PRE_SHUTDOWN, preShutdown);
            String manualDisinfect = jedis.hget(ROBOT_SYS_INFO, MANUAL_DISINFECT);
            if (StrUtil.isNotEmpty(manualDisinfect)) {
                if (Boolean.TRUE.toString().equalsIgnoreCase(manualDisinfect)) {
                    alertMsg = "定点消毒模式进行中......";
                    rosWebService.sendRingLightPrompt(RingLightDefine.WARNING_STATE);
                    result.put(STATUS_CODE, NavType.NAV_DOING_TYPE.getType());
                    result.put(TASK_TYPE, TaskType.DISINFECT.getType());
                    result.put(ALERT_MSG, alertMsg);
                } else {
                    rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
                    result.put(STATUS_CODE, NavType.NAV_FINISH_TYPE.getType());
                    result.put(TASK_TYPE, TaskType.ORIGIN.getType());
                    Thread.sleep(3000);
                    jedis.hdel(ROBOT_SYS_INFO, MANUAL_DISINFECT);
                }
            }
            String lineDisinfect = jedis.hget(ROBOT_SYS_INFO, LINE_DISINFECT);
            if (StrUtil.isNotEmpty(lineDisinfect)) {
                if (Boolean.TRUE.toString().equalsIgnoreCase(lineDisinfect)) {
                    alertMsg = "巡线消毒模式进行中......";
                    rosWebService.sendRingLightPrompt(RingLightDefine.DISINFECT_STATE);
                    result.put(STATUS_CODE, NavType.NAV_DOING_TYPE.getType());
                    result.put(TASK_TYPE, TaskType.DISINFECT.getType());
                    result.put(ALERT_MSG, alertMsg);
                } else {
//                    String s = jedis.get(TOPIC + "::" + DOCTOR_MODE_STATE);
                    String s = RedisUtil.getTopicValue(DOCTOR_MODE_STATE);
                    alertMsg = "";
                    if (StrUtil.isNotBlank(s)) {
                        _DoctorModeStatus doctorModeStatus = JSONObject.parseObject(s, _DoctorModeStatus.class);
                        if (doctorModeStatus.doctorState == 2) {
                            alertMsg = "巡线消毒模式暂停中......";
                            rosWebService.sendRingLightPrompt(RingLightDefine.DISINFECT_STATE);
                            //发布工作中的点阵表情话题---工作被打扰
                            rosWebService.publishUtil(ExpressionType.WORK_DISTURBED.getValue());
                            result.put(STATUS_CODE, NavType.NAV_DOING_TYPE.getType());
                            result.put(TASK_TYPE, TaskType.DISINFECT.getType());
                        } else if (doctorModeStatus.doctorState == 3) {
                            alertMsg = "巡线消毒模式继续中......";
                            rosWebService.sendRingLightPrompt(RingLightDefine.DISINFECT_STATE);
                            rosWebService.publishUtil(ExpressionType.WORKING.getValue());
                            result.put(STATUS_CODE, NavType.NAV_DOING_TYPE.getType());
                            result.put(TASK_TYPE, TaskType.DISINFECT.getType());
                        } else {
                            if (doctorModeStatus.doctorState == 4 || doctorModeStatus.doctorState == 5) {
                                alertMsg = "巡线消毒模式已完成";
                            } else if (doctorModeStatus.doctorState == EMERGENCY_MAKE_TYPE.getType()) {
                                alertMsg = "急停开关被摁下";
                            }
                            else if (doctorModeStatus.doctorState == COLLISION_MAKE_TYPE.getType()) {
                                alertMsg = "防撞条被触碰";
                            }
                            else if (doctorModeStatus.doctorState == MOTOR_MAKE.getType()) {
                                alertMsg = "电机未使能";
                            }
//                            else if (doctorModeStatus.doctorState == LOCATION_MAKE_TYPE.getType()) {
//                                alertMsg = "定位问题";
//                            }
                            else if (doctorModeStatus.doctorState == NODES_MAKE_TYPE.getType()) {
                                alertMsg = "节点错误";
                            } else if (doctorModeStatus.doctorState == CLOSE_OBSTACLE_MAKE.getType() || doctorModeStatus.doctorState == PLAN_MAKE_TYPE.getType()) {
                                alertMsg = "靠近障碍物导致局部路径规划失败";
                            } else if (doctorModeStatus.doctorState == DEPTH_MAKE_TYPE.getType()) {
                                alertMsg = "深度问题";
                            } else if (doctorModeStatus.doctorState == MANUAL_CANCEL_TYPE.getType()) {
                                alertMsg = "手动取消";
                            }
                            rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
                            result.put(STATUS_CODE, NavType.NAV_FINISH_TYPE.getType());
                            result.put(TASK_TYPE, TaskType.ORIGIN.getType());
                            jedis.hdel(ROBOT_SYS_INFO, LINE_DISINFECT);
                        }
                        result.put(ALERT_MSG, alertMsg);
                    }
                }
            }
            jedis.hset(TASK_INFO, TASK_DATA, JSON.toJSONString(result));
            Map map = sendTaskInfoToApp(result);
            webSocketServer.sendAllMessage(JSON.toJSONString(map), MEALS);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 根据当前任务ID获取任务信息，并通过websocket发送给APP
     */
    private Map sendTaskInfoToApp(Map<String, Object> map) {
        Object taskId = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        if (ObjectUtil.isNull(taskId)) {
            return map;
        }
        DisinfectTaskVo task = rosWebService.getDisinfectTask(taskId.toString());
        if (ROUTINE_TASK.getType().equals(task.getType())) {
            List<PositionsVo> positionsVoList = iRobotDisinfectService.getPoseInfoBytaskId(taskId.toString());
            map.put("fixed_point_task", positionsVoList);
        }
        if (LINE_PATROL_TASK.getType().equals(task.getType())) {
            RouteVo routeVo = iRobotDisinfectService.getRouteInfoByTaskId(taskId.toString());
            map.put("line_patrol_task", routeVo);
        }
        return map;
    }


    /**
     * 获取提示信息
     *
     * @return 提示信息
     */
    private String getAlertMsg(String statusCodeStr, String taskTypeStr, String nextTarget, String nextLay, String nextLocationInfo, String nextLocationCode) {
        String content = "";
        if (StrUtil.isEmpty(statusCodeStr) || StrUtil.isEmpty(taskTypeStr)) {
            return content;
        }
        Integer statusCode = Integer.valueOf(statusCodeStr);
        Integer taskType = Integer.valueOf(taskTypeStr);
        if (statusCode.equals(NavType.NAV_DOING_TYPE.getType())) {
            if (taskType.equals(TaskType.ORIGIN.getType())) {
                content = "返回" + nextTarget + "中......";
                rosWebService.sendRingLightPrompt(RingLightDefine.NAVING_STATE);
            } else if (taskType.equals(TaskType.CHARGING.getType())) {
                content = "充电模式，准备前往" + nextTarget + "中......";
                rosWebService.sendRingLightPrompt(RingLightDefine.NAVING_STATE);
            } else if (taskType.equals(TaskType.ENTRANCE_GUARD.getType())) {
                content = "门禁模式，准备前往" + nextTarget + "中......";
                rosWebService.sendRingLightPrompt(RingLightDefine.NAVING_STATE);
            } else if (taskType.equals(TaskType.ELEVATOR.getType())) {
                content = "梯控模式，准备前往" + nextLocationInfo + nextLocationCode + "中.....";
                rosWebService.sendRingLightPrompt(RingLightDefine.NAVING_STATE);
            } else if (taskType.equals(TaskType.DISINFECT.getType())) {
                content = "消毒模式，准备前往" + nextLocationInfo + nextLocationCode + "中.....";
                rosWebService.sendRingLightPrompt(RingLightDefine.DISINFECT_STATE);
            }
        } else if (statusCode.equals(NavType.NAV_FINISH_TYPE.getType()) || statusCode.equals(NavType.NAV_FINISH_NEARBY_TYPE.getType())) {
            if (taskType.equals(TaskType.ORIGIN.getType())) {
                content = "已到达" + nextTarget;
                rosWebService.sendRingLightPrompt(RingLightDefine.IDLE_STATE);
            } else if (taskType.equals(TaskType.CHARGING.getType())) {
                content = "充电模式，已到达" + nextTarget;
            } else if (taskType.equals(TaskType.ENTRANCE_GUARD.getType())) {
                content = "门禁模式，已到达" + nextTarget;
            } else if (taskType.equals(TaskType.ELEVATOR.getType())) {
                content = "梯控模式，已到达" + nextLocationInfo + nextLocationCode;
            } else if (taskType.equals(TaskType.DISINFECT.getType())) {
                content = "消毒模式，已到达" + nextLocationInfo + nextLocationCode;
                rosWebService.sendRingLightPrompt(RingLightDefine.DISINFECT_STATE);
            }
        } else if (statusCode.equals(NavType.EMERGENCY_MAKE_TYPE.getType())) {
            content = "急停被摁下,请松开急停按钮哦,并点击继续工作按钮";
        }
        else if (statusCode.equals(NavType.COLLISION_MAKE_TYPE.getType())) {
            content = "哎呀呀，好像出车祸了";
        }
//        else if (statusCode.equals(NavType.LOCATION_MAKE_TYPE.getType())) {
//            content = "小哥哥，人家迷路了呢，请把我推到标签附近并点击系统恢复哦";
//        }
        else if (statusCode.equals(NavType.NODES_MAKE_TYPE.getType())) {
            content = "内部错误，请尝试重启并联系管理员";
        } else if (statusCode.equals(NavType.DEPTH_MAKE_TYPE.getType())) {
            content = "深度异常，请尝试重启并联系管理员";
        } else if (statusCode.equals(NavType.PLAN_MAKE_TYPE.getType())) {
            content = "路径规划失败,请确保机器人附近或目标位置没有障碍物";
        }
        return content;
    }
}
