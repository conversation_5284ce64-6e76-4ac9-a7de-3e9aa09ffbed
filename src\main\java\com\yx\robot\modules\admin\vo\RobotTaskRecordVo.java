package com.yx.robot.modules.admin.vo;

import com.yx.robot.modules.admin.entity.RobotTaskRecord;
import lombok.Data;

import java.util.Date;

/**
 * 机器人消息记录视图层
 * <AUTHOR>
 * @date 2020/05/11
 */
@Data
public class RobotTaskRecordVo{

    public RobotTaskRecordVo(RobotTaskRecord robotTaskRecord) {
        this.id = robotTaskRecord.getId();
        this.uuId = robotTaskRecord.getUuId();
        this.startTime = robotTaskRecord.getStartTime();
        this.endTime = robotTaskRecord.getEndTime();
        this.robotLocation = robotTaskRecord.getRobotLocation();
        this.mileage = robotTaskRecord.getMileage();
        this.timeConsume = robotTaskRecord.getTimeConsume();
        this.status = robotTaskRecord.getStatus();
    }

    private String id;

    /**
     * uuId
     */
    private String uuId;

    /**
     * serialNumber
     */
    private String serialNumber;

    /**
     * 任务类型
     */
    private Integer taskType;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 结束时间
     */
    private Date endTime;

    /**
     * 机器人位置信息
     */
    private String robotLocation;

    /**
     * 机器人行驶里程
     */
    private Double mileage;

    /**
     * 任务耗时
     */
    private long timeConsume;

    /**
     * 任务执行状态
     * 0 为初始状态
     * 405 导航中
     * 407 导航已到达
     * 4071 到达目标点附近
     * 408 导航未到达
     * 4080 急停开关被摁下
     * 4081 防撞条触碰
     * 4082 定位问题
     * 4083 节点错误
     * 4084 全局规划失败
     * 4085 局部规划失败
     * 4086 深度问题
     * 4088 手动取消
     */
    private String status;
    
}
