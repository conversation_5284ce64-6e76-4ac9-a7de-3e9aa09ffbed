package com.yx.robot.modules.admin.entity;

import cn.hutool.core.date.DatePattern;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_switch_record")
@ApiModel(value = "机器人开关机记录")
public class RobotSwitchRecord extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "开始时间")
    @JsonFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    @DateTimeFormat(pattern = DatePattern.NORM_DATETIME_PATTERN)
    private Date startTime;

    @ApiModelProperty(value = "动作(0:关机 1:开机)")
    private Integer action;

    @ApiModelProperty(value = "类型(0:低电量 1:手动 2: 自动 3：远程)")
    private Integer type;

    @ApiModelProperty(value = "电量百分比")
    private Double percentage;

    @ApiModelProperty(value = "同步状态 1 已同步 0 未同步")
    private String syncStatus;
}