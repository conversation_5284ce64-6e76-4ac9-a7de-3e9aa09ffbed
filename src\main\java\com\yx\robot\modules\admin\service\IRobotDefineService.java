package com.yx.robot.modules.admin.service;

import com.yx.robot.modules.admin.dto.DirectionalControlDto;

import java.util.Map;

/**
 * 机器人设备服务类
 *
 * <AUTHOR>
 * @date 2021/04/22
 */
public interface IRobotDefineService {

    /**
     * 初始化机器人信息
     */
    void initRobotInfo();

    /**
     * 修改保养信息
     *
     * @param key   key
     * @param value value
     * @return
     */
    boolean updateDeviceInfo(String key, String value);

    /**
     * 获取机器人设备信息
     *
     * @return
     */
    Map<String, Object> getDeviceInfo();

    /**
     * 方向控制
     *
     * @param directionalControlDto 方向控制参数
     * @return true:成功，false:失败
     */
    boolean directionControl(DirectionalControlDto directionalControlDto);

    /**
     * 获取当前机器人运动状态
     *
     * @return
     */
    DirectionalControlDto getDirectionalControl();
}
