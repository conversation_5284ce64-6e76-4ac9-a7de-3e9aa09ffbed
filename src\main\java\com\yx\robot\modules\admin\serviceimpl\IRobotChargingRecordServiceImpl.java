package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.enums.ChargingCondition;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotChargingRecordMapper;
import com.yx.robot.modules.admin.entity.RobotChargingRecord;
import com.yx.robot.modules.admin.message._AutoDockState;
import com.yx.robot.modules.admin.message._BatteryState;
import com.yx.robot.modules.admin.message._Marker;
import com.yx.robot.modules.admin.service.IRobotChargingRecordService;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.util.Date;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.AUTO_DOCK_STATE;
import static com.yx.robot.common.constant.TopicConstants.POINT_MARKER;

/**
 * 机器人充电记录接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class IRobotChargingRecordServiceImpl extends ServiceImpl<RobotChargingRecordMapper, RobotChargingRecord> implements IRobotChargingRecordService {

    @Autowired
    private IRobotStatusService iRobotStatusService;

    /**
     * 充电桩对接失败短信通知
     */
    public static boolean charingDockFailureMessage = true;

    /**
     * 前往充电失败循环退出
     */
    public static boolean cycleOutChargingMessage = true;

    /**
     * 保存充电记录
     *
     * @param type 1:开始充电 2:结束充电
     */
    @Override
    public void saveRobotChargingRecord(Integer type) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String hasCharging = jedis.hget(ROBOT_SYS_INFO, HAS_CHARGING);
            // 开始充电
            if (type == 1) {
                if (StrUtil.isNotBlank(hasCharging) && Boolean.parseBoolean(hasCharging)) {
                    return;
                }
                jedis.hset(ROBOT_SYS_INFO, HAS_CHARGING, "true");
            }
            // 结束充电
            if (type == 2) {
                if (StrUtil.isNotBlank(hasCharging) && !Boolean.parseBoolean(hasCharging)) {
                    return;
                }
                jedis.hdel(ROBOT_SYS_INFO, CHARGING_CONDITION);
                jedis.hset(ROBOT_SYS_INFO, HAS_CHARGING, "false");
            }
            _BatteryState batteryState = iRobotStatusService.getBatteryState();
            String autoDockStateStr = jedis.get(TOPIC + "::" + AUTO_DOCK_STATE);
            String pointMarkerStateStr = jedis.get(TOPIC + "::" + POINT_MARKER);
            RobotChargingRecord robotChargingRecord = new RobotChargingRecord();
            String taskRecordId = jedis.hget(ROBOT_SYS_INFO, LAST_CHARGING_TASK_RECORD_ID);
            robotChargingRecord.setTaskRecordId(taskRecordId);
            robotChargingRecord.setType(type);
            robotChargingRecord.setStartTime(new Date());
            robotChargingRecord.setTryTimes(0);
            String chargingCondition = jedis.hget(ROBOT_SYS_INFO, CHARGING_CONDITION);
            if (StrUtil.isNotBlank(chargingCondition) && type == 1) {
                robotChargingRecord.setConditionType(Integer.valueOf(chargingCondition));
            } else {
                robotChargingRecord.setConditionType(ChargingCondition.NO_CONDITION.getType());
            }
            if (ObjectUtil.isNotNull(batteryState)) {
                robotChargingRecord.setVoltage((double) batteryState.voltage);
                robotChargingRecord.setCurrentFlow((double) batteryState.current);
                robotChargingRecord.setPercentage((double) batteryState.percentage);
            }
            if (StrUtil.isNotBlank(autoDockStateStr)) {
                _AutoDockState autoDockState = JSONObject.parseObject(autoDockStateStr, _AutoDockState.class);
                robotChargingRecord.setTryTimes((int) autoDockState.try_times);
                if (type == 1) {
                    if (!autoDockState.working && !autoDockState.charged) {
                        robotChargingRecord.setStatus(autoDockState.error + "");
                    }
                    if (autoDockState.charged) {
                        robotChargingRecord.setStatus("1");
                    }
                }
            }
            if (type == 2) {
                if (StrUtil.isNotBlank(pointMarkerStateStr)) {
                    _Marker marker = JSON.parseObject(pointMarkerStateStr, _Marker.class);
                    if (marker.id == 15) {
                        // 自动脱离充电桩
                        robotChargingRecord.setStatus("0");
                    } else {
                        robotChargingRecord.setStatus("1");
                    }
                } else {
                    robotChargingRecord.setStatus("1");
                }
            }
            robotChargingRecord.setDataStatus("1");
            robotChargingRecord.setSyncStatus("0");
            this.save(robotChargingRecord);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }


}