package com.yx.robot.modules.admin.service.core;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.yx.robot.YxApplication;
import com.yx.robot.common.constant.ControlStatusConstants;
import com.yx.robot.common.constant.RosWebConstants;
import com.yx.robot.common.enums.ChargingCondition;
import com.yx.robot.common.enums.ExpressionType;
import com.yx.robot.common.enums.SceneType;
import com.yx.robot.common.enums.TalkScene;
import com.yx.robot.common.utils.EnumUtils;
import com.yx.robot.common.utils.IpInfoUtil;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.controller.ProgramRestarter;
import com.yx.robot.modules.admin.message._BatteryState;
import com.yx.robot.modules.admin.message._Talk;
import com.yx.robot.modules.admin.message._Uint8;
import com.yx.robot.modules.admin.scheduler.RabbitMQScheduler;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.service.IRobotTaskService;
import com.yx.robot.modules.admin.vo.DisinfectTaskVo;
import com.yx.robot.modules.base.utils.SendMessageUtil;
import edu.wpi.rail.jrosbridge.Ros;
import edu.wpi.rail.jrosbridge.Service;
import edu.wpi.rail.jrosbridge.Topic;
import edu.wpi.rail.jrosbridge.callback.ServiceCallback;
import edu.wpi.rail.jrosbridge.messages.Message;
import edu.wpi.rail.jrosbridge.services.ServiceRequest;
import edu.wpi.rail.jrosbridge.services.ServiceResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.data.redis.core.RedisConnectionUtils;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import redis.clients.jedis.Jedis;
import ros.RosBridge;
import ros.RosListenDelegate;
import ros.SubscriptionRequestMsg;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.concurrent.*;

import static com.yx.robot.common.constant.ControlStatusConstants.LAST_EXPRESSION_SEND;
import static com.yx.robot.common.constant.ControlStatusConstants.TALK_STATUS_CTRL;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.StatusConstants.BATTERY_POWER_EXPRESSION;
import static com.yx.robot.common.constant.StatusConstants.BATTERY_POWER_THRESHOLD_WARNING;

/**
 * <AUTHOR>
 * @date 2020/4/1
 */
@Component
@Slf4j
public class RosBridgeService {

    @Autowired
    private IpInfoUtil ipInfoUtil;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${robot.port2}")
    private Integer port2;

    private static ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    private static Ros ros = null;

    private static RosBridge rosBridge = null;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Autowired
    private RosWebService rosWebService;

    @PostConstruct
    public void init() {
        try {
            String host = ipInfoUtil.getIp();
            //服务专用
            ros = new Ros(host, port2);
            boolean connect1 = false;
            boolean connect2 = false;
            //话题专用
            rosBridge = new RosBridge();
            String addr = "ws://" + host + ":" + port2;
            for (int i = 0; i < 3; i++) {
                connect1 = ros.connect();
                // 等待连接
                rosBridge.connect(addr, false);
                Thread.sleep(3000);
                connect2 = rosBridge.hasConnected();
                log.warn("尝试第" + (i + 1) + "次连接ros....");
                if (connect1 && connect2) {
                    break;
                }
            }
            if (connect1 && connect2) {
                log.info("ros连接成功......");
            } else {
                log.error("ros连接失败......");
                ProgramRestarter.restartApplication();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 两个都连接
     *
     * @return true/false
     */
    public boolean rosOnline() {
        if (null != ros && null != rosBridge) {
            boolean res1 = rosBridge.hasConnected();
            boolean res2 = ros.isConnected();
            return res1 && res2;
        } else {
            return false;
        }
    }

    /**
     * msg publish
     *
     * @param topic   话题名称
     * @param msgType 消息类型
     * @param content 消息内容
     */
    public synchronized void publish(String topic, String msgType, String content) {
        if (!ros.isConnected()) {
            ros.connect();
        }
        Topic t = new Topic(ros, topic, msgType);
        Message message = new Message(content);
        t.publish(message);
    }

    /**
     * 点阵表情专用话题发布
     *
     * @param topic   话题名称
     * @param msgType 消息类型
     * @param content 消息内容
     */
    public synchronized void publishExpression(String topic, String msgType, String content) {
        if (!ros.isConnected()) {
            ros.connect();
        }
        Topic t = new Topic(ros, topic, msgType);
//      是否返回充电（获取当前任务来判断是否返回充电也可以）
        boolean gotoCharing = ControlStatusConstants.EXPRESSION_PRIORITY.gotoCharing;
//      机器人运行状态---是否工作中
        boolean working = iRobotStatusService.getOperationStatus().getValue() == 1;
//      电池信息
        _BatteryState batteryState = iRobotStatusService.getBatteryState();
        //电池电量
        float batteryEletricRemain = 0.0f;
        if (ObjectUtil.isNotNull(batteryState)) {
            //电池电量
            batteryEletricRemain = batteryState.percentage;
        }
//      是否低电；true低电（20%）
        boolean lowBattery = batteryEletricRemain <= BATTERY_POWER_THRESHOLD_WARNING.floatValue();
//      是否满电：true满电（100%）
        boolean fullPower = batteryEletricRemain == BATTERY_POWER_EXPRESSION.floatValue();
//      是否急停
        boolean stopping = rosWebService.isStopping();
//      是否定位丢失
        boolean getLost = !iRobotStatusService.getPositionStatus();
//      是否处于充电中
        boolean charing = iRobotStatusService.isDock();
//      是否处于空闲
        boolean isIdle = rosWebService.isIdle();
//      当处于急停,只发送急停表情
        if (stopping) {
            publishUtil(ExpressionType.EMERGENCY_STOP.getValue(), t);
        } else {
            if (lowBattery) {
                //当处于低电量和充电中时,只发送充电;
                if (charing) {
                    publishUtil(ExpressionType.CHARGING_ING.getValue(), t);
                    return;
                }
                //当处于低电量，且不在返回充电时，发送低电
                if (!gotoCharing) {
                    publishUtil(ExpressionType.LOW_BATTERY.getValue(), t);
                    return;
                }
            }
            if (working) {
                // 当处于寻找充电桩和工作中时，只发送寻找充电桩
                if (gotoCharing) {
                    publishUtil(ExpressionType.FIND_CHARGING_PILE.getValue(), t);
                    return;
                }
                // 当处于工作中和满电时，只发送工作中；
                if (fullPower) {
                    publishUtil(ExpressionType.WORKING.getValue(), t);
                    return;
                }
            }
            if (isIdle) {
                // 当处于迷路和空闲(微笑)时，发送迷路；
                if (getLost) {
                    publishUtil(ExpressionType.LOST_WAY.getValue(), t);
                    return;
                }
                // 当处于充电和空闲(微笑)时，发送充电；
                if (charing) {
                    //如果是满电，则发送满电
                    if (fullPower) {
                        publishUtil(ExpressionType.FULL_BATTERY.getValue(), t);
                        return;
                    }
                    publishUtil(ExpressionType.CHARGING_ING.getValue(), t);
                    return;
                }
                // 如果是寻找充电桩
                if (gotoCharing) {
                    publishUtil(ExpressionType.FIND_CHARGING_PILE.getValue(), t);
                    return;
                }
            }
            // 防止弹出充电桩后，电池检测仍在充电，发布充电中的表情；发布工作中；
            if (charing && ControlStatusConstants.EXPRESSION_PRIORITY.working) {
                publishUtil(ExpressionType.WORKING.getValue(), t);
                return;
            }
            _Uint8 uint8 = JSONObject.parseObject(content, _Uint8.class);
            publishUtil(Integer.valueOf(uint8.data), t);
        }
    }

    /**
     * 点阵表情话题发布工具类
     *
     * @param date
     */
    private void publishUtil(Integer date, Topic t) {
        String s = String.valueOf(date);
        _Uint8 uint8 = new _Uint8();
        uint8.data = Short.valueOf(s);
        String message = JSONObject.toJSONString(uint8);
        Message msg = new Message(message);
        if (date.equals(LAST_EXPRESSION_SEND)) {
            return;
        }
        t.publish(msg);
        log.info("点阵表情：expression-date:" + date);
        LAST_EXPRESSION_SEND = date;
    }

    /**
     * 保存topic数据到本地
     *
     * @param topic 话题名称
     * @param msg   话题内容
     */
    private void saveTopicData(String topic, String msg) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            jedis.set(RosWebConstants.TOPIC + "::" + topic, msg);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * msg subscribe
     *
     * @param topic   话题名称
     * @param msgType 话题类型
     */
    public void subscribe(String topic, String msgType) {
        subscribe(topic, msgType, null);
    }

    public void subscribe(String topic, String msgType, Integer expireSec) {
        try {
            redisTemplate.opsForValue().set(RosWebConstants.TOPIC + "::" + topic, "");
            if (!rosBridge.hasConnected()) {
                log.error("rosbridge尚未连接,无法订阅话题:" + topic);
                return;
            }
            rosBridge.subscribe(SubscriptionRequestMsg.generate(topic).setType(msgType).setThrottleRate(0).setQueueLength(100), new RosListenDelegate() {
                @Override
                public void receive(JsonNode data, String stringRep) {
                    if (null != data) {
                        try {
                            JsonNode msgNode = data.get("msg");
                            String msg = msgNode == null ? "" : msgNode.toString();
                            //保存话题数据到本地
                            saveTopicData(topic, msg);
                            if (ObjectUtil.isNotNull(expireSec)) {
                                //设置键失效
                                RedisUtil.setTopicExpire(topic, expireSec);
                            }
                            //同步话题数据到服务端
//                                    syncTopicData(topic,msg);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("订阅topic:" + topic + "出现异常");
            RedisConnectionUtils.unbindConnection(redisTemplate.getConnectionFactory());
        }
    }

    /**
     * 语音对话专用话题订阅
     *
     * @param topic   话题名称
     * @param msgType 话题类型
     */
    public void subscribeTalkTopic(String topic, String msgType) {
        try {
            if (!rosBridge.hasConnected()) {
                log.error("rosbridge尚未连接,无法订阅话题:" + topic);
                return;
            }
            rosBridge.subscribe(SubscriptionRequestMsg.generate(topic).setType(msgType).setThrottleRate(0).setQueueLength(100), new RosListenDelegate() {
                @Override
                public void receive(JsonNode data, String stringRep) {
                    if (null != data) {
                        try {
                            //语音交流状态;在发送语音时会判断是否处于语音交流状态；如果处于此状态则停止其他语音播报
                            RedisUtil.setKeyExpire(TALK_STATUS_CTRL, 60);
                            JsonNode msgNode = data.get("msg");
                            String msg = msgNode == null ? "" : msgNode.toString();
                            log.info("发送对话语音：msg============" + msg);
                            _Talk talk = JSONObject.parseObject(msg, _Talk.class);
                            TalkScene enumObjByKey = EnumUtils.getEnumObjByKey(talk.data);
                            //获取当前任务
                            Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_TASK_ID);
                            //如果当前没有任务，并说出了这些指令不发送语音，也不执行对应操作
                            if (o == null && (16 == talk.data || 97 == talk.data || 98 == talk.data || 100 == talk.data)) {
                                return;
                            }
                            //如果处于迷路状态则不发送以下语音和操作；并发送迷路语音
                            boolean isDisturbed = !iRobotStatusService.getPositionStatus();
                            if (isDisturbed && (14 == talk.data || 15 == talk.data || 16 == talk.data || 97 == talk.data || 98 == talk.data || 100 == talk.data)) {
                                rosWebService.sendVoicePrompt(SceneType.DEVIATION_ROUTE, null);
                                return;
                            }
                            //原版发送语音的重载方法
                            rosWebService.sendVoicePrompt(enumObjByKey, null);
                            DisinfectTaskVo disinfectTask = null;
                            if (o != null) {
//                                      关于任务的业务判断放在此中，防止在无任务情况下说出任务相关指令报空指针；
                                disinfectTask = rosWebService.getDisinfectTask(o.toString());
                                //任务暂停：需要获取当前任务id和操作数
                                if (97 == talk.data || 16 == talk.data) {
                                    log.info("语音控制任务暂停");
                                    rosWebService.taskControl1(disinfectTask.getId(), "2");
                                }
                                //任务继续：需要获取当前任务id和操作数
                                if (98 == talk.data) {
                                    log.info("语音控制任务继续");
                                    rosWebService.taskControl1(disinfectTask.getId(), "3");
                                }
                                //任务取消：需要获取当前任务id和操作数
                                if (100 == talk.data) {
                                    log.info("语音控制任务取消");
                                    rosWebService.taskControl1(disinfectTask.getId(), "2");
                                    rosWebService.taskControl1(disinfectTask.getId(), "4");
                                }
                            }
                            //执行开始消毒操作：调用默认消毒按钮的接口
                            if (14 == talk.data) {
                                log.info("语音控制执行默认消毒任务");
                                String defaultTaskId = iRobotTaskService.getDefaultTaskId();
                                if (StrUtil.isBlank(defaultTaskId)) {
                                    rosWebService.sendVoicePrompt(SceneType.NOT_DEFAULT_TASK_LINE, null);
                                }
                                rosWebService.startDisinfectService(defaultTaskId);
                            }
                            //返回充电
                            if (15 == talk.data) {
                                log.info("语音控制返回充电");
                                //防止任务被取消时报空指针
                                if (ObjectUtil.isNotEmpty(disinfectTask)) {
                                    rosWebService.taskControl1(disinfectTask.getId(), "2");
                                    rosWebService.taskControl1(disinfectTask.getId(), "4");
                                }
                                //返回充电方法中包含语音发送，此处不加语音；
                                rosWebService.gotoCharging();
                                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CHARGING_CONDITION, ChargingCondition.AUTO_CHARGING_MANUAL.getType().toString());
                            }
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("订阅topic:" + topic + "出现异常");
        }
    }

    /**
     * 订阅点阵表情话题专用----仅用于测试，无实际用途；
     *
     * @param topic   话题名称
     * @param msgType 话题类型
     */
    public void subscribeExpressionTopic(String topic, String msgType) {
        try {
            if (!rosBridge.hasConnected()) {
                log.error("rosbridge尚未连接,无法订阅话题:" + topic);
                return;
            }
            rosBridge.subscribe(SubscriptionRequestMsg.generate(topic).setType(msgType).setThrottleRate(0).setQueueLength(100), new RosListenDelegate() {
                @Override
                public void receive(JsonNode data, String stringRep) {
                    if (null != data) {
                        try {
//                                    log.info("点阵表情话题订阅data=="+data.toString());
                            JsonNode msgNode = data.get("msg");
                            String msg = msgNode == null ? "" : msgNode.toString();
                            _Uint8 expression = JSONObject.parseObject(msg, _Uint8.class);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    }
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
            log.error("订阅topic:" + topic + "出现异常");
        }
    }

    /**
     * cancel subscribe
     *
     * @param topic 话题
     */
    public void unsubscribe(String topic) {
        rosBridge.unsubscribe(topic);
    }

    /**
     * service call 服务调用
     *
     * @param service     服务名称
     * @param serviceType 服务类型
     */
    public synchronized String callService(String service, String serviceType, String serviceReq) {
        if (!ros.isConnected()) {
            ros.connect();
        }
        Service s = new Service(ros, service, serviceType);
        ServiceRequest request = new ServiceRequest(serviceReq);
        SynchronousQueue<ServiceResponse> responses = new SynchronousQueue<>();
        Mono.fromCallable(() -> s.callServiceAndWait(request)).timeout(Duration.ofMillis(150000)) // 设置超时
                .subscribeOn(Schedulers.elastic()) // 在弹性线程池执行
                .subscribe(response -> {
                    try {
                        responses.put(response);
                    } catch (InterruptedException e) {
                        log.error(e.getMessage());
                    }
                    log.info("{}服务,请求参数为{},响应值为{},调用成功", service, serviceReq, response);
                }, error -> {
                    if (error instanceof TimeoutException) {
                        log.error("{}服务,请求参数为{},调用超时", service, serviceReq);
                        boolean b = rosBridge.hasConnected();
                        log.info("连接状态：{}",b);
                        init();
                    } else {
                        log.error("{}服务,请求参数为{},调用失败: {}", service, serviceReq, error.getMessage());
                    }
                    ros.disconnect();
                });
        ServiceResponse serviceResponse = null;
        try {
            serviceResponse = responses.poll(151,TimeUnit.SECONDS);
            responses.clear();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        return serviceResponse == null ? "" : serviceResponse.toString();
    }


    /**
     * service call 服务调用
     * 此服务调用专门针对因ROS版本过低导致服务没有响应的问题
     *
     * @param service     服务名称
     * @param serviceType 服务类型
     */
    public synchronized String callServiceForNoResponse(String service, String serviceType, String serviceReq) {
        if (!ros.isConnected()) {
            ros.connect();
        }
        Service s = new Service(ros, service, serviceType);
        ServiceRequest request = new ServiceRequest(serviceReq);
        log.info("{}服务,请求参数为{},无响应服务被调用！", service, serviceReq);
        executorService.submit(new Callable<ServiceResponse>() {
            @Override
            public ServiceResponse call() {
                try {
                    s.callService(request, new ServiceCallback() {
                        @Override
                        public void handleServiceResponse(ServiceResponse serviceResponse) {
                            log.info("服务调用");
                        }
                    });
//                    ServiceResponse serviceResponse = s.callServiceAndWait(request);
                    log.info("服务调用之后！！！");
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return null;
            }
        });
        return null;
    }
}
