package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_route_item")
@ApiModel(value = "机器人路线条目")
public class RobotRouteItem extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "路径id")
    private String routeId;

    @ApiModelProperty(value = "面积")
    private double area;

    @ApiModelProperty(value = "高度")
    private double height;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}