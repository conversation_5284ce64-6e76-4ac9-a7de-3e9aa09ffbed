package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotDeviceInfo;

import java.util.List;

/**
 * 设备信息接口
 *
 * <AUTHOR>
 */
public interface IRobotDeviceInfoService extends IService<RobotDeviceInfo> {

    /**
     * 更新设备信息
     *
     * @param robotDeviceInfo 设备信息
     * @return true:成功，false:失败
     */
    boolean updateDeviceInfo(RobotDeviceInfo robotDeviceInfo);

    /**
     * 保存设备信息
     *
     * @param robotDeviceInfo 设备信息
     * @return true:成功，false:失败
     */
    boolean saveDeviceInfo(RobotDeviceInfo robotDeviceInfo);

    /**
     * 删除设备信息
     *
     * @param id 设备ID
     * @return true:成功,false:失败
     */
    boolean deleteDevice(String id);


    /**
     * 根据类型和子类型获取设备信息
     * @param type
     * @param subType
     * @return
     */
    Result<List<RobotDeviceInfo>> getRobotDeviceInfoByTypeAndSubType(Integer type, Integer subType);


    /**
     * 根据点位类型获取设备信息
     * @param type
     * @return
     */
    Result<List<RobotDeviceInfo>> getRobotDeviceInfoByPositionType(Integer type);
}