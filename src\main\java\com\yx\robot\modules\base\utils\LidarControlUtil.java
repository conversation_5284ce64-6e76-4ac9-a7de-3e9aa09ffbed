package com.yx.robot.modules.base.utils;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.constant.ControlStatusConstants;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.enums.TaskType;
import com.yx.robot.common.utils.DateUtils;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.IRobotTaskService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.*;

/**
 * <AUTHOR>
 * @date 2022/9/15
 * description：
 */
@Slf4j
@Component("LidarControlUtil")
public class LidarControlUtil {

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    /**
     * 系统自检话题休眠字段
     */
    public static String SLEEP = "sleep";

    /**
     * 系统自检话题正常字段
     */
    public static String S00000 = "S00000";

    /**
     * 控制程序执行时间为30秒，方便前端倒计时
     */
    private static Integer TIME_CTRL = 21;

    /**
     * 脱离充电桩之后，不进行休眠
     */
    public static Boolean END_CHARING = false;

    /**
     * handleLidar方法执行次数
     */
    public static Integer HANDLER_COUNT = 0;

    /**
     * 雷达服务调用：开启和关闭；---用于雷达休眠
     * 雷达开闭时间间隔至少三十秒
     * true :  开启；
     * false :  关闭；
     * 注意：不能使用redis中scan话题中是否有数据来判断雷达是否开启和关闭，
     * 因为关闭雷达后，redis中的雷达数据不会清空，只能通过系统自检话题来判断，
     * 或者额外再写一个话题订阅方法订阅scan话题不存储到redis；
     * 此方法在两个开始消毒的方法中都添加了
     *
     * @return
     */
    public synchronized boolean handleLidar(boolean operation) throws InterruptedException {
        log.info("handleLidar方法执行次数：" + HANDLER_COUNT);
        HANDLER_COUNT++;
        boolean flag;
        boolean sleepCtrl = true;
        Integer sleepCount = 0;
        if (operation) {
            //开启底层板(关闭低功耗)
            _Uint8 uint8 = new _Uint8();
            uint8.data = 0;
            rosBridgeService.publish(LOW_POWER_CTRL, Message.getMessageType(_Uint8.class), JSONObject.toJSONString(uint8));
            //开启雷达
            serviceCall(ServiceConstants.START_LIDAR, true);
            Thread.sleep(6 * 1000);
            //开启深度相机
            serviceCall(ServiceConstants.DEPTH_CAMERA_SLEEP, true);
            Thread.sleep(3 * 1000);
            //向雷达休眠和深度休眠话题里发false,提醒ROS结束休眠
            log.info("第1次发布开启深度话题");
            log.info("第1次发布开启雷达话题");
            //这个方法发布false时对其中一个话题不生效，很奇怪；
            notifyRosSleep(false);
            while (sleepCtrl) {
                Thread.sleep(1 * 1000);
                sleepCount++;
                if (sleepCount == 1 && SLEEP.equals(returnDepthStatue())) {
                    log.info("第2次发布开启深度话题");
                    topicPublish(false, DEPTH_CAMERA_SLEEP_TOPIC);
                }
                if (sleepCount == 1 && SLEEP.equals(returnLiadrStatue())) {
                    log.info("第2次发布开启雷达话题");
                    topicPublish(false, LIDAR_SLEEP_TOPIC);
                }
                log.info("开启后returnLiadrStatue:" + returnLiadrStatue());
                log.info("开启后returnDepthStatue:" + returnDepthStatue());
                log.info("开启后returnUnderlyingStatue:" + returnUnderlyingStatue());
                if (S00000.equals(returnLiadrStatue()) && S00000.equals(returnDepthStatue()) && !returnUnderlyingStatue()) {
                    log.info("开启用时：" + sleepCount + "秒");
                    if (sleepCount < TIME_CTRL) {
                        log.info("开启用时小于30秒，延缓到30秒结束程序，防止频繁开关");
                        Thread.sleep((TIME_CTRL - sleepCount) * 1000);
                    }
                    sleepCtrl = false;
                }
                if (sleepCount == 10 && SLEEP.equals(returnDepthStatue())) {
                    log.info("第3次发布开启深度话题并再次调用服务");
//                    serviceCall(ServiceConstants.DEPTH_CAMERA_SLEEP, true);
                    topicPublish(false, DEPTH_CAMERA_SLEEP_TOPIC);
                }
                if (sleepCount == 10 && SLEEP.equals(returnLiadrStatue())) {
                    log.info("第3次发布开启雷达话题并再次调用服务");
//                    serviceCall(ServiceConstants.START_LIDAR, true);
                    topicPublish(false, LIDAR_SLEEP_TOPIC);
                }
                // 如果30秒还未开启则跳出循环
                if (sleepCount == 20) {
                    log.info("开启循环跳出！");
                    sleepCtrl = false;
                }
            }
            if (S00000.equals(returnLiadrStatue()) && S00000.equals(returnDepthStatue())) {
                log.info("-----------雷达和深度开启成功！");
                ControlStatusConstants.LIDAR_DORMANCY_STATUE = false;
                flag = true;
            } else {
                log.info("-----------雷达和深度开启失败！");
                flag = false;
            }
        } else {
            //向雷达和深度休眠话题里发true,提醒ROS开始休眠
            log.info("准备进入休眠模式！！！！！！！");
            _Bool bool = new _Bool();
            bool.data = true;
            String s = JSONObject.toJSONString(bool);
            rosBridgeService.publish(DEPTH_CAMERA_SLEEP_TOPIC, Message.getMessageType(_Bool.class), s);
            rosBridgeService.publish(LIDAR_SLEEP_TOPIC, Message.getMessageType(_Bool.class), s);
            //关闭雷达
            rosBridgeService.callServiceForNoResponse(ServiceConstants.STOP_LIDAR, "", "{}");
            //关闭深度相机
            _DepthCameraSleepReq sleepReq = new _DepthCameraSleepReq();
            sleepReq.enable = false;
            rosBridgeService.callServiceForNoResponse(ServiceConstants.DEPTH_CAMERA_SLEEP, Message.getMessageType(_DepthCameraSleepReq.class), JSONObject.toJSONString(sleepReq));
            //关闭底层版，开启底层版低功耗
            _Uint8 uint8 = new _Uint8();
            uint8.data = 1;
            rosBridgeService.publish(LOW_POWER_CTRL, Message.getMessageType(_Uint8.class), JSONObject.toJSONString(uint8));
            while (sleepCtrl) {
                if (sleepCount == 5) {
                    rosBridgeService.publish(LOW_POWER_CTRL, Message.getMessageType(_Uint8.class), JSONObject.toJSONString(uint8));
                }
                Thread.sleep(1 * 1000);
                sleepCount++;
                log.info("关闭后returnLiadrStatue:" + returnLiadrStatue());
                log.info("关闭后returnDepthStatue:" + returnDepthStatue());
                log.info("关闭后returnUnderlyingStatue:" + returnUnderlyingStatue());
                //休眠速度要快，不需要阻塞
                if (SLEEP.equals(returnLiadrStatue()) && SLEEP.equals(returnDepthStatue()) && returnUnderlyingStatue()) {
                    log.info("关闭用时：" + sleepCount + "秒");
                    sleepCtrl = false;
                }
                //如果30秒还没关闭，跳出循环
                if (sleepCount == 30) {
                    log.info("关闭循环跳出！");
                    sleepCtrl = false;
                }
            }
            if (SLEEP.equals(returnLiadrStatue()) && SLEEP.equals(returnDepthStatue()) && returnUnderlyingStatue()) {
                log.info("-----------雷达和深度休眠成功！");
                ControlStatusConstants.LIDAR_DORMANCY_STATUE = true;
                flag = true;
                log.info("已经进入休眠模式！！！！！！！");
//                SLEEPING = false;
            } else {
                log.info("-----------雷达和深度休眠失败！");
                flag = false;
            }
        }
        return flag;
    }

    /**
     * 获取系统自检话题中雷达状态
     *
     * @return
     */
    public String returnLiadrStatue() {
        String systemCheckStr = redisTemplate.opsForValue().get(TOPIC + "::" + SYSTEM_CHECK);
        _SystemCheck systemCheck = JSONObject.parseObject(systemCheckStr, _SystemCheck.class);
        return systemCheck.lidar;
    }

    /**
     * 获取系统自检话题中深度状态
     *
     * @return
     */
    public String returnDepthStatue() {
        String systemCheckStr = redisTemplate.opsForValue().get(TOPIC + "::" + SYSTEM_CHECK);
        _SystemCheck systemCheck = JSONObject.parseObject(systemCheckStr, _SystemCheck.class);
        return systemCheck.depth_camera;
    }

    /**
     * 获取底层版低功耗状态
     * True: 底层板处于低功耗状态
     * False：底层板处于正常状态
     *
     * @return
     */
    public Boolean returnUnderlyingStatue() {
        String underlying = redisTemplate.opsForValue().get(TOPIC + "::" + LOW_POWER_STATE);
        _Bool bool = new _Bool();
        bool.data = false;
        if (StrUtil.isNotBlank(underlying)) {
            bool = JSONObject.parseObject(underlying, _Bool.class);
        }
        return bool.data;
    }

    /**
     * 通知ROS开始或结束休眠
     *
     * @param contrl true: 开始休眠，false: 结束休眠
     *               此处有一个问题：当在一个方法里发布两个话题（false）时，会导致其中一个话题发布了但没效果，如果不在一个方法里分开发布就可以；
     *               但是分开发布两个话题（true）时，确没有影响；
     * @throws InterruptedException
     */
    public void notifyRosSleep(boolean contrl) throws InterruptedException {
        _Bool bool = new _Bool();
        bool.data = contrl;
        String s = JSONObject.toJSONString(bool);
        rosBridgeService.publish(DEPTH_CAMERA_SLEEP_TOPIC, Message.getMessageType(_Bool.class), s);
        Thread.sleep(3000);
        rosBridgeService.publish(LIDAR_SLEEP_TOPIC, Message.getMessageType(_Bool.class), s);
    }

    /**
     * 话题发布
     *
     * @param operation
     * @param topic
     */
    public void topicPublish(boolean operation, String topic) {
        _Bool bool = new _Bool();
        bool.data = operation;
        rosBridgeService.publish(topic, Message.getMessageType(_Bool.class), JSONObject.toJSONString(bool));
    }

    /**
     * 服务调用
     *
     * @param service
     * @param operation
     */
    public void serviceCall(String service, boolean operation) {
        if (service.equals(ServiceConstants.DEPTH_CAMERA_SLEEP)) {
            _DepthCameraSleepReq sleepReq = new _DepthCameraSleepReq();
            sleepReq.enable = operation;
            String s = rosBridgeService.callServiceForNoResponse(service, Message.getMessageType(_DepthCameraSleepReq.class), JSONObject.toJSONString(sleepReq));
        } else {
            String s = rosBridgeService.callServiceForNoResponse(service, "", "{}");
        }
    }

    /**
     * 判断time之内是否有定时任务：如果有返回true,没有则返回false
     * time 为分钟
     *
     * @return
     * @throws ParseException
     */
    public boolean checkFixTimeTask(Integer time) throws ParseException {
//      获取定时任务
        Object currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
        List<RobotTask> robotTaskList = iRobotTaskService.list(new LambdaQueryWrapper<RobotTask>()
                .eq(RobotTask::getType, TaskType.DISINFECT.getType())
                .eq(RobotTask::getMapId, currentMap.toString())
                .eq(RobotTask::getIsFixedTime, 1));
        if (CollectionUtil.isEmpty(robotTaskList)) {
            return false;
        }
        // 每次检查只获取一个定时任务
        ArrayList<Boolean> arrayList = new ArrayList<>();
        for (RobotTask robotTask : robotTaskList) {
            String startTime = robotTask.getStartTime();
            String weekdays = robotTask.getWeekdays();
            if (StrUtil.isNotEmpty(startTime)) {
                boolean result = judgmentTime(time, startTime, weekdays);
                if (result) {
                    arrayList.add(result);
                }
            }
        }
        if (CollectionUtil.isNotEmpty(arrayList)) {
            return true;
        }
        return false;
    }

    /**
     * 判断定时任务是否处于time时间之内
     *
     * @param time      分钟
     * @param startTime
     * @param weekdays
     * @return
     * @throws ParseException
     */
    public boolean judgmentTime(Integer time, String startTime, String weekdays) throws ParseException {
        Calendar currentCalendar = DateUtils.getCalendar();
        int currentHour = currentCalendar.get(Calendar.HOUR_OF_DAY);
        int currentMinute = currentCalendar.get(Calendar.MINUTE);
        Calendar startTimeCalendar = DateUtils.parseCalendar(startTime, "HH:mm");
        int startHour = startTimeCalendar.get(Calendar.HOUR_OF_DAY);
        int startMinute = startTimeCalendar.get(Calendar.MINUTE);
        int differHour = startHour - currentHour;
        int differMinute = startMinute - currentMinute;
        int differTime = differHour * 60 + differMinute;
        boolean weekDayOfCheck = weekDaysCheck(weekdays, currentCalendar.get(Calendar.DAY_OF_WEEK));
        // 如果处于夜晚23点以后，不进行休眠，防止因当天无定时任务导致时间内判断定时任务异常；
        if (currentHour == 23) {
            return true;
        }
        // 如果在指定的时间和星期之内
        if (weekDayOfCheck) {
            //隔小时
            boolean flag1 = differHour > 0;
            //同一个小时
            boolean flag2 = differHour == 0 && differMinute > 0;
            //隔天（如果weekDayOfCheck为true，则不存在隔天的情况）
            boolean flag3 = differHour < 0;
            if (flag1) {
                differTime = differHour * 60 + differMinute;
            }
            if (flag2) {
                differTime = differMinute;
            }
            if (flag3) {
                differTime = (24 + differHour) * 60 + differMinute;
            }
            // 包括当前分钟
            if (differTime > 0 && differTime <= time) {
                return true;
            } else {
                return false;
            }
        }
        return false;
    }

    /**
     * 是否在指定的星期内
     *
     * @param weekdays
     * @param currentDayOfWeek
     * @return
     */
    public boolean weekDaysCheck(String weekdays, int currentDayOfWeek) {
        boolean res = false;
        if (StrUtil.isNotEmpty(weekdays)) {
            //星期天
            if (currentDayOfWeek == 1) {
                res = weekdays.contains("7");
            } else {
                res = weekdays.contains((currentDayOfWeek - 1) + "");
            }
        }
        return res;
    }

    /**
     * 打开雷达控制接口
     *
     * @return
     * @throws InterruptedException
     */
    public Result<Boolean> openLidar() throws InterruptedException {
        Result<Boolean> result = new Result<>();
        result.setSuccess(true);
        result.setMessage("打开雷达接口被调用");
        log.info("打开雷达接口被调用！");
        Integer openCount = 0;
        Boolean flag = true;
        serviceCall(ServiceConstants.START_LIDAR, true);
        while (flag) {
            Thread.sleep(1000);
            openCount++;
            log.info("开启后returnLiadrStatue:" + returnLiadrStatue());
            if (S00000.equals(returnLiadrStatue())) {
                flag = false;
                result.setMessage("雷达开启成功！");
            }
            if (openCount == 30) {
                flag = false;
                result.setMessage("雷达开启失败！");
            }
        }
        return result;
    }

}
