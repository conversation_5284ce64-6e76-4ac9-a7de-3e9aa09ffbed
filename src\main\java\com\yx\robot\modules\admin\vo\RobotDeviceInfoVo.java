package com.yx.robot.modules.admin.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020-1-6
 */
@Data
public class RobotDeviceInfoVo{

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 设备主机号
     */
    private String deviceHost;

    /**
     * 当前地图
     */
    private String currentMap;

    /**
     * 设备状态
     */
    private Integer deviceStatus;

    /**
     * 运行信息
     */
    private String workingInfo;

    /**
     * 电量
     */
    private Double battery;

    /**
     * 行驶里程（单位km）
     */
    private Double mileage;

    /**
     * 运行时间（单位h）
     */
    private Double runTime;

    /**
     * 音量大小
     */
    private Integer volumeSize;

    /**
     * 雾化喷雾告警 0：为否 1：为是
     */
    private Integer sprayWarning;

    /**
     * 急停开关 0：为否 1：为是
     */
    private Integer stop;

    /**
     * 防撞条 0：为否 1：为是
     */
    private Integer collision;

    /**
     * 空闲状态 0：为否 1：为是
     */
    private Integer idle;

    /**
     * 建图状态 0：为否 1：为是
     */
    private Integer mapping;

    /**
     * 充电状态 0：为否 1：为是
     */
    private Integer charging;

    /**
     * 设备保养时间(单位：年)
     */
    private Long deviceMaintenanceTime;

    /**
     * 设备保养日期
     */
    private Date deviceMaintenanceDate;

    /**
     * 脉冲灯保养时间（单位：小时）
     */
    private Long ulrayMaintenanceTime;

    /**
     * 脉冲灯使用时长(单位：秒)
     */
    private Long ulrayUsageTime;

    /**
     * 脉冲灯保养日期
     */
    private Date ulrayMaintenanceDate;

    /**
     * 更新时间
     */
    private Date updateTime;

}
