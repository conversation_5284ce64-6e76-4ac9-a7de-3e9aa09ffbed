package com.yx.robot.modules.admin.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2022/5/25
 * description：机器人版本
 */
@Data
@ApiModel("版本信息")
public class YxRobotVersionDto {

    /**
     * id
     */
    @ApiModelProperty(value = "id")
    private String id;

    /**
     * name
     */
    @ApiModelProperty(value = "name")
    private String name;

    /**
     * versionCode
     */
    @ApiModelProperty(value = "versionCode")
    private String versionCode;

    /**
     * 当前版本
     */
    @ApiModelProperty(value = "当前版本")
    private String currentVersionCode;

    /**
     * type
     */
    @ApiModelProperty(value = "type")
    private Integer type;

    /**
     * updateContent
     */
    @ApiModelProperty(value = "updateContent")
    private String updateContent;

    /**
     * content
     */
    @ApiModelProperty(value = "content")
    private String content;

    /**
     * publishDate
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "publishDate")
    private Date publishDate;

    /**
     * filePath
     */
    @ApiModelProperty(value = "filePath")
    private String filePath;

    /**
     * createName
     */
    @ApiModelProperty(value = "createName")
    private String createName;
    /**
     * createBy
     */
    @ApiModelProperty(value = "createBy")
    private String createBy;
    /**
     * createTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "createTime")
    private Date createTime;
    /**
     * updateName
     */
    @ApiModelProperty(value = "updateName")
    private String updateName;
    /**
     * updateBy
     */
    @ApiModelProperty(value = "updateBy")
    private String updateBy;
    /**
     * updateTime
     */
    @JsonFormat(timezone = "GMT+8", pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ApiModelProperty(value = "updateTime")
    private Date updateTime;
}
