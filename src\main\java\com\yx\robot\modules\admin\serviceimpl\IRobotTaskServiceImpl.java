package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUnit;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.enums.RobotType;
import com.yx.robot.common.enums.SceneType;
import com.yx.robot.common.enums.TaskOperationType;
import com.yx.robot.common.enums.TaskType;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.utils.SecurityUtil;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dao.mapper.RobotTaskMapper;
import com.yx.robot.modules.admin.dto.RobotTaskItemDto;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.RobotTaskVo;
import com.yx.robot.modules.base.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

import static com.yx.robot.common.constant.CommonConstant.STRING_CONNECTOR_CHARACTER;
import static com.yx.robot.common.constant.RobotRedisConstants.ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.enums.TaskType.DISINFECT;

/**
 * 机器人列表接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class IRobotTaskServiceImpl extends ServiceImpl<RobotTaskMapper, RobotTask> implements IRobotTaskService {

    @Autowired
    private IRobotTaskItemService iRobotTaskItemService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private SecurityUtil securityUtil;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotRouteItemService iRobotRouteItemService;

    @Autowired
    private RobotTaskMapper robotTaskMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Override
    public Result<Boolean> add(RobotTaskVo robotTaskVo) {
        RobotTask robotTask = new RobotTask();
        robotTask.setName(robotTaskVo.getName());
        robotTask.setType(robotTaskVo.getType());
        robotTask.setSubType(robotTaskVo.getSubType());
        User user = securityUtil.getCurrUser();
        if (null != user) {
            robotTask.setCreateBy(user.getUsername());
        }
        if (ObjectUtil.isNull(robotTaskVo.getLoops())) {
            robotTask.setLoops(robotTaskVo.getLoops());
        }
        robotTask.setCreateTime(new Date());
        robotTask.setSortOrder(robotTaskVo.getSortOrder());
        String mapId = robotTaskVo.getMapId() == null ? "" : robotTaskVo.getMapId();
        robotTask.setMapId(mapId);
        robotTask.setLevel(robotTaskVo.getLevel());
        robotTask.setExecutableStartTime(robotTaskVo.getExecutableStartTime());
        robotTask.setExecutableEndTime(robotTaskVo.getExecutableEndTime());
        String uuId = UUID.randomUUID().toString();
        robotTask.setUuId(uuId);
        //判断同一张地图下名称是否重复
        boolean isRepeat = isRepeat(robotTaskVo.getMapId(), robotTaskVo.getName());
        if (isRepeat) {
            log.warn("任务名称重复，无法添加");
            return new ResultUtil<Boolean>().setErrorMsg("任务名称重复，无法添加");
        }
        if (!save(robotTask)) {
            log.error("任务添加失败");
            return new ResultUtil<Boolean>().setErrorMsg("任务添加失败");
        }
        return new ResultUtil<Boolean>().setData(true, "任务添加成功");
    }

    @Override
    public boolean delete(String id) {
        try {
            RedisUtil.hdel(ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP, id);
            this.removeById(id);
            QueryWrapper<RobotTaskItem> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("task_id", id);
            iRobotTaskItemService.remove(queryWrapper);
            //删除点位绑定的路径相关信息
            List<RobotDisinfect> robotDisinfectList = iRobotDisinfectService.list(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, id));
            if (CollectionUtil.isNotEmpty(robotDisinfectList)) {
                for (RobotDisinfect robotDisinfect : robotDisinfectList) {
                    iRobotRouteItemService.remove(new LambdaQueryWrapper<RobotRouteItem>().eq(RobotRouteItem::getRouteId, robotDisinfect.getRobotLocationId()));
                }
            }
            //同时删除消毒点位
            iRobotDisinfectService.remove(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, id));
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

    }

    @Override
    public Result<Boolean> update(RobotTaskVo robotTaskVo) {
        RedisUtil.hdel(ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP, robotTaskVo.getId());
        String id = robotTaskVo.getId();
        RobotTask robotTask = this.getById(id);
        if (null != robotTask) {
            robotTask.setType(robotTaskVo.getType());
            robotTask.setSubType(robotTaskVo.getSubType());
            robotTask.setLevel(robotTaskVo.getLevel());
            robotTask.setMapId(robotTaskVo.getMapId());
            User user = securityUtil.getCurrUser();
            if (null != user) {
                robotTask.setUpdateBy(user.getUsername());
            }
            if (ObjectUtil.isNull(robotTaskVo.getLoops())) {
                robotTask.setLoops(robotTaskVo.getLoops());
            }
            robotTask.setUpdateTime(new Date());
            robotTask.setSortOrder(robotTaskVo.getSortOrder());
            robotTask.setExecutableStartTime(robotTaskVo.getExecutableStartTime());
            robotTask.setExecutableEndTime(robotTaskVo.getExecutableEndTime());
            //判断任务名称是否重复
            if (!robotTask.getName().equals(robotTaskVo.getName())) {
                boolean isRepeat = isRepeat(robotTaskVo.getMapId(), robotTaskVo.getName());
                if (isRepeat) {
                    log.warn("任务名称重复，无法更新");
                    return new ResultUtil<Boolean>().setErrorMsg("任务名称重复，无法更新");
                }
            }
            robotTask.setName(robotTaskVo.getName());
            if (!updateById(robotTask)) {
                log.error("更新任务信息失败");
                return new ResultUtil<Boolean>().setErrorMsg("更新任务信息失败");
            }
            return new ResultUtil<Boolean>().setData(true, "任务更新成功");
        }
        return new ResultUtil<Boolean>().setErrorMsg("任务不存在");
    }

    private boolean isRepeat(String mapId, String name) {
        QueryWrapper<RobotTask> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", name);
        queryWrapper.eq("map_id", mapId);
        List<RobotTask> robotTaskList = this.list(queryWrapper);
        return robotTaskList.size() > 0;
    }

    @Override
    public List<RobotTaskItemDto> taskDetail(String taskId) {
        List<RobotTaskItemDto> robotTaskItemDtoList = new ArrayList<>();
        QueryWrapper<RobotTaskItem> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("task_id", taskId);
        queryWrapper.orderByAsc("sort_order");
        List<RobotTaskItem> robotTaskItemList = iRobotTaskItemService.list(queryWrapper);
        if (null != robotTaskItemList && !robotTaskItemList.isEmpty()) {
            for (RobotTaskItem robotTaskItem : robotTaskItemList) {
                RobotTaskItemDto robotTaskItemDto = new RobotTaskItemDto();
                robotTaskItemDto.setId(robotTaskItem.getId());
                robotTaskItemDto.setCreateBy(robotTaskItem.getCreateBy());
                robotTaskItemDto.setCreateTime(robotTaskItem.getCreateTime());
                robotTaskItemDto.setUpdateBy(robotTaskItem.getUpdateBy());
                robotTaskItemDto.setUpdateTime(robotTaskItem.getUpdateTime());
                robotTaskItemDto.setSortOrder(robotTaskItem.getSortOrder());
                robotTaskItemDto.setTaskId(robotTaskItem.getTaskId());
                robotTaskItemDto.setNavMode(robotTaskItem.getNavMode());
                robotTaskItemDto.setCmd(robotTaskItem.getCmd());
                String positionId = robotTaskItem.getPositionId();
                robotTaskItemDto.setPositionId(positionId);
                RobotPosition robotPosition = iRobotPositionService.getById(positionId);
                if (null != robotPosition) {
                    robotTaskItemDto.setPositionName(robotPosition.getName());
                    robotTaskItemDto.setType(robotPosition.getType());
                }
                robotTaskItemDtoList.add(robotTaskItemDto);
            }
        }
        return robotTaskItemDtoList;
    }

    /**
     * 获取默认任务id
     *
     * @return 任务ID
     */
    @Override
    public String getDefaultTaskId() {
        String data = "";
        Object currentMapObj = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (currentMapObj != null && StrUtil.isNotBlank(currentMapObj.toString())) {
            List<RobotTask> robotTaskList = this.list(new LambdaQueryWrapper<RobotTask>()
                    .eq(RobotTask::getIsDefault, "0")
                    .eq(RobotTask::getMapId, currentMapObj.toString())
                    .eq(RobotTask::getType, DISINFECT.getType()));
            if (CollectionUtil.isNotEmpty(robotTaskList)) {
                data = robotTaskList.get(0).getId();
            }
        }
        boolean isEmpty = StringUtils.isBlank(data);

        if (isEmpty) {
            return null;
        }
        return data;
    }

    /**
     * 判断当前机器人是否有巡线任务
     *
     * @return true(有)/false(没有)
     */
    @Override
    public boolean isNeedLineTask() {
        boolean isNeedLineTask = (RobotType.U1.getType().equals(RobotBaseInfoConstant.type)
                || RobotType.X2.getType().equals(RobotBaseInfoConstant.type)
                || RobotType.U2.getType().equals(RobotBaseInfoConstant.type));
        return isNeedLineTask;
    }

    /**
     * 设置循环消毒次数
     *
     * @param counts 循环次数
     */
    @Override
    public void setDisinfectCounts(Integer counts) {
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, DISINFECT_COUNTS, counts.toString());
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (o != null && StrUtil.isNotBlank(o.toString())) {
            List<RobotTask> robotTasks = robotTaskMapper.selectList(new LambdaQueryWrapper<RobotTask>()
                    .eq(RobotTask::getMapId, o.toString())
                    .eq(RobotTask::getType, DISINFECT.getType()));
            if (CollectionUtil.isNotEmpty(robotTasks)) {
                for (RobotTask robotTask : robotTasks) {
                    robotTask.setLoops(counts);
                    robotTaskMapper.updateById(robotTask);
                }
            }
        }

    }

    /**
     * 当前任务是否为消毒任务
     * 当前任务为空时，false
     * 任务暂停时，false
     *
     * @return true:是消毒任务 / false:不是消毒任务
     */
    @Override
    public boolean isDisinfectCurrentTaskType() {
        String currentId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        if (StringUtils.isBlank(currentId)) {
//            log.info("当前无任务");
            return false;
        }
        if (!iRobotStatusService.checkTaskStatus()) {
            log.info("活物状态检测，任务暂停或者停止");
            return false;
        }
        RobotTask robotTask = this.getById(currentId);
        return ObjectUtil.isNotNull(robotTask) && robotTask.getType().equals(DISINFECT.getType());
    }

    /**
     * 获取当前任务类型
     * null 当前没有任务
     *
     * @return type
     */
    @Override
    public Integer getCurrentTaskType() {
        String currentId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        if (StringUtils.isBlank(currentId)) {
//            log.info("当前无任务");
            return null;
        }
        RobotTask robotTask = this.getById(currentId);
        if (ObjectUtil.isNull(robotTask)) {
            return null;
        }
        return robotTask.getType();
    }

    @Override
    public Integer getDisinfectCounts() {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, DISINFECT_COUNTS);
        if (o != null) {
            return Integer.valueOf(o.toString());
        } else {
            return 1;
        }
    }

    /**
     * 判断任务是否在可执行时间段内
     *
     * @param taskId 任务ID
     * @return true:可执行/false:不可执行
     */
    @Override
    public boolean isExecutableTime(String taskId) {
        RobotTask robotTask = getById(taskId);
        if (ObjectUtil.isNull(robotTask)) {
            log.warn("任务不存在:{}", taskId);
            return false;
        }
        return isExecutableTime(robotTask);
    }

    /**
     * 判断任务是否在可执行时间段内
     *
     * @param robotTask 任务信息
     * @return true:可执行/false:不可执行
     */
    @Override
    public boolean isExecutableTime(RobotTask robotTask) {
        if (ObjectUtil.isNull(robotTask)) {
            return false;
        }
        String periods = RedisUtil.getHash(ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP, robotTask.getId());

        if (ObjectUtil.isNull(periods)) {
            periods = catchExecutablePeriod(robotTask);
        }
        if (StringUtils.isBlank(periods)) {
            return true;
        }
        // 充电任务和返回充电装任务，所有时间段皆可执行
        if (TaskType.ORIGIN.getType().equals(robotTask.getType())
                || TaskType.CHARGING.getType().equals(robotTask.getType())) {
            return true;
        }
        return isCurrentTimeInPeriods(periods);
    }

    /**
     * 判断机器人当前任务是否可执行
     * 如果不可以执行，则取消任务，返回充电
     *
     * @param taskId 任务ID
     */
    @Override
    public void cancelNotInExecutableTimeTask(String taskId) {
        RobotTask task = this.getById(taskId);
        if (ObjectUtil.isNull(task)) {
            return;
        }
        if (!isExecutableTime(task)) {
            rosWebService.taskControl1(taskId, TaskOperationType.STOP.getOperation().toString());
            rosWebService.taskControl1(taskId, TaskOperationType.CANCEL.getOperation().toString());
            log.warn("任务不可以执行，执行返回充电任务：停止正在执行的巡线任务");
            if (!iRobotStatusService.isDock()) {
                rosWebService.gotoCharging();
            }
        }
    }

    private String catchExecutablePeriod(RobotTask robotTask) {
        String periods = null;

        if (StringUtils.isNotBlank(robotTask.getExecutableStartTime())) {
            periods = robotTask.getExecutableStartTime();
            if (StringUtils.isNotBlank(robotTask.getExecutableEndTime())) {
                periods = periods + STRING_CONNECTOR_CHARACTER + robotTask.getExecutableEndTime();
            }
        }
        RedisUtil.hset(ROBOT_TASK_EXECUTABLE_TIME_PERIODS_MAP, robotTask.getId(), StringUtils.isNotBlank(periods) ? periods : "");
        return periods;
    }

    /**
     * 当前时间是否在可执行时间范围内
     *
     * @param periods 可执行时间段 startTime&endTime
     * @return true:是，false:false
     */
    private boolean isCurrentTimeInPeriods(String periods) {
        boolean isCurrentDay = true;
        String startTime;
        String endTime;
        startTime = periods.split(STRING_CONNECTOR_CHARACTER)[0];
        if (periods.contains(STRING_CONNECTOR_CHARACTER)) {
            endTime = periods.split(STRING_CONNECTOR_CHARACTER)[1];
        } else {
            endTime = "23:59:59";
        }
        DateTime start = DateUtil.parseDateTime(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + " " + startTime);
        DateTime end = DateUtil.parseDateTime(DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + " " + endTime);
        // 如果开始时间在大于结束时间，则表明不是在同一天。
        if (start.after(end)) {
            isCurrentDay = false;
        }
        Date currentDateTime = new Date();
        if (isCurrentDay) {
            return currentDateTime.after(start) && currentDateTime.before(end);
        } else {
            return  currentDateTime.before(end) || currentDateTime.after(start);
        }
    }
}