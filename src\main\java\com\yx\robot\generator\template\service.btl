package ${entity.servicePackage};

import yxBaseService;
import ${entity.entityPackage}.${entity.className};
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import SearchVo;

import java.util.List;

/**
 * ${entity.description}接口
 * <AUTHOR>
 */
public interface ${entity.className}Service extends yxBaseService<${entity.className},${entity.primaryKeyType}> {

    /**
    * 多条件分页获取
    * @param ${entity.classNameLowerCase}
    * @param searchVo
    * @param pageable
    * @return
    */
    Page<${entity.className}> findByCondition(${entity.className} ${entity.classNameLowerCase}, SearchVo searchVo, Pageable pageable);
}