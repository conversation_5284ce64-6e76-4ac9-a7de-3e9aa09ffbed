package com.yx.robot.modules.base.dao;

import com.yx.robot.base.BaseDao;
import com.yx.robot.modules.base.entity.Role;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * 角色数据处理层
 * <AUTHOR>
 */
@Repository
public interface RoleDao extends BaseDao<Role,String> {

    /**
     * 获取默认角色
     * @param defaultRole
     * @return
     */
    List<Role> findByDefaultRole(Boolean defaultRole);
}
