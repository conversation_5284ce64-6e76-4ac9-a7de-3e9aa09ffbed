package ${entity.controllerPackage};

import yxBaseController;
import PageUtil;
import ResultUtil;
import PageVo;
import Result;
import SearchVo;
import ${entity.entityPackage}.${entity.className};
import ${entity.servicePackage}.${entity.className}Service;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "${entity.description}管理接口")
@RequestMapping("/yx/${entity.classNameLowerCase}")
@Transactional
public class ${entity.className}Controller extends yxBaseController<${entity.className}, ${entity.primaryKeyType}> {

    @Autowired
    private ${entity.className}Service ${entity.classNameLowerCase}Service;

    @Override
    public ${entity.className}Service getService() {
        return ${entity.classNameLowerCase}Service;
    }

    @RequestMapping(value = "/getByCondition", method = RequestMethod.GET)
    @ApiOperation(value = "多条件分页获取")
    public Result<Page<${entity.className}>> getByCondition(@ModelAttribute ${entity.className} ${entity.classNameLowerCase},
                                                            @ModelAttribute SearchVo searchVo,
                                                            @ModelAttribute PageVo pageVo){

        Page<${entity.className}> page = ${entity.classNameLowerCase}Service.findByCondition(${entity.classNameLowerCase}, searchVo, PageUtil.initPage(pageVo));
        return new ResultUtil<Page<${entity.className}>>().setData(page);
    }
}
