package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.dto.YxRobotWorldPositionDto;
import com.yx.robot.modules.admin.entity.RobotWorldPosition;
import com.yx.robot.modules.admin.message._PoseStamped;

/**
 * 机器人列表接口
 *
 * <AUTHOR>
 */
public interface IRobotWorldPositionService extends IService<RobotWorldPosition> {

    /**
     * 更新世界坐标系的值
     *
     * @param yxRobotWorldPositionDto
     * @return
     */
    boolean updateRobotWorldPosition(YxRobotWorldPositionDto yxRobotWorldPositionDto);

    /**
     * 通过世界坐标系获取 机器人位置信息
     *
     * @param robotWorldPosition 世界坐标系
     * @return 机器人位置信息
     */
    _PoseStamped getPoseStamped(RobotWorldPosition robotWorldPosition);

    /**
     * 通过停靠点ID获取机器人位置信息
     *
     * @param positionId 停靠点
     * @return 机器人位置信息
     */
    _PoseStamped getPoseStamped(String positionId);
}