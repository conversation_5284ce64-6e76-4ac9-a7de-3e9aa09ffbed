package com.yx.robot.modules.admin.vo;

import lombok.Data;

/**
 * 消毒点位信息
 * <AUTHOR>
 * @date 2020/4/7
 */
@Data
public class DisinfectPointVo {

    /**
     * 消毒点位id
     */
    private String id;

    /**
     * 位置区域id
     */
    private String robotLocationId;

    /**
     * 位置区域
     */
    private String locationInfo;

    /**
     * 位置编号
     */
    private String locationCode;

    /**
     * 紫外线
     */
    private boolean ulray = false;

    /**
     * 喷雾
     */
    private boolean spray = false;

    /**
     * 消毒模块
     */
    private boolean xt = false;

    /**
     * 消毒时间
     */
    private Integer disinfectTime;

    /**
     * 消毒后等待时间
     */
    private Integer waitTime;

    /**
     * 是否已消毒
     */
    private boolean finish;

}
