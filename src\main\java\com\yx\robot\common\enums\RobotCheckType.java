package com.yx.robot.common.enums;

/**
 * 机器人检测类型
 *
 * <AUTHOR>
 * @date 2020/10/20
 */
public enum RobotCheckType {

    /**
     * 1,"开机检测"
     */
    POWER_ON_CHECK(1, "开机检测"),

    /**
     * 2,"出厂检测"
     */
    FACTORY_CHECK(2, "出厂检测"),

    /**
     * 3,"自定义检测"
     */
    DEFINE_CHECK(3, "自定义检测");

    private final Integer type;

    private final String value;

    RobotCheckType(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return this.type;
    }

    public String getValue() {
        return this.value;
    }
}
