package com.yx.robot.listener;

import com.yx.robot.modules.admin.service.IRobotCheckHistoryRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextClosedEvent;
import org.springframework.context.event.ContextStoppedEvent;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import static com.yx.robot.common.constant.RosWebConstants.ROBOT_SYS_INFO;
import static com.yx.robot.common.constant.SecurityConstant.SYS_IS_UP;

@Component
@Slf4j
public class ApplicationEventListener implements ApplicationListener {

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IRobotCheckHistoryRecordService iRobotCheckHistoryRecordService;

    @Override
    public void onApplicationEvent(ApplicationEvent applicationEvent) {
        if(applicationEvent instanceof ApplicationReadyEvent) {
            log.info("应用已启动完成......");
            stringRedisTemplate.opsForHash().put(ROBOT_SYS_INFO,SYS_IS_UP,"true");
            iRobotCheckHistoryRecordService.powerOnCheck();
        } else if(applicationEvent instanceof ContextStoppedEvent) {
            log.error("应用停止......");
            stringRedisTemplate.opsForHash().put(ROBOT_SYS_INFO,SYS_IS_UP,"false");
        } else if(applicationEvent instanceof ContextClosedEvent) {
            log.error("应用已关闭......");
            stringRedisTemplate.opsForHash().put(ROBOT_SYS_INFO,SYS_IS_UP,"false");
        }
    }
}
