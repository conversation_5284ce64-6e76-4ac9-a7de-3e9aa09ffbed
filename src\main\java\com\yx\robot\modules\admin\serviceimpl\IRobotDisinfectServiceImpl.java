package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotDisinfectMapper;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._Pose;
import com.yx.robot.modules.admin.message._RouteDisinfectRep;
import com.yx.robot.modules.admin.message._RouteDisinfectReq;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.DisinfectPointVo;
import com.yx.robot.modules.admin.vo.PositionsVo;
import com.yx.robot.modules.admin.vo.RouteVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.ServiceConstants.DOCTOR_MODE;

/**
 * 机器人消毒任务条目接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotDisinfectServiceImpl extends ServiceImpl<RobotDisinfectMapper, RobotDisinfect> implements IRobotDisinfectService {

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotRouteService iRobotRouteService;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private IRobotLocationService iRobotLocationService;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    /**
     * 路径消毒
     *
     * @param id        机器人路径ID
     * @param operation (1: 开始任务 2: 暂停任务 3：继续任务 4：取消任务)
     * @return true:成功，失败
     */
    @Override
    public boolean routeDisinfectOperation(String id, Integer operation) {
        rosWebService.startFallBeforeTask();
        if (!operation.equals(TaskOperationType.START.getOperation())
                && !operation.equals(TaskOperationType.STOP.getOperation())
                && !operation.equals(TaskOperationType.CONTINUE.getOperation())
                && !operation.equals(TaskOperationType.CANCEL.getOperation())
        ) {
            log.warn("当前任务操作类型不存在");
            return false;
        }
        _RouteDisinfectReq routeDisinfectReq = new _RouteDisinfectReq();
        routeDisinfectReq.frame_id = UUID.randomUUID().toString();
        routeDisinfectReq.cmd = operation;
        String s = null;
        if (operation.equals(TaskOperationType.START.getOperation())) {
            // 延迟避障
//            Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, AVOID_TYPE);
//            if(o != null && Integer.valueOf(o.toString()).equals(RouteAvoidType.AUTO_AVOID.getType())) {
//                routeDisinfectReq.cmd = 12;
//            }else{
//                routeDisinfectReq.cmd = 11;
//            }
            RobotRoute robotRoute = iRobotRouteService.getById(id);
            if (robotRoute != null) {
                routeDisinfectReq.path_name = robotRoute.getLocationInfo().concat(robotRoute.getLocationCode());
                List<_Pose> poses = JSONObject.parseArray(robotRoute.getPositions(), _Pose.class);
                _Pose[] poseArr = ArrayUtil.toArray(poses, _Pose.class);
                routeDisinfectReq.pose_info = poseArr;
                s = rosBridgeService.callService(DOCTOR_MODE, Message.getMessageType(_RouteDisinfectReq.class),
                        JSON.toJSONString(routeDisinfectReq));
            }
        } else {
            routeDisinfectReq.path_name = "";
            routeDisinfectReq.pose_info = new _Pose[0];
            s = rosBridgeService.callService(DOCTOR_MODE, Message.getMessageType(_RouteDisinfectReq.class),
                    JSON.toJSONString(routeDisinfectReq));
        }
        if (StrUtil.isNotEmpty(s)) {
            _RouteDisinfectRep routeDisinfectRep = JSONObject.parseObject(s, _RouteDisinfectRep.class);
            if (routeDisinfectRep.ret == 1) {
                log.info("路径消毒服务调用成功");
//              点阵表情发布---工作中
                rosWebService.publishUtil(ExpressionType.WORKING.getValue());
                return true;
            } else {
                log.error("路径消毒服务调用失败");
            }
        }
        return false;
    }

    /**
     * 获取局部消毒详情
     *
     * @return list
     */
    @Override
    public List<DisinfectPointVo> getLocalDisinfectTaskInfo() {
        List<DisinfectPointVo> disinfectPointInfoList = new ArrayList<>();
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (o == null) {
            log.warn("无法信息");
            return disinfectPointInfoList;
        }
        // 如果没有任务先添加任务
        RobotTask robotTask = rosWebService.addRobotTask(DisinfectTaskType.LOCAL_TASK.getLabel() + TaskType.DISINFECT.getValue(), 1, TaskType.DISINFECT.getType(), DisinfectTaskType.LOCAL_TASK.getType());
        QueryWrapper queryWrapper = new QueryWrapper();
        queryWrapper.eq("map_id", o.toString());
        queryWrapper.eq("type", RobotPositionType.ROOM_POSITION.getType());
        queryWrapper.isNull("parent_id");
        List<RobotPosition> robotPositionList = iRobotPositionService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(robotPositionList)) {
            queryWrapper = new QueryWrapper();
            queryWrapper.in("position_id", robotPositionList.stream().map(RobotPosition::getId).collect(Collectors.toList()));
            queryWrapper.orderByAsc("sort_order");
            List<RobotLocation> robotLocationList = iRobotLocationService.list(queryWrapper);
            if (CollectionUtil.isNotEmpty(robotLocationList)) {
                for (RobotLocation robotLocation : robotLocationList) {
                    DisinfectPointVo disinfectPointVo = new DisinfectPointVo();
                    RobotDisinfect robotDisinfect = this.getOne(new LambdaQueryWrapper<RobotDisinfect>()
                            .eq(RobotDisinfect::getRobotLocationId, robotLocation.getId())
                            .eq(RobotDisinfect::getRobotTaskId, robotTask.getId()));
                    disinfectPointVo.setRobotLocationId(robotLocation.getId());
                    disinfectPointVo.setLocationInfo(robotLocation.getLocationInfo());
                    disinfectPointVo.setLocationCode(robotLocation.getLocationCode());
                    disinfectPointVo.setFinish(false);
                    if (robotDisinfect == null) {
                        disinfectPointVo.setDisinfectTime(60);
                        disinfectPointVo.setSpray(true);
                        disinfectPointVo.setUlray(true);
                        disinfectPointVo.setXt(true);
                        robotDisinfect = rosWebService.saveOrUpdateDisinfectPoint(disinfectPointVo, robotTask.getId());
                    } else {
                        disinfectPointVo.setDisinfectTime(robotDisinfect.getDisinfectTime());
                        disinfectPointVo.setSpray(robotDisinfect.getSpray().equals(SUCCESS));
                        disinfectPointVo.setUlray(robotDisinfect.getUlray().equals(SUCCESS));
                        disinfectPointVo.setXt(robotDisinfect.getXt().equals(SUCCESS));
                    }
                    disinfectPointVo.setId(robotDisinfect.getId());
                    disinfectPointInfoList.add(disinfectPointVo);
                }
            }
        }
        return disinfectPointInfoList;
    }

    /**
     * 更新局部消毒详情
     *
     * @param disinfectPointVo
     * @return
     */
    @Override
    public boolean updateLocalDisinfect(DisinfectPointVo disinfectPointVo) {
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (o == null) {
            log.warn("无法获取当前地图信息");
            return false;
        }
        RobotTask robotTask = iRobotTaskService.getOne(new LambdaQueryWrapper<RobotTask>()
                .eq(RobotTask::getType, TaskType.DISINFECT.getType())
                .eq(RobotTask::getSubType, DisinfectTaskType.LOCAL_TASK.getType())
                .eq(RobotTask::getMapId, o.toString()));
        if (robotTask != null) {
            rosWebService.saveOrUpdateDisinfectPoint(disinfectPointVo, robotTask.getId());
        }
        return true;
    }

    /**
     * 是否进行喷雾消毒
     *
     * @param list 本次任务所执行的消毒设备和点位关系
     * @return true：是，false:否
     */
    @Override
    public boolean isSprayDisinfect(List<RobotDisinfect> list) {
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        for (RobotDisinfect robotDisinfect : list) {
            if (SUCCESS.equals(robotDisinfect.getSpray())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 是否进行喷雾消毒
     *
     * @param taskId 当前任务ID
     * @return true：是，false:否
     */
    @Override
    public boolean isSprayDisinfect(String taskId) {
        String result = RedisUtil.getHash(TASK_INFO+":"+taskId, TASK_IS_CONTAINS_SPRAY);
        if (StringUtils.isBlank(result)) {
            List<RobotDisinfect> robotDisinfects = list(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotTaskId, taskId).orderByAsc(RobotDisinfect::getUpdateTime));
            result = ObjectUtil.toString(isSprayDisinfect(robotDisinfects));
            RedisUtil.hset(TASK_INFO+":"+taskId, TASK_IS_CONTAINS_SPRAY,result);
        }
        return Boolean.parseBoolean(result);
    }

    /**
     * 是否进行紫外或者脉冲消毒
     *
     * @param list 本次任务所执行的消毒设备和点位关系
     * @return true：是，false:否
     */
    @Override
    public boolean isUltravioletDisinfect(List<RobotDisinfect> list) {
        if (CollectionUtil.isEmpty(list)) {
            return false;
        }
        for (RobotDisinfect robotDisinfect : list) {
            if (SUCCESS.equals(robotDisinfect.getUlray())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public List<PositionsVo> getPoseInfoBytaskId(String id) {
        return baseMapper.getPoseInfoBytaskId(id);
    }

    @Override
    public RouteVo getRouteInfoByTaskId(String id) {
        return baseMapper.getRouteInfoByTaskId(id);
    }
}