package com.yx.robot.modules.admin.jike.enums;

/**
 * 状态码
 * <AUTHOR>
 * @date 2021/12/13
 */
public enum RespCodeEnum {

    SUCCESS("10000000"    ,"成功"),

    SYS_ERROR("44000001","系统异常"),

    BUSINESS("44000002","业务异常");

    private String code;

    private String label;

    RespCodeEnum(String code, String label) {
        this.code = code;
        this.label = label;
    }

    public String getCode() {
        return code;
    }

    public String getLabel() {
        return label;
    }
}
