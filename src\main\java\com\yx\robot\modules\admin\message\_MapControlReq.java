package com.yx.robot.modules.admin.message;

/**
 * 地图管理请求
 */
@MessageType(string = "frame_id cmd map_name")
public class _MapControlReq extends Message {

    public String frame_id;

    /**
     * 0: 开始建图（cartographer） 废弃
     * 1: 开始建图（gmapping）
     * 2：停止建图
     * 3：保存地图
     * 4：修改地图
     * 5：地图续建（cartographer）
     * 6：继续建图 (gmapping)
     */
    public int cmd;

    /**
     * 地图名称
     */
    public String map_name;

    /**
     * 地图大小
     */
    public int map_size = 3000;
}
