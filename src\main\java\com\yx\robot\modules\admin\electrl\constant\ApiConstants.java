package com.yx.robot.modules.admin.electrl.constant;

/**
 * 接口常量定义
 * <AUTHOR>
 * @date 2021/12/11
 */
public interface ApiConstants {

    /**
     * 地址前缀
     */
//    String BASE_URL = "https://192.168.15.163:8090";

    String BASE_URL = "https://192.168.0.22:8090";

    /**
     * SDK开发者认证
     */
    String DEVELOPER_LOGIN = "/api/cloud/base/developerLogin";

    /**
     * 获取设备信息
     */
    String GET_DEVICE_INFO = "/api/cloud/base/getDeviceInfo";

    /**
     * 预约单云电梯
     */
    String CALL_ELEVATOR = "/api/cloud/elevator/callElevator";

    /**
     * 预约无感电梯
     */
    String CALL_NONIDUCTIVE_ELEVATOR = "/api/cloud/elevator/callNoninductiveElevator";

    /**
     * 发送开门指令
     */
    String SEND_OPEN_DOOR = "/api/cloud/elevator/sendOpenDoor";

    /**
     * 获取任务信息
     */
    String GET_TASK_INFO = "/api/cloud/elevator/getTaskInfo";

}
