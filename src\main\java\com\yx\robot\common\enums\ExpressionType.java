package com.yx.robot.common.enums;

/**
 * <AUTHOR>
 * @date 2022/10/20
 * description：点阵表情类型
 */
public enum ExpressionType {

    EMERGENCY_STOP(4,"急停被按下"),

    SMILE_ONE(5,"微笑一"),

    WORKING(6,"工作中"),

    SMILE_TWO(7,"微笑二"),

    WORK_DISTURBED(8,"工作被打扰"),

    FIND_CHARGING_PILE(9,"寻找充电桩"),

    CHARGING_ING(10,"充电中"),

    FULL_BATTERY(11,"满电"),

    LOW_BATTERY(12,"低电"),

    LOST_WAY(14,"迷路");

    private final Integer value;

    private final String statue;

    ExpressionType(Integer value, String statue) {
        this.value = value;
        this.statue = statue;
    }

    public Integer getValue() {
        return value;
    }

    public String getStatue() {
        return statue;
    }}
