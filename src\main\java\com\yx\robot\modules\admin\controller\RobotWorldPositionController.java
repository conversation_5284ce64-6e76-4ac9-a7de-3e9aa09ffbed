package com.yx.robot.modules.admin.controller;

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dto.YxRobotWorldPositionDto;
import com.yx.robot.modules.admin.entity.RobotWorldPosition;
import com.yx.robot.modules.admin.service.IRobotWorldPositionService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人世界坐标系管理接口")
@RequestMapping("/yx/api-v1/robotWorldPosition")
@Transactional
public class RobotWorldPositionController {

    @Autowired
    private IRobotWorldPositionService iRobotWorldPositionService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotWorldPosition> get(@PathVariable String id){

        RobotWorldPosition robotWorldPosition = iRobotWorldPositionService.getById(id);
        return new ResultUtil<RobotWorldPosition>().setData(robotWorldPosition);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotWorldPosition>> getAll(){

        List<RobotWorldPosition> list = iRobotWorldPositionService.list();
        return new ResultUtil<List<RobotWorldPosition>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotWorldPosition>> getByPage(@ModelAttribute PageVo page){

        IPage<RobotWorldPosition> data = iRobotWorldPositionService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotWorldPosition>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotWorldPosition> saveOrUpdate(@ModelAttribute RobotWorldPosition robotWorldPosition){

        if(iRobotWorldPositionService.saveOrUpdate(robotWorldPosition)){
            return new ResultUtil<RobotWorldPosition>().setData(robotWorldPosition);
        }
        return new ResultUtil<RobotWorldPosition>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotWorldPositionService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }

    @RequestMapping(value = "updateRobotWorldPosition", method = RequestMethod.POST)
    @ApiOperation(value = "更新世界坐标系点位")
    public Result<Boolean> updateRobotWorldPosition(@RequestBody YxRobotWorldPositionDto yxRobotWorldPositionDto) {
        boolean res = iRobotWorldPositionService.updateRobotWorldPosition(yxRobotWorldPositionDto);
        if(res) {
            return new ResultUtil<Boolean>().setSuccessMsg("更新世界坐标系点位成功");
        }else {
            return new ResultUtil<Boolean>().setSuccessMsg("更新世界坐标系点位失败");
        }
    }
}
