package com.yx.robot.common.enums;

/**
 * <AUTHOR>
 * @date 2022/8/31
 * description：
 */
public enum RabbitMqMessageType {


    UPLOAD_MAP_FAILURE(1,"建图后的地图上传云端失败"),

    UPLOAD_MAP_SUCCESS(2,"建图后的地图上传云端成功"),

    SAVE_MAP_ERROR(3,"保存地图服务调用异常"),

    MANAGER_MAP(4,"地图管理服务远程过程调用异常"),

    PROTECTING(5,"需要保养"),

    COLLISION_STRIP_TRIGGER(6,"防撞条触发"),

    LOW_BATTERY_SHOUT_DOWN(7,"低电量自动关机"),

    LOW_LIQUID(8,"低液位"),

    ROS_CONNECT_FAILED(9,"ROS连接失败"),

    PATH_PLANNING_FAILED(10,"路径规划失败"),

    LOCATION_LOST(11,"充电桩检测失败定位丢失"),

    GO_TO_CHARING_FAILURE(12,"前往充电失败,循环退出"),

    DOCKING_CHARGING_PILE_FAILURE(13,"对接充电桩失败"),

    SYSTEM_CHECK_FAILURE(14,"系统自检不通过"),

    LIDAY_ERROR(15,"雷达报错"),

    DEEP_ERROR(16,"深度报错"),

    MOTOR_ERROR(17,"电机报错"),

    EXPIRATION_OF_LAMP(18,"灯管使用寿命到期"),

    FULL_LIQUID(19,"满液位"),

    BATTERY_CHARING_REMIND(20,"机器如未开机使用，请充电后再放置");


    private Integer type;

    private String value;

    RabbitMqMessageType(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return this.type;
    }

    public String getValue() {
        return value;
    }
}
