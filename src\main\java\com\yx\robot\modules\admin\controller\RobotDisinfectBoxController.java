package com.yx.robot.modules.admin.controller;

import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotDisinfectBox;
import com.yx.robot.modules.admin.service.IRobotDisinfectBoxService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(description = "机器人消毒仓条目    管理接口")
@RequestMapping("/yx/api-v1/robotDisinfectBox")
@Transactional
public class RobotDisinfectBoxController {

    @Autowired
    private IRobotDisinfectBoxService iRobotDisinfectBoxService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotDisinfectBox> get(@PathVariable String id){

        RobotDisinfectBox robotDisinfectBox = iRobotDisinfectBoxService.getById(id);
        return new ResultUtil<RobotDisinfectBox>().setData(robotDisinfectBox);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据")
    public Result<List<RobotDisinfectBox>> getAll(){

        List<RobotDisinfectBox> list = iRobotDisinfectBoxService.list();
        return new ResultUtil<List<RobotDisinfectBox>>().setData(list);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotDisinfectBox>> getByPage(@ModelAttribute PageVo page){

        IPage<RobotDisinfectBox> data = iRobotDisinfectBoxService.page(PageUtil.initMpPage(page));
        return new ResultUtil<IPage<RobotDisinfectBox>>().setData(data);
    }

    @RequestMapping(value = "/insertOrUpdate", method = RequestMethod.POST)
    @ApiOperation(value = "编辑或更新数据")
    public Result<RobotDisinfectBox> saveOrUpdate(@ModelAttribute RobotDisinfectBox robotDisinfectBox){

        if(iRobotDisinfectBoxService.saveOrUpdate(robotDisinfectBox)){
            return new ResultUtil<RobotDisinfectBox>().setData(robotDisinfectBox);
        }
        return new ResultUtil<RobotDisinfectBox>().setErrorMsg("操作失败");
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Object> delAllByIds(@PathVariable String[] ids){

        for(String id : ids){
            iRobotDisinfectBoxService.removeById(id);
        }
        return new ResultUtil<Object>().setSuccessMsg("批量通过id删除数据成功");
    }
}
