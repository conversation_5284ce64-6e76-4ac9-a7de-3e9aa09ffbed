package com.yx.robot.modules.admin.message;

@MessageType(string = "riki_msgs/system_check")
public class _SystemCheck extends Message{


    /**
     * =======================通用底盘
     *
     * 雷达
     */
    public String lidar;
    /**
     *  深度
     */
    public String depth_camera;
    /**
     * imu设备
     */
    public String imu;
    /**
     * 电机
     */
    public String motor;
    /**
     * 电源管理板
     */
    public String bms;
    /**
     * 防撞条
     */
    public String collision;
    /**
     * 急停开关
     */
    public String stop_btn;
    /**
     * 自动充电头
     */
    public String auto_charge;
    /**
     * 下层板
     */
    public String singlechip_bottom;
    /**
     * 转向灯
     */
    public String turn_light;
    /**
     * 电源键
     */
    public String power_key;
    /**
     * 电源管理板
     */
    public String system_charge;

    /**
     * ====================运输
     */
    /**
     * 液晶显示屏
     */
    public String eye;
    /**
     * 广告屏
     */
    public String ad;
    /**
     * 触控按钮
     */
    public String touch;
    /**
     * 门
     */
    public String door;
    /**
     * led灯
     */
    public String led_light;
    /**
     *  红外摄像头
     */
    public String infra_light;
    /**
     * 上层板
     */
    public String singlechip_top;
    /**
     *  ===================消毒=======
     */
    /**
     * 脉冲灯
     */
    public String pulse;
    /**
     * 紫外线
     */
    public String ulray;
    /**
     * 喷雾
     */
    public String spray;
    /**
     * 风扇
     */
    public String fan;
    /**
     *  抽水泵
     */
    public String pump;
    /**
     * 液位
     */
    public String wiquid;
    /**
     * 消毒板
     */
    public String doctor;
    /**
     * 环形灯
     */
    public String roll_led;

    /**
     * ============节点检测
     */
    /**
     * 传感器节点
     */
    public String[] sensor_nodes;
    /**
     * 任务节点
     */
    public String[] task_nodes;
    /**
     * 导航节点
     */
    public String[] navigation_nodes;
    /**
     * slam 节点
     */
    public String[] slam_nodes;
}
