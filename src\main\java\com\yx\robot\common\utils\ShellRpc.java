package com.yx.robot.common.utils;

import ch.ethz.ssh2.*;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.net.InetAddress;

/**
 *
 *  远程调用脚本..
 * <AUTHOR>
 *
 */
@Slf4j
public class ShellRpc {

    /**
     * 上传文件到远程服务器
     * @param userName
     * @param password
     * @param ipAddr
     * @param localFilePath
     * @param remoteFilePath
     * @return
     */
    public static boolean uploadFileToRemote(String userName, String password, String ipAddr, String localFilePath, String remoteFilePath) {
        boolean isAuthed = false;
        try {
            if (InetAddress.getByName(ipAddr).isReachable(1500)) {
                Connection conn = new Connection(ipAddr);
                conn.connect();
                isAuthed = conn.authenticateWithPassword(userName, password);
                if (isAuthed) {
                    SCPClient scpClient = conn.createSCPClient();
                    scpClient.put(localFilePath, remoteFilePath);
                    conn.close();
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

        return isAuthed;
    }


}