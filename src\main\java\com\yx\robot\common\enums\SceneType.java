package com.yx.robot.common.enums;

/**
 * 场景类型
 *
 * <AUTHOR>
 * @date 2020/05/20
 */
public enum SceneType {

    /**
     * 3, "流程语音", "开始工作", "我要开始工作啦"
     */
    START(3, "流程语音", "开始工作", "我要开始工作啦"),

//    小哥哥，人家迷路了呢，请把我推到原点重启
    DEVIATION_ROUTE(3, "流程语音", "导航偏离", "我好像迷路了，请把我推回充电桩哦"),

    SYS_ERROR(3, "流程语音", "系统错误", "系统出错啦，请联系工作人员哦"),

    EMERGENCY_ERROR_IDLE(3, "流程语音", "急停", "我被按急停啦，请注意松开哦"),

    EMERGENCY_ERROR_NOT_IDLE(3, "流程语音", "急停", "急停已被按下，记得随时查看哦"),

    BUMP_INTO_OBJECTS(3, "流程语音", "防撞条触发", "哎呀撞到我啦，请注意避让"),

    BACK_TO_CHARGING(3, "流程语音", "返回充电", "嘿嘿嘿，我要去补充能量了"),

    BACK_TO_ORIGIN(3, "流程语音", "返回原点", "我要回去啦"),

    //小乐正在补充能量哦 rosWebService.sendVoicePrompt(SceneType.CHARGING, null);
    CHARGING(3, "流程语音", "充电中", "主人，我要休息了"),

    /**
     * 3,"流程语音","消毒过程中","正在消毒中，请注意避让"
     */
    DISINFECT_PROCESS(3, "流程语音", "消毒过程中", "小乐正在努力消毒，请小心避让哦"),

    DISINFECT_WARNING(3, "流程语音", "消毒过程中", "小乐还在工作中，离我远点哦"),

    SHUTDOWN_PROCESS(3, "流程语音", "关机过程中", "正在关机中，下次见哦"),

    SHUTDOWN_PROCESS_TWO(3, "流程语音", "关机过程中", "30秒后就要关机啦，拜拜"),

    OUT_WATER(3, "流程语音", "排水过程中", "我在排除脏水，谨防误碰哦"),

    OUT_WATER_TWO(3, "流程语音", "排水过程中", "小乐正在努力排水，谨防误碰哦"),

    SPRAY_WATER_WARNING(3, "流程语音", "雾化水位不足", "小乐水位不足了，请及时添加哦"),

    //_DQSWYMBYZJLE：当前水位已加满，可以不用再加了哦 改为_DQSWYMQBYGWJSL：当前水位已加满，请不要给我加水了
    SPRAY_OVER_WATER_WARNING(3, "流程语音", "当前水位已满", "当前水位已加满，请不要给我加水了"),

    SPRAY_OVER_WATER_WARNING_TWO(3, "流程语音", "当前水位已满", "当前水位已满，再加我就要撑了哦"),

    MOCK_LOCK_WARN(3, "流程语音", "前方危险", "前方危险，请把我推到安全位置继续工作吧"),
    /**
     * 3,"流程语音","路径规划失败","路径规划失败,请确保机器人附近或目标位置没有障碍物"
     */
    PLAN_PROCESS(3, "流程语音", "路径规划失败", "我找不到方向啦，路径规划失败，请把我推回充电桩哦"),

    END_CHARGING(3, "流程语音", "脱离充电桩", "我要开始努力工作啦"),

    SYS_FAIL(3, "流程语音", "系统自检不通过", "主人，非常抱歉，小乐遇到故障了，请您尝试重新启动或者联系机器人厂家哦"),

    DEPTH_FAIL(3, "流程语音", "深度异常", "主人，非常抱歉，深度设备无法启动了，请您尝试重新启动或者联系机器人厂家哦"),

    SYS_SUCCESS(3, "流程语音", "系统自检通过", "你好，我是小乐，我已经成功启动了哦"),

    NOT_FOUND_CHARGING(3, "流程语音", "无法检测到充电桩", "哎呀我迷路了，请把我推回充电桩哦"),

    POSITION_SUCCESS(3, "流程语音", "定位成功", "定位成功，可以使用手机app，或者语音对话，控制小乐去消毒哦"),

    START_MAP(3, "流程语音", "", "小乐来到了一个陌生环境，请主人扫描小乐前方二维码，下载手机app，为小乐创建消毒区域的地图吧"),

    CONNECTED(3, "流程语音", "建立连接", "当当当，连接成功，小乐竭诚为您服务"),

    DONT_WORK_OF_LOW_BATTERY(3,"流程语音","当前电量不足，无法进行消毒工作", "当前电量不足，无法进行消毒工作"),

    ALERT_MAP(3, "流程语音", "提醒开始建图", "请主人点击app下方的部署按键，然后选择创建地图哦"),

    ALERT_OPERATION(3, "流程语音", "操作提醒", "请您推动小乐去探索未知区域吧，扫描地图前，注意清空路径上的障碍物哦"),

    MAPPING_PROCESS_1(3, "流程语音", "建图提醒", "地图中的白色代表可通行区域，黑色代表障碍物，红色代表激光雷达扫描信息，灰色代表未知区域，绿色曲线代表消毒路径"),

    MAPPING_PROCESS_2(3, "流程语音", "建图提醒", "建图时请清空路径上的障碍物哦"),

    MAPPING_PROCESS_3(3, "流程语音", "建图提醒", "雷达信息和障碍物重合度越高，建图效果越好哦"),

    CONNECT_SUCCESS(3, "流程语音", "建立连接", "我已和主人成功建立连接,小乐很高兴为您服务"),

    SAVE_MAP_ALERT(3, "流程语音", "保存地图提醒", "主人,保存地图之前请不要站在我旁边哦"),

    SAVE_MAP(3, "流程语音", "保存地图", "当当当，地图保存成功啦"),

    SELECT_MAP(3, "流程语音", "切换地图", "切换地图成功，已为主人选择默认消毒路径"),

    SAVE_MAP_X4(3, "流程语音", "X4保存地图", "啦啦啦，地图保存成功"),

    SETTING_FIXED_TIME_TASK(3, "流程语音", "保存定时任务", "定时任务已保存，小乐会按时工作的哦"),

    SELECT_MAP_X4(3, "流程语音", "X4切换地图", "啦啦啦，地图切换成功"),

    SETTING_ROUTE_TASK(3, "流程语音", "保存路径", "机器人语音提示，主人，消毒路径更改成功，小乐会沿着路径进行消毒哦"),

    NO_MAP(3, "流程语音", "没有地图", "小乐现在还没有地图，脑袋空空，不能消毒哦"),

    NO_ROUTE(3, "流程语音", "没有路径", "小乐现在还没有路径，可以先推着小乐认路哦"),

    STOP_WORK(3, "流程语音", "停止任务", "任务已暂停，请主人选择继续任务或者取消任务哦"),

    CONTINUE_WORK(3, "流程语音", "继续任务", "小乐要继续任务了哦"),

//    CANCEL_WORK(3, "流程语音", "取消任务", "主人，任务已被取消了哦"),

    NOT_DEFAULT_TASK_LINE(3, "流程语音", "没有默认消毒路径", "主人，当前小乐没有默认消毒路线，请使用APP设置好哦"),

    NOT_EXEC_TASK_LINE(3, "流程语音", "不支持巡线消毒", "主人，当前小乐不支持巡线消毒哦"),

    CONTINUE_WORK_TWO(3, "流程语音", "继续任务", "小乐要继续努力工作啦"),

    CANCEL_WORK(3, "流程语音", "取消任务", "任务已经取消，请注意查看哦"),

    //新增语音反馈

    DONT_WORK_OF_DUCK(3,"流程语音","正在进行手动充电，无法进行消毒工作", "正在进行手动充电，无法进行消毒工作"),

    DONT_WORK_OF_LOSE_LOCATION(3,"流程语音","定位丢失，无法进行消毒工作", "定位丢失，无法进行消毒工作"),

    DONT_WORK_OF_DOCK(3,"流程语音","正在对接充电中,请先结束充电", "正在对接充电中,请先结束充电"),

    DONT_WORK_OF_OUT_OF_TIME(3,"流程语音","任务不在可执行的时间段内", "任务不在可执行的时间段内");


    /**
     * 3、流程语音 0、点位语音
     */
    private final Integer speechType;

    private final String label;

    private final String value;

    private final String defaultContent;

    SceneType(Integer speechType, String label, String value, String defaultContent) {
        this.speechType = speechType;
        this.label = label;
        this.value = value;
        this.defaultContent = defaultContent;
    }

    public String getValue() {
        return value;
    }

}
