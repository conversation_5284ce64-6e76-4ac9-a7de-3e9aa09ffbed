package com.yx.robot.modules.admin.jike.dto;


import cn.hutool.core.date.DateUtil;
import com.yx.robot.common.utils.RDes;
import com.yx.robot.modules.admin.jike.constant.JikeConstants;
import com.yx.robot.modules.admin.jike.enums.RobotTypeEnum;
import com.yx.robot.modules.admin.jike.enums.TaskStatusEnum;
import lombok.Data;

import java.util.Date;

import static cn.hutool.core.date.DatePattern.NORM_DATETIME_PATTERN;

/**
 * 机器人工作状态
 * <AUTHOR>
 * @date 2021/12/13
 */
@Data
public class RobotWorkStatusDto {
        private String robotBrandCode = JikeConstants.FAC_CODE;
        private String robotNo = JikeConstants.ROBOT_NO;
        private Integer robotType = RobotTypeEnum.X1.getType();
        private String buildingNo = JikeConstants.BULID_NO;
        private String reportTime = DateUtil.format(new Date(), NORM_DATETIME_PATTERN);
        private Integer taskStatus = TaskStatusEnum.FINISH_WORK.getValue();
        private String destinationId = "";
        private String taskId = "";
        private String orderId = "";
        private String floorNo = "";
        private String roomNo = "";
        private String remark = "";
}
