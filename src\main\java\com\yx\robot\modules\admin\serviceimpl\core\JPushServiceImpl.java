package com.yx.robot.modules.admin.serviceimpl.core;

import cn.hutool.core.util.StrUtil;
import cn.jiguang.common.ClientConfig;
import cn.jiguang.common.resp.APIConnectionException;
import cn.jiguang.common.resp.APIRequestException;
import cn.jpush.api.JPushClient;
import cn.jpush.api.push.PushResult;
import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.PushPayload;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.Notification;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.enums.MsgPushTemplate;
import com.yx.robot.common.utils.RDes;
import com.yx.robot.modules.admin.service.core.JPushService;
import com.yx.robot.modules.admin.sync.constant.DataSyncConstant;
import com.yx.robot.modules.admin.sync.util.SyncRedisUtil;
import com.yx.robot.modules.admin.vo.RobotMsgVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.util.Date;

/**
 * 极光推送服务类
 * <AUTHOR>
 * @date 2020/02/15
 */
@Service
@Slf4j
public class JPushServiceImpl implements JPushService {

    @Value("${robot.jpush.master-secret}")
    private String masterSecret;

    @Value("${robot.jpush.app-key}")
    private String appKey;

    /**
     * 给所有人群发消息
     *
     * @return
     */
    @Override
    public PushPayload buildPushObjectByAlias(String alertTitle, String alertContent, String alias, Message message) {
        return PushPayload.newBuilder()
                .setPlatform(Platform.android())
                .setAudience(Audience.alias(alias))
                .setNotification(Notification.android(alertContent,alertTitle,null))
                .setMessage(message)
                .build();
    }

    /**
     * 自定义消息模板
     *
     * @param msgPushTemplate
     * @param subVal
     * @return
     */
    @Override
    public MsgPushTemplate defineValue(MsgPushTemplate msgPushTemplate, String subVal) {
        String value = msgPushTemplate.getValue();
        String concat = value.concat(":" + subVal);
        msgPushTemplate.setValue(concat);
        return msgPushTemplate;
    }

//    /**
//     * 推送消息
//     *
//     * @param msgPushTemplate
//     * @return
//     */
//    @Override
//    public boolean pushMsg(MsgPushTemplate msgPushTemplate) {
//        ClientConfig clientConfig = ClientConfig.getInstance();
//        JPushClient jpushClient = new JPushClient(masterSecret, appKey, null, clientConfig);
//        String serverId = RDes.getServerId();
//        String serialNumber = RDes.getSerialNumber();
//        if(StrUtil.isEmpty(serverId)) {
//            return false;
//        }
//        if(StrUtil.isEmpty(serialNumber)) {
//            return false;
//        }
//        String deviceTypeAndCode = RDes.decrypt(serialNumber);
//        String alertTitle  = "机器人" + deviceTypeAndCode + "发出一条消息";
//        Message message = Message.content(msgPushTemplate.getValue());
//        PushPayload payload = this.buildPushObjectByAlias(alertTitle,msgPushTemplate.getValue(),serverId,message);
//        Jedis jedis = null;
//        try {
//            PushResult result = jpushClient.sendPush(payload);
//            int status = result.getResponseCode();
//            log.info("响应状态码为" + status);
//            RobotMsgVo robotMsgVo = new RobotMsgVo();
//            robotMsgVo.setSerialNumber(serialNumber);
//            robotMsgVo.setStatus(0);
//            robotMsgVo.setScene(msgPushTemplate.getScene());
//            robotMsgVo.setTime(new Date());
//            robotMsgVo.setType(msgPushTemplate.getType());
//            robotMsgVo.setValue(msgPushTemplate.getValue());
//            jedis = SyncRedisUtil.getJedis();
//            String key = DataSyncConstant.DEVICE_DATA_KEY + ":" + serialNumber + ":" + DataSyncConstant.MSG_RECORD_KEY;
//            jedis.lpush(key, JSONObject.toJSONString(robotMsgVo));
//            return true;
//        } catch (APIConnectionException e) {
//            log.error("Connection error. Should retry later. ", e);
//            log.error("Sendno: " + payload.getSendno());
//        } catch (APIRequestException e) {
//            log.error("Error response from JPush server. Should review and fix it. ", e);
//            log.info("HTTP Status: " + e.getStatus());
//            log.info("Error Code: " + e.getErrorCode());
//            log.info("Error Message: " + e.getErrorMessage());
//            log.info("Msg ID: " + e.getMsgId());
//            log.error("Sendno: " + payload.getSendno());
//        } finally {
//            SyncRedisUtil.closeJedis(jedis);
//        }
//        return false;
//    }
}
