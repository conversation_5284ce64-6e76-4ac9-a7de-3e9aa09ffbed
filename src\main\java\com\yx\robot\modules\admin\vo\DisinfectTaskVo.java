package com.yx.robot.modules.admin.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 消毒任务
 *
 * <AUTHOR>
 * @date 2020/04/08
 */
@Data
public class DisinfectTaskVo {
    /**
     * 任务id
     */
    private String id;

    /**
     * 楼层
     */
    private Integer floor;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 任务类型
     */
    private Integer type;

    /**
     * 是否定时
     */
    private boolean fixedTime;

    /**
     * 星期数
     */
    private String weekdays;

    /**
     * 定时时间
     */
    private String startTime;

    /**
     * 定时周期
     */
    private String period;

    /**
     * 循环次数
     */
    private Integer loops;

    /**
     * 是否默认 是（0）、否（1）
     */
    private boolean defaultTask;

    /**
     * 巡线消毒任务设置
     */
    private DisinfectRouteVo disinfectRouteVo;

    /**
     * 自定义巡线任务
     */
    private List<DefineDisinfectRouteVo> defineDisinfectRouteVos;

    /**
     * 定点消毒任务列表
     */
    private List<DisinfectPointVo> disinfectPointVos;

    @ApiModelProperty(value = "任务可执行，开始时间")
    private String executableStartTime;

    @ApiModelProperty(value = "任务可执行，结束时间")
    private String executableEndTime;

}
