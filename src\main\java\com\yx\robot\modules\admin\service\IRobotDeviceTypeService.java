package com.yx.robot.modules.admin.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.yx.robot.modules.admin.entity.RobotDeviceType;

import java.util.List;

/**
 * 设备和点位关联信息接口
 *
 * <AUTHOR>
 */
public interface IRobotDeviceTypeService extends IService<RobotDeviceType> {

    /**
     * 根据父级类型获取设备类型列表
     *
     * @param parentType 父级类型，规定：值为null时，获取所有设备类型，值为-1时获取所有一级类型
     * @return list
     */
    List<RobotDeviceType> getList(Integer parentType);
}