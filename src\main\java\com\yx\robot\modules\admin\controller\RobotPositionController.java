package com.yx.robot.modules.admin.controller;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.yx.robot.common.enums.RobotOperationStatus;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.vo.PageVo;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.entity.RobotEntranceGuard;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.service.IRobotEntranceGuardService;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import com.yx.robot.modules.admin.service.IRobotStatusService;
import com.yx.robot.modules.admin.vo.RobotPositionVo;
import com.yx.robot.modules.admin.vo.RobotSubPositionVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.List;

import static com.yx.robot.common.constant.RosWebConstants.CURRENT_MAP;
import static com.yx.robot.common.constant.RosWebConstants.ROBOT_SYS_INFO;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@Api(tags = "机器人点位信息管理接口")
@RequestMapping("/yx/api-v1/robotPosition")
@Transactional(rollbackFor = Exception.class)
@AllArgsConstructor
public class RobotPositionController {

    private IRobotPositionService iRobotPositionService;

    private IRobotEntranceGuardService iRobotEntranceGuardService;

    private StringRedisTemplate redisTemplate;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @RequestMapping(value = "/get/{id}", method = RequestMethod.GET)
    @ApiOperation(value = "通过id获取")
    public Result<RobotPosition> get(@PathVariable String id) {

        RobotPosition robotPosition = iRobotPositionService.getById(id);
        return new ResultUtil<RobotPosition>().setData(robotPosition);
    }

    @RequestMapping(value = "/getAll", method = RequestMethod.GET)
    @ApiOperation(value = "获取全部数据根据地图ID")
    public Result<List<RobotPositionVo>> getAll(@RequestParam String mapId) {
        QueryWrapper<RobotPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", mapId);
        List<RobotPosition> robotPositionList = iRobotPositionService.list(queryWrapper);
        List<RobotPositionVo> robotPositionVoList = new ArrayList<>();
        for (RobotPosition robotPosition : robotPositionList) {
            RobotPositionVo robotPositionVo = iRobotPositionService.robotPositionToRobotPositionVo(robotPosition);
            robotPositionVoList.add(robotPositionVo);
        }
        return new ResultUtil<List<RobotPositionVo>>().setData(robotPositionVoList);
    }

    @RequestMapping(value = "/getChargingPile", method = RequestMethod.GET)
    @ApiOperation(value = "根据地图ID获取充电桩点位")
    public Result<RobotPositionVo> getChargingPile(@RequestParam String mapId) {
        QueryWrapper<RobotPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("map_id", mapId).eq("name", "充电桩");
        RobotPosition one = iRobotPositionService.getOne(queryWrapper);
        RobotPositionVo robotPositionVo = null;
        if (ObjectUtil.isNotNull(one)) {
            robotPositionVo = iRobotPositionService.robotPositionToRobotPositionVo(one);
        }
        return new ResultUtil<RobotPositionVo>().setData(robotPositionVo);
    }

    @RequestMapping(value = "/getAllByType", method = RequestMethod.GET)
    @ApiOperation(value = "根据类型获取全部数据")
    public Result<List<RobotPositionVo>> getAllByType(@RequestParam Integer type, String mapId) {
        List<RobotPositionVo> robotPositionVoList = new ArrayList<>();
        if (StrUtil.isBlank(mapId)) {
            Object currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
            if (null != currentMap) {
                mapId = currentMap.toString();
            }
        }
        List<RobotPosition> robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                .eq(RobotPosition::getMapId, mapId).eq(RobotPosition::getType, type));
        for (RobotPosition robotPosition : robotPositionList) {
            RobotPositionVo robotPositionVo = iRobotPositionService.robotPositionToRobotPositionVo(robotPosition);
            robotPositionVoList.add(robotPositionVo);
        }
        return new ResultUtil<List<RobotPositionVo>>().setData(robotPositionVoList);
    }

    @RequestMapping(value = "/getByPage", method = RequestMethod.GET)
    @ApiOperation(value = "分页获取")
    public Result<IPage<RobotPositionVo>> getByPage(@ModelAttribute PageVo page,
                                                    @RequestParam String mapId,
                                                    @RequestParam Integer type) {
        QueryWrapper<RobotPosition> queryWrapper = new QueryWrapper<>();
        if (StrUtil.isNotBlank(page.getName())) {
            queryWrapper.like("name", page.getName());
        }
        queryWrapper.eq("map_id", mapId);
        if (null != type) {
            queryWrapper.eq("type", type);
        }
        queryWrapper.isNull("parent_id");
        IPage<RobotPosition> data = iRobotPositionService.page(PageUtil.initMpPage(page), queryWrapper);

        IPage<RobotPositionVo> dataTrans = new Page<>();
        dataTrans.setCurrent(data.getCurrent());
        dataTrans.setPages(data.getPages());
        dataTrans.setSize(data.getSize());
        dataTrans.setTotal(data.getTotal());
        List<RobotPosition> robotPositionList = data.getRecords();
        List<RobotPositionVo> robotPositionVoList = new ArrayList<>();
        for (RobotPosition robotPosition : robotPositionList) {
            RobotPositionVo robotPositionVo = iRobotPositionService.robotPositionToRobotPositionVo(robotPosition);
            robotPositionVoList.add(robotPositionVo);
        }
        dataTrans.setRecords(robotPositionVoList);
        return new ResultUtil<IPage<RobotPositionVo>>().setData(dataTrans);
    }

    @RequestMapping(value = "/getAllRobotSubPosition")
    @ApiOperation(value = "获取所有机器人子位置区域")
    public Result<List<RobotSubPositionVo>> getAllRobotSubPosition(@RequestParam String id) {
        List<RobotPosition> robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getParentId, id));
        List<RobotSubPositionVo> robotSubPositionVos = new ArrayList<>();
        for (RobotPosition robotPosition : robotPositionList) {
            RobotSubPositionVo robotSubPositionVo = iRobotPositionService.robotPositionToRobotSubPositionVo(robotPosition);
            robotSubPositionVos.add(robotSubPositionVo);
        }
        return new ResultUtil<List<RobotSubPositionVo>>().setData(robotSubPositionVos);
    }

    @RequestMapping(value = "/insert", method = RequestMethod.POST)
    @ApiOperation(value = "插入数据")
    public Result<Boolean> save(@RequestBody RobotPositionVo robotPositionVo) {
        return iRobotPositionService.add(robotPositionVo);
    }

    @RequestMapping(value = "/sortPositionAndRs", method = RequestMethod.GET)
    @ApiOperation(value = "排序")
    public Result<Boolean> sort() {
        boolean res = iRobotPositionService.sort();
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "一键排序成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("一键排序失败");
        }
    }

    @RequestMapping(value = "/update", method = RequestMethod.PUT)
    @ApiOperation(value = "更新数据")
    public Result<Boolean> update(@RequestBody RobotPositionVo robotPositionVo) {
        return iRobotPositionService.update(robotPositionVo);
    }

    @RequestMapping(value = "/delByIds/{ids}", method = RequestMethod.DELETE)
    @ApiOperation(value = "批量通过id删除")
    public Result<Boolean> delAllByIds(@PathVariable String[] ids) {
        if (RobotOperationStatus.RUNNING.equals(iRobotStatusService.getOperationStatus())) {
            return new ResultUtil<Boolean>().setErrorMsg("操作失败,任务执行中不允许删除点位！");
        }
        try {
            log.info("删除点位:{}", JSON.toJSONString(ids));
            for (String id : ids) {
//                门禁点位成对删除，如果删除一个，另一个也会删除，仅限于t_robot_entrance_guard表
                RobotPosition robotPosition = iRobotPositionService.getById(id);
//                如果点位类型是门禁内
                if (robotPosition.getType().equals(RobotPositionType.INSIDE_ENTRANCE_GUARD.getType())) {
//                    级联删除t_robot_entrance_guard表中的点位数据
                    iRobotEntranceGuardService.remove(new QueryWrapper<RobotEntranceGuard>().eq("position_a_id", id));
                }
//                如果点位类型是门禁外
                if (robotPosition.getType().equals(RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType())) {
//                    级联删除t_robot_entrance_guard表中的点位数据
                    iRobotEntranceGuardService.remove(new QueryWrapper<RobotEntranceGuard>().eq("position_b_id", id));
                }
//                再删除点位表中的点位
                iRobotPositionService.delete(id);
            }
            return new ResultUtil<Boolean>().setData(true, "操作成功");
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultUtil<Boolean>().setErrorMsg("操作失败");
        }
    }

    @RequestMapping(value = "/clearAll", method = RequestMethod.DELETE)
    @ApiOperation(value = "清空全部数据")
    public Result<Object> clearAll() {
        Object currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
        if (currentMap != null && StrUtil.isNotBlank(currentMap.toString())) {
            List<RobotPosition> robotPositions = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getMapId, currentMap.toString()));
            if (null != robotPositions && !robotPositions.isEmpty()) {
                for (RobotPosition robotPosition : robotPositions) {
                    iRobotPositionService.delete(robotPosition.getId());
                }
            }
        }
        return new ResultUtil<>().setSuccessMsg("清空数据成功");
    }

    @GetMapping("/getImgPath")
    @ApiOperation(value = "获取图片路劲")
    public Result<String> getImgPath() {
        Result<String> result = new Result<>();
        result.setResult(iRobotPositionService.getImagePath());
        return result;
    }

    @PostMapping("/setPositionStatus")
    @ApiOperation(value = "手动设置定位状态，1：失败，0：成功")
    public Result<Boolean> setPositionStatus(String status) {
        iRobotPositionService.setPositionStatus(status);
        return ResultUtil.success("true");
    }

}
