package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.constant.DeviceInfoConstants;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.NavType;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.utils.DateUtils;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotEntranceGuardMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotLocationMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotPositionMapper;
import com.yx.robot.modules.admin.dao.mapper.RobotWorldPositionMapper;
import com.yx.robot.modules.admin.entity.RobotEntranceGuard;
import com.yx.robot.modules.admin.entity.RobotLocation;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.entity.RobotWorldPosition;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.vo.InterceptFunctionVo;
import com.yx.robot.modules.admin.vo.RobotEntranceGuardVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import static com.yx.robot.common.constant.RobotRedisConstants.*;
import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 机器人门禁控制接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotEntranceGuardServiceImpl extends ServiceImpl<RobotEntranceGuardMapper, RobotEntranceGuard> implements IRobotEntranceGuardService {

    @Autowired
    private RobotEntranceGuardMapper robotEntranceGuardMapper;

    @Autowired
    private RobotLocationMapper robotLocationMapper;

    @Autowired
    private RobotPositionMapper robotPositionMapper;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private RobotWorldPositionMapper robotWorldPositionMapper;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotTaskRecordService iRobotTaskRecordService;

    @Autowired
    private IRobotPositionDevRelationService iRobotPositionDevRelationService;

    @Autowired
    private IRobotDeviceOperateService iRobotDeviceOperateService;

    /**
     * 获取所有的门禁区域
     *
     * @return list
     */
    @Override
    public List<RobotEntranceGuardVo> getAll() {
        List<RobotEntranceGuardVo> result = new ArrayList<>();
        String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
        List<RobotEntranceGuard> robotEntranceGuardList = robotEntranceGuardMapper.selectList(new LambdaQueryWrapper<RobotEntranceGuard>().eq(RobotEntranceGuard::getMapId, currentMap));
        if (CollectionUtil.isNotEmpty(robotEntranceGuardList)) {
            for (RobotEntranceGuard robotEntranceGuard : robotEntranceGuardList) {
                RobotPosition robotPositionA = robotPositionMapper.selectById(robotEntranceGuard.getPositionAId());
                RobotPosition robotPositionB = robotPositionMapper.selectById(robotEntranceGuard.getPositionBId());
                if (robotPositionA != null && robotPositionB != null) {
                    RobotEntranceGuardVo item = new RobotEntranceGuardVo();
                    RobotWorldPosition robotWorldPositionA = robotWorldPositionMapper.selectById(robotPositionA.getWorldPoseId());
                    RobotWorldPosition robotWorldPositionB = robotWorldPositionMapper.selectById(robotPositionB.getWorldPoseId());
                    item.setId(robotEntranceGuard.getId());
                    _Point pointA = new _Point();
                    pointA.x = robotWorldPositionA.getPositionX();
                    pointA.y = robotWorldPositionA.getPositionY();
                    pointA.z = robotWorldPositionA.getPositionZ();
                    _Point pointB = new _Point();
                    pointB.x = robotWorldPositionB.getPositionX();
                    pointB.y = robotWorldPositionB.getPositionY();
                    pointB.z = robotWorldPositionB.getPositionZ();
                    item.setPositionA(pointA);
                    item.setPositionB(pointB);
                    item.setRadius(robotEntranceGuard.getRadius());
                    result.add(item);
                }
            }
        }
        return result;
    }

    @Override
    public void delete(String id) {
        RobotEntranceGuard robotEntranceGuard = robotEntranceGuardMapper.selectById(id);
        // 删除门禁区域
        robotEntranceGuardMapper.deleteById(id);
        // 删除门禁点
        String positionAId = robotEntranceGuard.getPositionAId();
        String positionBId = robotEntranceGuard.getPositionBId();
        iRobotPositionService.delete(positionAId);
        iRobotPositionService.delete(positionBId);
    }

    @Override
    public void handleMarkEntranceGuardPosition(RobotPosition robotPosition) {
        String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
        // 如果是门禁内或者门禁外
        if (robotPosition.getType().equals(RobotPositionType.INSIDE_ENTRANCE_GUARD.getType())) {
            List<RobotPosition> robotPositionList = robotPositionMapper.selectList(new LambdaQueryWrapper<RobotPosition>()
                    .eq(RobotPosition::getType, RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType())
                    .eq(RobotPosition::getName, robotPosition.getName())
                    .eq(RobotPosition::getMapId, currentMap));
            if (CollectionUtil.isNotEmpty(robotPositionList)) {
                log.info("找到门禁内点和门禁外点" + robotPosition.getName());
                //  自动创建门禁区域
                createEntranceGuardAreas(robotPosition, robotPositionList.get(0));
            }
        }
        if (robotPosition.getType().equals(RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType())) {
            List<RobotPosition> robotPositionList = robotPositionMapper.selectList(new LambdaQueryWrapper<RobotPosition>()
                    .eq(RobotPosition::getType, RobotPositionType.INSIDE_ENTRANCE_GUARD.getType())
                    .eq(RobotPosition::getName, robotPosition.getName())
                    .eq(RobotPosition::getMapId, currentMap));
            if (CollectionUtil.isNotEmpty(robotPositionList)) {
                log.info("找到门禁内点和门禁外点" + robotPosition.getName());
                // 自动创建门禁区域
                createEntranceGuardAreas(robotPositionList.get(0), robotPosition);
            }
        }
    }

    /**
     * 生成门禁区域
     *
     * @param insidePosition  门禁内点
     * @param outsidePosition 门禁外点
     */
    @Override
    public void createEntranceGuardAreas(RobotPosition insidePosition, RobotPosition outsidePosition) {
        // 查找世界坐标系
        RobotWorldPosition robotWorldPosition1 = robotWorldPositionMapper.selectById(insidePosition.getWorldPoseId());
        RobotWorldPosition robotWorldPosition2 = robotWorldPositionMapper.selectById(outsidePosition.getWorldPoseId());
        Double positionX1 = robotWorldPosition1.getPositionX();
        Double positionX2 = robotWorldPosition2.getPositionX();
        Double positionY1 = robotWorldPosition1.getPositionY();
        Double positionY2 = robotWorldPosition2.getPositionY();
        // 计算区域中心x轴和y轴坐标
        Double positionXCenter = (positionX1 + positionX2) / 2;
        Double positionYCenter = (positionY1 + positionY2) / 2;
        // 计算两点坐标距离充当半径
        double tempX = positionX1 > positionX2 ? (positionX1 - positionX2) : (positionX2 - positionX1);
        double tempY = positionY1 > positionY2 ? (positionY1 - positionY2) : (positionY2 - positionY1);
        double distance = Math.sqrt(tempX * tempX + tempY * tempY);
        RobotEntranceGuard robotEntranceGuard = new RobotEntranceGuard();
        //门禁内
        robotEntranceGuard.setPositionAId(insidePosition.getId());
        //门禁外
        robotEntranceGuard.setPositionBId(outsidePosition.getId());
        RobotLocation robotLocation = robotLocationMapper.selectOne(new LambdaQueryWrapper<RobotLocation>()
                .eq(RobotLocation::getPositionId, insidePosition.getId()));
        if (robotLocation != null) {
            robotEntranceGuard.setName(robotLocation.getLocationCode());
        } else {
            robotEntranceGuard.setName(insidePosition.getName());
        }
        robotEntranceGuard.setCenterX(positionXCenter);
        robotEntranceGuard.setCenterY(positionYCenter);
        // distance 半径
        robotEntranceGuard.setRadius(distance);
        String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
        robotEntranceGuard.setMapId(currentMap);
        robotEntranceGuard.setCreateTime(DateUtils.getDate());
        robotEntranceGuardMapper.insert(robotEntranceGuard);
        log.info("门禁区域{}创建成功", robotEntranceGuard.getName());
    }

    /**
     * 判断局部路径是否穿越门禁区域
     */
    @Override
    public void checkLocalRouteThrough(String robotPositionId) {
        try {
            // 通过目标点计算路径
            String currentMap = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
            List<RobotEntranceGuard> robotEntranceGuardList = this.list(new LambdaQueryWrapper<RobotEntranceGuard>().eq(RobotEntranceGuard::getMapId, currentMap));
            if (CollectionUtil.isEmpty(robotEntranceGuardList)) {
                log.warn("无法查找到当前地图的门禁区域");
                return;
            }
            // 计算目标点穿越哪些门禁区域
            RobotPosition robotPosition = robotPositionMapper.selectById(robotPositionId);
            if (robotPosition.getType().equals(RobotPositionType.INSIDE_ENTRANCE_GUARD.getType()) || robotPosition.getType().equals(RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType())) {
                log.info("当前前往的是门禁点{}，不作逻辑处理", robotPosition.getName());
                return;
            }
            RobotWorldPosition robotWorldPosition = robotWorldPositionMapper.selectById(robotPosition.getWorldPoseId());
            _Pose pose = new _Pose();
            _Pose robotPose = rosWebService.getRobotPose().pose;
            pose.orientation = new _Quaternion();
            pose.orientation.w = robotWorldPosition.getOrientationW();
            pose.orientation.x = robotWorldPosition.getOrientationX();
            pose.orientation.y = robotWorldPosition.getOrientationY();
            pose.orientation.z = robotWorldPosition.getOrientationZ();
            pose.position = new _Point();
            pose.position.x = robotWorldPosition.getPositionX();
            pose.position.y = robotWorldPosition.getPositionY();
            pose.position.z = robotWorldPosition.getPositionZ();
            _Point[] entranceGuardArr = new _Point[robotEntranceGuardList.size()];
            for (int i = 0; i < robotEntranceGuardList.size(); i++) {
                _Point point = new _Point();
                RobotEntranceGuard robotEntranceGuard = robotEntranceGuardList.get(i);
                point.x = robotEntranceGuard.getCenterX();
                point.y = robotEntranceGuard.getCenterY();
                point.z = robotEntranceGuard.getRadius();
                entranceGuardArr[i] = point;
            }
            _OriginalPathReq originalPathReq = new _OriginalPathReq();
            originalPathReq.cmd = 0;
            originalPathReq.door_type = 0;
            originalPathReq.robot_pose_info = robotPose;
            originalPathReq.goal_pose_info = pose;
            originalPathReq.door_access = entranceGuardArr;
            String s = rosBridgeService.callService(ServiceConstants.ORIGINAL_PATH, Message.getMessageType(_OriginalPathReq.class), JSONObject.toJSONString(originalPathReq));
            _PoseStamped[] routePoseList = null;
            if (StrUtil.isNotEmpty(s)) {
                _OriginalPathRep originalPathRep = JSON.parseObject(s, _OriginalPathRep.class);
                if (originalPathRep.ret >= 0) {
                    log.info("目标路径服务调用成功,获取路径数据");
                    Thread.sleep(1000);
                    routePoseList = getOriginPath();
                    if (routePoseList == null || routePoseList.length == 0) {
                        log.warn("无法获取目标位置的局部路径");
                        return;
                    }
                }
            }
            if (null == routePoseList) {
                log.warn("无法获取路径数据");
            }
            // 筛选路径所穿越的门禁区域
            RobotEntranceGuard targetRobotEntranceGuard = null;
            InterceptFunctionVo targetInterceptFunctionVo = null;
            List<_PoseStamped> poseStampeds = new ArrayList<>();
            if (ObjectUtil.isNull(routePoseList)) {
                return;
            }
            for (_PoseStamped routePose : routePoseList) {
                double x = routePose.pose.position.x;
                double y = routePose.pose.position.y;
                for (RobotEntranceGuard robotEntranceGuard : robotEntranceGuardList) {
                    // 垂直平分线方程
                    InterceptFunctionVo interceptFunctionVo = initInterceptFunction(1, robotEntranceGuard.getPositionAId(), robotEntranceGuard.getPositionBId());
                    double k = interceptFunctionVo.k;
                    double b = interceptFunctionVo.b;
                    Double centerX = robotEntranceGuard.getCenterX();
                    Double centerY = robotEntranceGuard.getCenterY();
                    double tempX = x > centerX ? (x - centerX) : (centerX - x);
                    double tempY = y > centerY ? (y - centerY) : (centerY - y);
                    double distance = Math.sqrt(tempX * tempX + tempY * tempY);
                    if (distance < robotEntranceGuard.getRadius()) {
                        String id = RedisUtil.getHash(ENTRANCE_ACCESS_STATUS_MAP, ENTRANCE_ACCESS_STATUS_ID);
                        if (StringUtils.isBlank(id) || !id.trim().equals(robotEntranceGuard.getId())) {
                            RedisUtil.del(ENTRANCE_ACCESS_STATUS_MAP);
                            RedisUtil.hset(ENTRANCE_ACCESS_STATUS_MAP, ENTRANCE_ACCESS_STATUS_ID, robotEntranceGuard.getId());
                        }
                        if (k * x + b > y) {
                            RedisUtil.hset(ENTRANCE_ACCESS_STATUS_MAP, ENTRANCE_ACCESS_STATUS_UP, Boolean.TRUE.toString());
                        } else if (k * x + b < y) {
                            RedisUtil.hset(ENTRANCE_ACCESS_STATUS_MAP, ENTRANCE_ACCESS_STATUS_DOWN, Boolean.TRUE.toString());
                        }
                        targetRobotEntranceGuard = robotEntranceGuard;
                        targetInterceptFunctionVo = interceptFunctionVo;
                        poseStampeds.add(routePose);
                        break;
                    }
                }
                boolean isUp = Boolean.TRUE.toString().equals(RedisUtil.getHash(ENTRANCE_ACCESS_STATUS_MAP, ENTRANCE_ACCESS_STATUS_UP));
                boolean isDown = Boolean.TRUE.toString().equals(RedisUtil.getHash(ENTRANCE_ACCESS_STATUS_MAP, ENTRANCE_ACCESS_STATUS_DOWN));
                if (isUp && isDown) {
                    _PoseStamped checkPoint = poseStampeds.get(0);
                    RobotPosition robotPositionA = robotPositionMapper.selectById(targetRobotEntranceGuard.getPositionAId());
                    RobotPosition robotPositionB = robotPositionMapper.selectById(targetRobotEntranceGuard.getPositionBId());
                    if (robotPositionA != null && robotPositionB != null) {
                        RobotWorldPosition robotWorldPositionA = robotWorldPositionMapper.selectById(robotPositionA.getWorldPoseId());
                        RobotWorldPosition robotWorldPositionB = robotWorldPositionMapper.selectById(robotPositionB.getWorldPoseId());
                        Double positionX1 = robotWorldPositionA.getPositionX();
                        Double positionY1 = robotWorldPositionA.getPositionY();
                        Double positionX2 = robotWorldPositionB.getPositionX();
                        Double positionY2 = robotWorldPositionB.getPositionY();
                        //A点 当前点位在门禁上方。 斜切式子
                        boolean aUp = targetInterceptFunctionVo.k * positionX1 + targetInterceptFunctionVo.b > positionY1
                                && targetInterceptFunctionVo.k * checkPoint.pose.position.x + targetInterceptFunctionVo.b > checkPoint.pose.position.y;
                        //A点 当前点位在门禁下方。 斜切式子
                        boolean aDown = targetInterceptFunctionVo.k * positionX1 + targetInterceptFunctionVo.b < positionY1
                                && targetInterceptFunctionVo.k * checkPoint.pose.position.x + targetInterceptFunctionVo.b < checkPoint.pose.position.y;
                        log.info("A is up:{},A is down:{}", aUp, aDown);
                        if (aUp || aDown) {
                            log.info("当前路径穿越门禁区域" + targetRobotEntranceGuard.getName());
                            log.info("当前路径穿越门禁内点" + robotPositionA.getName());
                            if (StrUtil.isBlank(RedisUtil.getValue(ENTRANCE_GUARD_ID))) {
                                RedisUtil.setValue(ENTRANCE_GUARD_ID, targetRobotEntranceGuard.getId());
                            }
                            if (StrUtil.isBlank(RedisUtil.getValue(ENTRANCE_POSITION_ID))) {
                                RedisUtil.setValue(ENTRANCE_POSITION_ID, robotPositionA.getId());
                            }
                        }
                        // B 当前点位在门禁上方。 斜切式子
                        boolean bUp = targetInterceptFunctionVo.k * positionX2 + targetInterceptFunctionVo.b > positionY2
                                && targetInterceptFunctionVo.k * checkPoint.pose.position.x + targetInterceptFunctionVo.b > checkPoint.pose.position.y;
                        // B 当前点位在门禁下方。 斜切式子
                        boolean bDown = (targetInterceptFunctionVo.k * positionX2 + targetInterceptFunctionVo.b < positionY2
                                && targetInterceptFunctionVo.k * checkPoint.pose.position.x + targetInterceptFunctionVo.b < checkPoint.pose.position.y);
                        log.info("B is up:{},B is down:{}", bUp, bDown);
                        if (bUp || bDown) {
                            log.info("当前路径穿越门禁区域" + targetRobotEntranceGuard.getName());
                            log.info("当前路径穿越门禁外点" + robotPositionB.getName());
                            if (StrUtil.isBlank(RedisUtil.getValue(ENTRANCE_GUARD_ID))) {
                                RedisUtil.setValue(ENTRANCE_GUARD_ID, targetRobotEntranceGuard.getId());
                            }
                            if (StrUtil.isBlank(RedisUtil.getValue(ENTRANCE_POSITION_ID))) {
                                RedisUtil.setValue(ENTRANCE_POSITION_ID, robotPositionB.getId());
                            }
                        }
                    }
                    RedisUtil.del(ENTRANCE_ACCESS_STATUS_MAP);
                    poseStampeds = new ArrayList<>();
                    targetRobotEntranceGuard = null;
                    targetInterceptFunctionVo = null;
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 初始化斜截式方程
     *
     * @param type        type
     * @param positionAId a
     * @param positionBId b
     * @return 斜截式
     */
    public InterceptFunctionVo initInterceptFunction(Integer type, String positionAId, String positionBId) {
        InterceptFunctionVo interceptFunctionVo = null;
        RobotPosition insidePosition = robotPositionMapper.selectById(positionAId);
        RobotPosition outsidePosition = robotPositionMapper.selectById(positionBId);
        RobotWorldPosition robotWorldPosition1 = robotWorldPositionMapper.selectById(insidePosition.getWorldPoseId());
        RobotWorldPosition robotWorldPosition2 = robotWorldPositionMapper.selectById(outsidePosition.getWorldPoseId());
        Double positionX1 = robotWorldPosition1.getPositionX();
        Double positionY1 = robotWorldPosition1.getPositionY();
        Double positionX2 = robotWorldPosition2.getPositionX();
        Double positionY2 = robotWorldPosition2.getPositionY();
        // 计算两点的直线方程
        if (type == 0) {
            // 待扩展
        }
        // 计算两点垂直线方程
        if (type == 1) {
            double k = -(positionX1 - positionX2) / (positionY1 - positionY2);
            double b = (positionY1 + positionY2) / 2 + (positionX1 - positionX2) / (positionY1 - positionY2) * (positionX1 + positionX2) / 2;
            interceptFunctionVo = new InterceptFunctionVo();
            interceptFunctionVo.k = k;
            interceptFunctionVo.b = b;
        }
        return interceptFunctionVo;
    }

    /**
     * 获取路径相关数据
     *
     * @return _PoseStamped
     */
    public _PoseStamped[] getOriginPath() {
        _PoseStamped[] routePoseList = null;
        String originalPathVisual = RedisUtil.getValue(TOPIC + "::" + TopicConstants.ORIGINAL_PATH_VISUAL);
        if (StrUtil.isNotBlank(originalPathVisual)) {
            _Path path = JSONObject.parseObject(originalPathVisual, _Path.class);
            routePoseList = Objects.requireNonNull(path).poses;
        }
        return routePoseList;
    }

    /**
     * 处理到达门禁点之后
     *
     * @param positionId id
     * @param operate    true:开门禁，false:关门禁
     * @return true:成功，false:失败
     */
    @Override
    public boolean handleArriveEntranceGuardPosition(boolean operate, String positionId) {
        _OriginalPathReq originalPathReq = new _OriginalPathReq();
        // 开门禁
        if (operate) {
            RobotEntranceGuard robotEntranceGuard = robotEntranceGuardMapper.selectOne(new LambdaQueryWrapper<RobotEntranceGuard>()
                    .eq(RobotEntranceGuard::getPositionAId, positionId)
                    .or().eq(RobotEntranceGuard::getPositionBId, positionId));
            RobotPosition targetRobotPosition = null;
            RobotWorldPosition targetWorldPosition = null;
            if (robotEntranceGuard.getPositionAId().equals(positionId)) {
                targetRobotPosition = iRobotPositionService.getById(robotEntranceGuard.getPositionBId());
            }
            if (robotEntranceGuard.getPositionBId().equals(positionId)) {
                targetRobotPosition = iRobotPositionService.getById(robotEntranceGuard.getPositionAId());
            }
            if (null != targetRobotPosition) {
                targetWorldPosition = robotWorldPositionMapper.selectById(targetRobotPosition.getWorldPoseId());
            }
            if (targetRobotPosition != null && targetWorldPosition != null) {
                originalPathReq.cmd = 1;
                originalPathReq.door_type = 0;
                _Pose pose = new _Pose();
                _Pose robotPose = rosWebService.getRobotPose().pose;
                pose.orientation = new _Quaternion();
                pose.orientation.w = targetWorldPosition.getOrientationW();
                pose.orientation.x = targetWorldPosition.getOrientationX();
                pose.orientation.y = targetWorldPosition.getOrientationY();
                pose.orientation.z = targetWorldPosition.getOrientationZ();
                pose.position = new _Point();
                pose.position.x = targetWorldPosition.getPositionX();
                pose.position.y = targetWorldPosition.getPositionY();
                pose.position.z = targetWorldPosition.getPositionZ();
                originalPathReq.goal_pose_info = pose;
                originalPathReq.robot_pose_info = robotPose;
            }
        } else { // 关门禁
            originalPathReq.cmd = 2;
            originalPathReq.door_type = 0;
            _Pose pose = new _Pose();
            _Pose robotPose = rosWebService.getRobotPose().pose;
            pose.orientation = new _Quaternion();
            pose.orientation.w = 0.0;
            pose.orientation.x = 0.0;
            pose.orientation.y = 0.0;
            pose.orientation.z = 0.0;
            pose.position = new _Point();
            pose.position.x = 0.0;
            pose.position.y = 0.0;
            pose.position.z = 0.0;
            originalPathReq.goal_pose_info = pose;
            originalPathReq.robot_pose_info = robotPose;
        }
        String serviceResponse = rosBridgeService.callService(ServiceConstants.ORIGINAL_PATH, Message.getMessageType(_OriginalPathReq.class), JSONObject.toJSONString(originalPathReq));
        if (StrUtil.isNotEmpty(serviceResponse)) {
            _OriginalPathRep originalPathRep = JSON.parseObject(serviceResponse, _OriginalPathRep.class);
            if (originalPathRep.ret == 0) {
                if (operate) {
                    log.info("门禁打开成功");
                } else {
                    log.info("门禁关闭成功");
                }
                iRobotTaskRecordService.updateRobotTaskRecord(NavType.NAV_FINISH_TYPE.getType());
            }
            if (originalPathRep.ret == -1) {
                if (operate) {
                    log.error("门禁打开失败");
                } else {
                    log.error("门禁关闭失败");
                }
                iRobotTaskRecordService.updateRobotTaskRecord(NavType.PLAN_MAKE_TYPE.getType());
                return false;
            }
        } else {
            log.error("门禁服务处理无响应");
            iRobotTaskRecordService.updateRobotTaskRecord(NavType.PLAN_MAKE_TYPE.getType());
            return false;
        }
        return true;
    }

    /**
     * 处理到达门禁点之后
     *
     * @param operate         0:设置门禁点参数，1:开门，2：关门
     * @param positionId      id
     * @param entranceGuardId 门禁点ID
     * @return true:成功，false:失败
     */
    @Override
    public boolean handleArriveEntranceGuardPosition(Short operate, String positionId, String entranceGuardId) {
        log.info("positionId:" + positionId + "     entranceGuardId:" + entranceGuardId);
        String devId = iRobotPositionDevRelationService.getDeviceIdByPositionAndDeviceType(positionId,
                DeviceInfoConstants.DEVICE_TYPE_OTHER,
                DeviceInfoConstants.DEVICE_OTHER_ENTRANCE_GUARD);
        if (StringUtils.isBlank(devId)) {
            devId = RedisUtil.getHash(ROBOT_SYS_INFO, ENTRANCE_CURRENT_OPEN_DEV_ID);
        }
        if (StringUtils.isBlank(devId)) {
            log.info("门禁设备不存在:{}", devId);
            return true;
        }
        Boolean result = false;
        int times = 20;
        do {
            result = iRobotDeviceOperateService.operateDevice(devId, DeviceInfoConstants.DEVICE_OPERATE_SELECT_TYPE, operate);
            if (!result) {
                ThreadUtil.sleep(1000);
                times--;
            }
        } while (!result && times > 0);
        if (ObjectUtil.isNotNull(result)) {
            if (result) {
                if (operate == DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_OPEN) {
                    log.info("门禁打开成功");
                    RedisUtil.hset(ROBOT_SYS_INFO, ENTRANCE_CURRENT_OPEN_DEV_ID, devId);
                } else if (operate == DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_CLOSE) {
                    log.info("门禁关闭成功");
                    RedisUtil.delHash(ROBOT_SYS_INFO, ENTRANCE_CURRENT_OPEN_DEV_ID);
                }
            } else {
                if (operate == DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_OPEN) {
                    log.error("门禁打开失败");
                } else if (operate == DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_CLOSE) {
                    log.error("门禁关闭失败");
                }
                return false;
            }
        } else {
            log.error("门禁服务处理无响应");
            return false;
        }
        return true;
    }

    /**
     * 处理到达门禁点之后 ,并保存记录
     *
     * @param operate         0:设置门禁点参数，1:开门，2：关门
     * @param positionId      id
     * @param entranceGuardId 门禁点ID
     * @return true:成功，false:失败
     */
    @Override
    public boolean handleArriveEntranceGuardPositionAndSaveRecord(Short operate, String positionId, String entranceGuardId) {
        boolean flg = handleArriveEntranceGuardPosition(operate, positionId, entranceGuardId);
        iRobotTaskRecordService.updateRobotTaskRecord(flg ? NavType.NAV_FINISH_TYPE.getType() : NavType.PLAN_MAKE_TYPE.getType());
        return flg;
    }

    /**
     * 开门子线程
     * 使门禁一直敞开，直到机器人结束门禁操作
     *
     * @param positionId      位置ID
     * @param entranceGuardId 门禁ID
     * @return true/false
     */
    @Override
    public boolean threadOpenDoor(String positionId, String entranceGuardId) {
        StringBuffer result = new StringBuffer();
        new Thread(() -> {
            long startTime = System.currentTimeMillis();
            while (true) {
                result.setLength(0);
                ThreadUtil.sleep(500);
                boolean openRes;
                // 如果门禁结束，旧不执行开门操作
                if (StringUtils.isNotBlank(RedisUtil.getValue(ENTRANCE_POSITION_ID))) {
                    openRes = handleArriveEntranceGuardPosition(new Short(DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_OPEN + ""), positionId, null);
                    result.append(openRes);
                } else {
                    log.info("关闭门禁");
                    handleArriveEntranceGuardPosition(new Short(DeviceInfoConstants.ENTRANCE_GUARD_OPERATION_CLOSE + ""), positionId, null);
                    return;
                }
                log.info("开门状态：{}", openRes);
                long endTime = System.currentTimeMillis();
                if (endTime - startTime > 5 * 60 * 1000) {
                    log.info("默认门打开时间结束");
                    return;
                }
                if (openRes) {
                    ThreadUtil.sleep(2000);
                }
                String devId = RedisUtil.getHash(ROBOT_SYS_INFO, ENTRANCE_CURRENT_OPEN_DEV_ID);
                if (StringUtils.isBlank(devId)) {
                    log.info("设备为空，取消门禁操作");
                    return;
                }
            }
        }).start();
        int i = 0;
        do {
            ThreadUtil.sleep(1000);
            i++;
        } while (!Boolean.TRUE.toString().equalsIgnoreCase(ObjectUtil.toString(result)) && 10 > i);
        boolean flg = Boolean.TRUE.toString().equalsIgnoreCase(ObjectUtil.toString(result));
        iRobotTaskRecordService.updateRobotTaskRecord(flg ? NavType.NAV_FINISH_TYPE.getType() : NavType.PLAN_MAKE_TYPE.getType());
        return flg;
    }
}