package com.yx.robot.modules.admin.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.yx.robot.modules.admin.entity.RobotTaskRecord;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 消毒任务记录(供app端展示)
 * <AUTHOR>
 * @date 2020/08/29
 */
@Data
public class RobotDisinfectTaskRecordVo {

    public RobotDisinfectTaskRecordVo(RobotTaskRecord robotTaskRecord) {
        this.id = robotTaskRecord.getId();
        this.startTime = robotTaskRecord.getStartTime();
        this.endTime = robotTaskRecord.getEndTime();
        this.robotLocation = robotTaskRecord.getRobotLocation();
        this.mileage = robotTaskRecord.getMileage();
        this.timeConsume = robotTaskRecord.getTimeConsume();
        this.status = robotTaskRecord.getStatus();
    }

    /**
     * 任务记录id
     */
    private String id;

    /**
     * 任务类型
     */
    private String taskType;

    /**
     * 任务名称
     */
    private String taskName;

    /**
     * 开始时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(timezone = "GMT+8",pattern = "yyyy-MM-dd HH:mm:ss")
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /**
     * 机器人位置信息
     */
    private String robotLocation;

    /**
     * 机器人行驶里程
     */
    private Double mileage;

    /**
     * 任务耗时
     */
    private long timeConsume;

    /**
     * 任务执行状态
     */
    private String status;

    /**
     * 任务执行状态描述
     */
    private String statusText;
}
