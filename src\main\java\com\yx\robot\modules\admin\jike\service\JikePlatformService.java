package com.yx.robot.modules.admin.jike.service;

import com.alibaba.fastjson.JSONObject;
import com.yx.robot.modules.admin.jike.dto.*;
import com.yx.robot.modules.admin.jike.response.GetDisinfectBoxRep;
import com.yx.robot.modules.admin.jike.response.*;

/**
 * 机科平台服务
 *
 * <AUTHOR>
 * @date 2021/12/13
 */
public interface JikePlatformService {


    /**
     * 获取科技化平台token
     *
     * @param loginInfoDto
     * @return
     */
    JSONObject refreshToken(LoginInfoDto loginInfoDto);

    /**
     * 获取消毒仓平台token
     */
    void uvrRefreshToken(UvrLoginDto uvrLoginDto);

    /**
     * 上报机器人任务状态
     */
    void reportRobotWorkStatus(RobotWorkStatusDto robotWorkStatusDto);

    /**
     * 处理任务状态
     */
    void handleRobotWorkStatus(RobotWorkStatusDto robotWorkStatusDto);

    /**
     * 上报机器人自身状态
     */
    void reportRobotStatus(RobotStatusDto robotStatusDto);

    /**
     * 处理机器人自身状态
     */
    void handleRobotStatus();

    /**
     * 获取消毒仓
     */
    GetDisinfectBoxRep getDisinfectBox(GetDisinfectBoxDto getDisinfectBoxDto);

    /**
     * 控制污染区开门
     */
    OpenDisinfectBoxDoorResponse openDisinfectBoxDoor(OpenDisinfectBoxDoorDto openDisinfectBoxDoorDto);

    /**
     * 控制污染区关门
     */
    CloseDisinfectBoxDoorResponse closeDisinfectBoxDoor(CloseDisinfectBoxDoorDto closeDisinfectBoxDoorDto);

    /**
     * UC灯开启请求
     */
    StartDisinfectBoxTaskResponse startDisinfectBoxTask(StartDisinfectBoxTaskDto startDisinfectBoxTaskDto);


    /**
     * UC灯关闭请求
     */
    StopDisinfectBoxTaskResponse stopDisinfectBoxTask(StopDisinfectBoxTaskDto stopDisinfectBoxTaskDto);

    /**
     * 控制清洁区开门
     */
    OpenDisinfectCleanDoorResponse openDisinfectCleanDoor(OpenDisinfectCleanDoorDto openDisinfectCleanDoorDto);

    /**
     * 控制清洁区关门
     */
    CloseDisinfectCleanDoorResponse closeDisinfectCleanDoor(CloseDisinfectCleanDoorDto closeDisinfectCleanDoorDto);

    /**
     * 消毒仓完成通知
     */
    NotifyDisinfectBoxFinishResponse notifyDisinfectBoxFinishResponse(NotifyDisinfectBoxFinishDto notifyDisinfectBoxFinishDto);

    /**
     * 判断房间消杀是否到达或完成
     *
     * @return true 完成/ false 未完成
     */
    boolean roomDisinfectIsStartOrFinish();
}
