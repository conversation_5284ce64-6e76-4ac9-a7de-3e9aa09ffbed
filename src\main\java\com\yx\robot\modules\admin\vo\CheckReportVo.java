package com.yx.robot.modules.admin.vo;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 检测报告视图层
 * <AUTHOR>
 * @date 2020/10/16
 */
@Data
public class CheckReportVo {

    /**
     * 检测厂家
     */
    private String factoryInfo;

    /**
     * 检测日期
     */
    private Date checkDate;

    /**
     * 检测人员
     */
    private String operationUser;

    /**
     * 备注信息
     */
    private String comments;

    /**
     * 检测进度
     */
    private Double checkProgress;

    /**
     * 成功率
     */
    private Double successPercentage;

    /**
     * 失败率
     */
    private Double failPercentage;

    /**
     * 总检测项目
     */
    private Integer nums;

    /**
     * 已检测项目
     */
    private Integer hasCheckedNums;

    /**
     * 待检测项目
     */
    private Integer waitCheckedNums;

    /**
     * 状态
     */
    private boolean status;

    /**
     * 检测条目
     */
    private List<CheckItemVo> checkItems;
}
