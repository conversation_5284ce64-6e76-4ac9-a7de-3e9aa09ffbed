package com.yx.robot.modules.admin.message;

@MessageType(string = "riki_msgs/udrive_auto_dock_state")
public class _UdriveAutoDockState extends Message {

    /**
     * 对接充电桩的阶段
     * <p>
     * F0：激光识别充电桩阶段
     * F1：机器旋转至充电桩预备点
     * F2：行走至充电桩预备点
     * F3：旋转至充电桩预备点方向
     * D1：寻找准确的红外信号
     * D2:后外对接
     * D3:检测充电电流
     * END:充电桩对接完成
     */
    public String stage;

    /**
     * 是否在对接充电桩
     * working：true机器人在运动
     * working：false机器人停止运动
     */
    public boolean working;

    /**
     * 是否充上电
     * <p>
     * charged=false对接充电桩失败
     * charged=true对接充电桩成功
     */
    public boolean charged;

    /**
     * 对接充电桩错误码，正常情况为0
     * 10找不到充电桩
     * 23 ???
     */
    public short error;

    /**
     * 提示信息
     */
    public String msg;
}
