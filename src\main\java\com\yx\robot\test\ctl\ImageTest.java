package com.yx.robot.test.ctl;

import com.alibaba.fastjson.JSON;
import com.yx.robot.common.utils.YmlUtils;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/6/16 16:13
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ImageTest {

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Test
    public void getImgPath() {
        while (true) {
            try {
                System.out.println(iRobotPositionService.getImagePath());
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    @Test
    public void getYml (){
        System.out.println(JSON.toJSONString(YmlUtils.getValue("hk")));
        System.out.println(JSON.toJSONString(YmlUtils.getValue("hk.saveDays")));;
    }
}
