package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.utils.*;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dao.mapper.RobotMapMapper;
import com.yx.robot.modules.admin.dto.RobotMapDto;
import com.yx.robot.modules.admin.dto.YxRobotMapDto;
import com.yx.robot.modules.admin.dto.YxRobotMapInfoDto;
import com.yx.robot.modules.admin.entity.RobotMap;
import com.yx.robot.modules.admin.entity.RobotPosition;
import com.yx.robot.modules.admin.entity.RobotTask;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._LaserScan;
import com.yx.robot.modules.admin.message._MapMetadata;
import com.yx.robot.modules.admin.message._Vector3;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.vo.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.yaml.snakeyaml.Yaml;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.util.*;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.MAP_METADATA;

/**
 * 机器人列表接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotMapServiceImpl extends ServiceImpl<RobotMapMapper, RobotMap> implements IRobotMapService {

    @Autowired
    private RobotMapMapper robotMapMapper;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotRouteService iRobotRouteService;

    @Autowired
    private IRobotAreasService iRobotAreasService;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Value("${robot.file.map-path}")
    private String mapPath;

    @Value("${robot.file.temp-map-path}")
    private String tempMapPath;

    @Value("${robot.file.temp-map}")
    private String tempMap;

    @Value("${robot.file.map-back-suffix}")
    private String mapBackSuffix;

    @Value("${yx-yun.host}")
    private String yxYunHost;

    @Value("${yx-yun.port}")
    private Integer yxYunPort;

    @Value("${yx-yun.context-path}")
    private String yxYunContextPath;

    @Value("${yx-yun.url.mapList}")
    private String yxYunMapList;

    @Value("${yx-yun.username}")
    private String yxYunUsername;

    @Value("${yx-yun.password}")
    private String yxYunPassword;

    @Autowired
    private TaskExecutor taskExecutor;

    @Autowired
    private StringRedisTemplate redisTemplate;


    @Override
    public List<RobotMapDto> getMapList() {
        String[] mapFilesSuffix = {".png", ".yaml", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
        List<RobotMapDto> robotMapDtoList = new ArrayList<>();
        List<RobotMap> robotMaps = this.list();
        //列出所有地图文件和数据库中做比较,
        List<String> fileNames = FileUtil.listFileNames(mapPath);
        if (CollectionUtil.isNotEmpty(robotMaps)) {
            for (RobotMap robotMap : robotMaps) {
                String name = robotMap.getName().replace(".png", "");
                int fileCount = 0;
                RobotMapDto robotMapDto = new RobotMapDto();
                robotMapDto.setId(robotMap.getId());
                robotMapDto.setName(name);
                robotMapDto.setFilePath(robotMap.getFilePath());
                if (CollectionUtil.isNotEmpty(fileNames)) {
                    for (String fileName : fileNames) {
                        if (fileName.equals(name + mapFilesSuffix[0])
                                || fileName.equals(name + mapFilesSuffix[1])
                                || fileName.equals(name + mapFilesSuffix[2])
                                || fileName.equals(name + mapFilesSuffix[3])) {
                            fileCount++;
                        }
                    }
                }
                robotMapDto.setFileCount(fileCount);
                robotMapDtoList.add(robotMapDto);
            }
        }
        return robotMapDtoList;
    }

    /**
     * 获取临时地图列表
     *
     * @return
     */
    @Override
    public Set<String> getTempMapList() {
        String[] mapFilesSuffix = {".png", ".yaml", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
        List<String> fileNames = FileUtil.listFileNames(tempMapPath);
        Set<String> fileList = new LinkedHashSet<>();
        if (CollectionUtil.isNotEmpty(fileNames)) {
            for (String fileName : fileNames) {
                if (!fileName.equals(tempMap.concat(mapFilesSuffix[0]))) {
                    String s = fileName.split("\\.")[0];
                    boolean res1 = fileNames.contains(s + mapFilesSuffix[0]);
                    boolean res2 = fileNames.contains(s + mapFilesSuffix[1]);
                    boolean res3 = fileNames.contains(s + mapFilesSuffix[2]);
                    boolean res4 = fileNames.contains(s + mapFilesSuffix[3]);
                    if (res1 && res2 && res3 && res4) {
                        fileList.add(s + mapFilesSuffix[0]);
                    }
                }
            }
        }
        return fileList;
    }

    /**
     * 获取远程地图列表
     *
     * @return
     */
    @Override
    public List<YxRobotMapDto> getRemoteMapList(String deviceId) {
        List<YxRobotMapDto> data = new ArrayList<>();
        String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + yxYunMapList;
        try {
            JSONObject variables = new JSONObject();
            variables.put("id", deviceId);
            JSONObject result = RestUtil.get(url, variables);
            if (result != null && null != result.get("result")) {
                Object result1 = result.get("result");
                if (null != result1) {
                    data = JSON.parseObject(JSON.toJSONString(result1), List.class);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    /**
     * 编辑地图
     *
     * @param robotMapInfoVo
     * @return
     */
    @Override
    public boolean editMap(RobotMapInfoVo robotMapInfoVo) {
        String[] mapFilesSuffix = {".png", ".yaml", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
        String id = robotMapInfoVo.getId();
        RobotMap robotMap = this.getById(id);
        if (null != robotMap) {
            String filePath = robotMap.getFilePath();
            robotMap.setStoreName(robotMapInfoVo.getStoreName());
            robotMap.setFloor(robotMapInfoVo.getFloor());
            String name = robotMap.getName().replace(".png", "");
            String newMapName = robotMapInfoVo.getMapName();
            String newName = newMapName.replace(".png", "");
            if (!newMapName.equals(robotMap.getName())) {
                try {
                    for (String mapFileSuffix : mapFilesSuffix) {
                        String fullPath = filePath + "/" + name + mapFileSuffix;
                        File file = new File(fullPath);
                        if (!file.exists()) {
                            continue;
                        }
                        // 更换yaml文件里的内容
                        if (mapFileSuffix.indexOf(".yaml") != -1) {
                            CommonUtil.replaceTextContent(fullPath, name, newName);
                        }
                        File localMapFile = new File(fullPath);
                        FileUtil.rename(localMapFile, newName + mapFileSuffix, false, false);
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                    log.error("文件内容替换异常");
                    return false;
                }
            }
            robotMap.setName(newMapName);
            robotMap.setFileName(newMapName);
            boolean b = this.updateById(robotMap);
            return b;
        }
        return false;
    }

    /**
     * 复制地图
     *
     * @param robotMapInfoVo
     * @return
     */
    @Override
    public boolean copyMap(RobotMapInfoVo robotMapInfoVo) {
        String[] mapFilesSuffix = {".png", ".yaml", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
        String id = robotMapInfoVo.getId();
        RobotMap robotMap = this.getById(id);
        String newMapName = robotMapInfoVo.getMapName();
        String newName = newMapName.replace(".png", "");
        String name = robotMap.getName().replace(".png", "");
        String filePath = robotMap.getFilePath();
        if (null != robotMap) {
            try {
                for (String mapFileSuffix : mapFilesSuffix) {
                    String localFullPath = filePath + "/" + name + mapFileSuffix;
                    String destFullPath = filePath + "/" + newName + mapFileSuffix;
                    File localMapFile = new File(localFullPath);
                    File destMapFile = new File(destFullPath);
                    FileUtil.copy(localMapFile, destMapFile, false);
                    // 更换yaml文件里的内容
                    if (mapFileSuffix.indexOf(".yaml") != -1) {
                        CommonUtil.replaceTextContent(destFullPath, name, newName);
                    }
                }
                // 插入新的地图数据
                RobotMap copyRobotMap = new RobotMap();
                copyRobotMap.setName(robotMapInfoVo.getMapName());
                copyRobotMap.setFileName(robotMapInfoVo.getMapName());
                copyRobotMap.setStoreName(robotMapInfoVo.getStoreName());
                copyRobotMap.setFloor(robotMapInfoVo.getFloor());
                copyRobotMap.setFilePath(robotMap.getFilePath());
                copyRobotMap.setIsDefault(FAIL);
                robotMapMapper.insert(copyRobotMap);
                return true;
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    /**
     * 预览地图
     *
     * @param name
     * @return
     */
    @Override
    public void viewMap(String name, Integer type, HttpServletResponse response) {
        response.setContentType("image/png;charset=utf-8");
        OutputStream outputStream = null;
        InputStream inputStream = null;
        String localMapPath = "";
        try {
            if (type == 1) {
                localMapPath = mapPath + "/" + name;
            }
            if (type == 2) {
                localMapPath = tempMapPath + "/" + name;
            }
            inputStream = new BufferedInputStream(new FileInputStream(localMapPath));
            outputStream = response.getOutputStream();
            byte[] buf = new byte[1024];
            int len;
            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
            if (outputStream != null) {
                try {
                    outputStream.close();
                } catch (IOException e) {
                    log.error(e.getMessage(), e);
                }
            }
        }
    }

    /**
     * 删除地图
     */
    @Override
    public Result<Boolean> delMap(String id) {
        try {
            String[] mapFilesSuffix = {".png", ".yaml", ".pbstream", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
            RobotMap robotMap = robotMapMapper.selectById(id);
            if (null != robotMap) {
                String filePath = robotMap.getFilePath();
                String fileName = robotMap.getName();
                String name = fileName.replace(".png", "");
                //从数据库中删除
                robotMapMapper.deleteById(id);
                //如果删除的是当前选择的地图
                if (null != robotMap.getIsDefault() && robotMap.getIsDefault().equals(SUCCESS)) {
                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, CURRENT_MAP, "");
                }
                QueryWrapper queryWrapper = new QueryWrapper();
                queryWrapper.eq("map_id", id);
                //删除标定点
                List<RobotPosition> robotPositionList = iRobotPositionService.list(queryWrapper);
                for (RobotPosition robotPosition : robotPositionList) {
                    iRobotPositionService.delete(robotPosition.getId());
                }

                //删除任务
                List<RobotTask> robotTaskList = iRobotTaskService.list(queryWrapper);
                for (RobotTask robotTask : robotTaskList) {
                    iRobotTaskService.delete(robotTask.getId());
                }
                for (String mapFileSuffix : mapFilesSuffix) {
                    String fullPath = filePath + "/" + name + mapFileSuffix;
                    File file = new File(fullPath);
                    if (!file.exists()) {
                        continue;
                    }
                    FileUtil.del(fullPath);
                }
                //删除路径
                iRobotRouteService.remove(queryWrapper);
                //删除区域
                iRobotAreasService.remove(queryWrapper);
                log.info("地图删除成功");
                return new ResultUtil<Boolean>().setData(true, "地图删除成功");
            } else {
                log.error("地图不存在");
                return new ResultUtil().setErrorMsg("地图不存在");
            }
        } catch (Exception e) {
            e.printStackTrace();
            return new ResultUtil().setErrorMsg("删除地图异常");
        }
    }

    /**
     * 上传地图到云端
     *
     * @param ids
     * @return
     */
    @Override
    public boolean uploadMapsToYun(String ids) {
        String[] mapFilesSuffix = {".png", ".yaml", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
        String[] idArr = ids.split(",");
        String serialNumber = RDes.getSerialNumber();
        if (StrUtil.isEmpty(serialNumber)) {
            log.error("系统未激活");
            return false;
        }
        String mapPath = "/app/yx-yun/data/webapp/maps/" + serialNumber;
        try {
            for (String id : idArr) {
                RobotMap robotMap = this.getById(id);
                String name = robotMap.getName().replace(".png", "");
                for (String mapFileSuffix : mapFilesSuffix) {
                    taskExecutor.execute(() -> {
                        try {
                            String fileName = robotMap.getFilePath() + "/" + name + mapFileSuffix;
                            ShellRpc.uploadFileToRemote(yxYunUsername, yxYunPassword, yxYunHost, fileName, mapPath);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return true;
    }

    /**
     * 从云端下载地图到本地
     *
     * @param mapAddrs
     * @return
     */
    @Override
    public boolean downloadMapsToLocal(String mapAddrs) {
        //文件读取操作放在多线程里执行
        String[] mapFilesSuffix = {".png", ".yaml", mapBackSuffix + ".png", mapBackSuffix + ".yaml"};
        if (StrUtil.isNotEmpty(mapAddrs)) {
            String[] mapAddrArr = mapAddrs.split(",");
            for (String mapAddr : mapAddrArr) {
                String[] mapAddrSplit = mapAddr.split("/");
                String fileNameOrigin = mapAddrSplit[mapAddrSplit.length - 1];
                for (String mapFileSuffix : mapFilesSuffix) {
                    taskExecutor.execute(() -> {
                        try {
                            String fileName = mapAddr + mapFileSuffix;
                            URL httpUrl = new URL(fileName);
                            File localMapFile = new File(mapPath + "/" + fileNameOrigin + mapFileSuffix);
                            if (!localMapFile.exists()) {
                                localMapFile.createNewFile();
                            }
                            FileUtils.copyURLToFile(httpUrl, localMapFile);
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                    });
                }
            }
        }
        return true;
    }

    /**
     * 更新地图
     *
     * @param mapAddr
     * @return
     */
    @Override
    public boolean updateMap(String mapAddr) {
        String mapFileSuffix = ".png";
        if (StrUtil.isNotEmpty(mapAddr)) {
            String[] mapAddrSplit = mapAddr.split("/");
            String fileNameOrigin = mapAddrSplit[mapAddrSplit.length - 1];
            String fileName = mapAddr + mapFileSuffix;
            try {
                URL httpUrl = new URL(fileName);
                File localMapFile = new File(mapPath + "/" + fileNameOrigin + mapFileSuffix);
                if (!localMapFile.exists()) {
                    localMapFile.createNewFile();
                }
                FileUtils.copyURLToFile(httpUrl, localMapFile);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return false;
    }

    /**
     * 楼层列表
     *
     * @return
     */
    @Override
    public List<FloorMapVo> floorList() {
        List<FloorMapVo> floorMapListVoList = new ArrayList<>();
        Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_SERVER_ADDR);
        if (o != null && StrUtil.isNotEmpty(o.toString())) {
            RobotMap one = robotMapMapper.selectById(o.toString());
            if (null != one) {
                List<RobotMap> robotMapList = this.list(new LambdaQueryWrapper<RobotMap>().eq(RobotMap::getStoreName, one.getStoreName()));
                if (CollectionUtil.isNotEmpty(robotMapList)) {
                    for (RobotMap robotMap : robotMapList) {
                        Integer floor = robotMap.getFloor();
                        if (floor != null) {
                            FloorMapVo floorMapVo = new FloorMapVo();
                            floorMapVo.setFloor(floor);
                            floorMapVo.setMapId(robotMap.getId());
                            floorMapListVoList.add(floorMapVo);
                        }
                    }
                }
            }
        }
        return floorMapListVoList;
    }

    /**
     * 获取选中的地图
     *
     * @return
     */
    @Override
    public YxRobotMapInfoDto getSelectMap() {
        YxRobotMapInfoDto yxRobotMapInfoDto = new YxRobotMapInfoDto();
        FileInputStream fileInputStream = null;
        try {
            List<RobotMap> list = this.list(new LambdaQueryWrapper<RobotMap>().eq(RobotMap::getIsDefault, "0"));
            if (CollectionUtil.isNotEmpty(list)) {
                RobotMap robotMap = list.get(0);
                yxRobotMapInfoDto.setId(robotMap.getId());
                String mapName = robotMap.getName();
                yxRobotMapInfoDto.setName(mapName);
                String yamlFileName = mapName.replace("png", "yaml");
                File yamlFile = new File(mapPath + "/" + yamlFileName);
                fileInputStream = new FileInputStream(yamlFile);
                Yaml yaml = new Yaml();
                HashMap hashMap = yaml.loadAs(fileInputStream, HashMap.class);
                Object resolution = hashMap.get("resolution");
                yxRobotMapInfoDto.setResolution(Double.valueOf(resolution.toString()));
                List<Double> origin = (List) hashMap.get("origin");
                double originX = origin.get(0);
                double originY = origin.get(1);
                yxRobotMapInfoDto.setOriginX(originX);
                yxRobotMapInfoDto.setOriginY(originY);
                return yxRobotMapInfoDto;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (ObjectUtil.isNotNull(fileInputStream)) {
                try {
                    fileInputStream.close();
                } catch (Exception e) {
                }
            }
        }
        return null;
    }

    /**
     * 地图参数校准
     *
     * @param robotMapCorrectVo
     * @return
     */
    @Override
    public void correctMapParam(RobotMapCorrectVo robotMapCorrectVo) {
        _Vector3 vector3 = new _Vector3();
        vector3.x = robotMapCorrectVo.getX();
        vector3.y = robotMapCorrectVo.getY();
        vector3.z = robotMapCorrectVo.getAngle();
        rosBridgeService.publish(TopicConstants.MAP_TRANSFORM, Message.getMessageType(_Vector3.class), JSONObject.toJSONString(vector3));
    }

    /**
     * 根据地图名查找地图
     *
     * @param robotMapVo
     * @return
     */
    @Override
    public RobotMap getRobotMap(RobotMapVo robotMapVo) {
        String mapName1 = robotMapVo.getMapName();
//            给传来的地图名加后缀
        String queMapName = mapName1 + ".png";
//            跟据地图名进行查找
        RobotMap queRobotMap = this.getOne(new LambdaQueryWrapper<RobotMap>().eq(RobotMap::getName, queMapName));
        return queRobotMap;
    }

    /**
     * 获取地图雷达信息
     *
     * @return 雷达信息
     */
    @Override
    public _LaserScan getRobotScan() {
        _LaserScan laserScan = null;
        String scan = getRobotScanJson();
        if (StrUtil.isNotBlank(scan)) {
            laserScan = JSONObject.parseObject(scan, _LaserScan.class);
        }
        return laserScan;
    }

    /**
     * 获取地图雷达信息字符串
     *
     * @return 雷达信息
     */
    @Override
    public String getRobotScanJson() {
        return RedisUtil.getTopicValue(TopicConstants.SCAN);
    }

    /**
     * 获取雷达信息
     *
     * @return 雷达简洁信息
     */
    @Override
    public ScanSimple getRobotScanSimple() {
        ScanSimple scanSimple = new ScanSimple();
        _LaserScan laserScan = getRobotScan();
        scanSimple.setAngleMin(laserScan.angle_min);
        scanSimple.setAngleMax(laserScan.angle_max);
        scanSimple.setRanges(laserScan.ranges);
        return scanSimple;
    }

    /**
     * 获取地图数据
     *
     * @return _MapMetadata
     */
    @Override
    public _MapMetadata getMapMetaData() {
        String metadata = RedisUtil.getTopicValue(MAP_METADATA);
        if (StringUtils.isBlank(metadata)) {
            return null;
        }
        return JSONObject.parseObject(metadata, _MapMetadata.class);
    }

    /**
     * 自动重定位功能
     *
     * @return true:成功，false:失败
     */
    @Override
    public boolean autoResetPose() {
        rosBridgeService.callService(ServiceConstants.AUTO_RESET_POSE, "std_srvs/Empty", "{}");
        return true;
    }
}