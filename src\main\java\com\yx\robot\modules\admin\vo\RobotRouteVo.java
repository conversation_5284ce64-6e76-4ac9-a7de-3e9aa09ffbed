package com.yx.robot.modules.admin.vo;

import com.yx.robot.base.BaseEntity;
import com.yx.robot.modules.admin.message._Point;
import com.yx.robot.modules.admin.message._Pose;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class RobotRouteVo extends BaseEntity {

    /**
     * 位置区域
     */
    private String locationInfo;

    /**
     * 位置编号
     */
    private String locationCode;

    /**
     * 地图id
     */
    private String mapId;

    /**
     * 是否默认
     */
    private String isDefault;

    /**
     * 路径点
     */
    List<_Pose> poses;
}
