package com.yx.robot.config;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import static com.yx.robot.common.constant.RabbitMqConstants.*;

/**
 * <AUTHOR>
 * @date 2022/9/7
 * description：RabbitTemplate配置类
 */
@Configuration
public class rabbitMqConfiguration {

    public static String RABBITMQ_HOST;

    public static String PASSWORD;

    @Value("${spring.rabbitmq.host}")
    public void setHost(String host) {
        RABBITMQ_HOST = host;
    }

    @Value("${spring.rabbitmq.password}")
    public void setPassword(String password) {
        PASSWORD = password;
    }

    @Bean
    public RabbitTemplate rabbitTemplate() {
        CachingConnectionFactory connectionFactory = new CachingConnectionFactory(RABBITMQ_HOST,PORT);
        connectionFactory.setUsername(USER_NAME);
        connectionFactory.setPassword(PASSWORD);
        RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
        rabbitTemplate.setExchange(EXCHANGE_NAME);
        return rabbitTemplate;
    }
}
