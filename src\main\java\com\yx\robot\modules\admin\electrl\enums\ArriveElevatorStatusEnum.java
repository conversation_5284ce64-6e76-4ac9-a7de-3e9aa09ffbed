package com.yx.robot.modules.admin.electrl.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/28 11:48
 */
public enum ArriveElevatorStatusEnum {

    /**
     * 异常
     */
    ERROR(0, "异常"),
    /**
     * 当前楼层梯控点
     */
    CURRENT_FLOOR_POINT(1, "当前楼层梯控点"),
    /**
     * 到达目标楼层梯控点
     */
    ARRIVE_TARGET_FLOOR_POINT(2, "到达目标楼层梯控点");

    private final Integer status;

    private final String label;

    ArriveElevatorStatusEnum(Integer status, String label) {
        this.status = status;
        this.label = label;
    }

    public Integer getStatus() {
        return status;
    }

    public String getLabel() {
        return label;
    }
}
