package com.yx.robot.modules.admin.dto;

import com.yx.robot.modules.admin.entity.RobotDisinfect;
import lombok.Data;

import static com.yx.robot.common.constant.RosWebConstants.FAIL;

/**
 * 机器人消毒任务详情
 * <AUTHOR>
 * @date 2020/10/13
 */
@Data
public class RobotDisinfectTaskDetailDto {

    public RobotDisinfectTaskDetailDto(){};

    public RobotDisinfectTaskDetailDto(Integer disinfectType, RobotDisinfect robotDisinfect) {
        this.disinfectType = disinfectType;
        this.spray = robotDisinfect.getSpray();
        this.ulray = robotDisinfect.getUlray();
        this.xt = robotDisinfect.getXt();
        this.disinfectTime = robotDisinfect.getDisinfectTime();
    }

    private Integer disinfectType;

    private String spray = FAIL;

    private String ulray = FAIL;

    private String xt = FAIL;

    private long disinfectTime;
}
