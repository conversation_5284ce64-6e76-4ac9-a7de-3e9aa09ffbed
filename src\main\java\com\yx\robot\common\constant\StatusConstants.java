package com.yx.robot.common.constant;

import java.math.BigDecimal;

/**
 * 用于定义某些参数的默认值或者阈值，
 * 比如说：水箱位置正常满水值
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/3/30 14:29
 */
public interface StatusConstants {

    /**
     * 电池电量阈值：大于该电量时，可以继续任务
     */
    BigDecimal BATTERY_POWER_THRESHOLD_CONTINUE_TASK = new BigDecimal(80);

    /**
     * 电池电量阈值：告警电量，高于该电量时，重置低电量短信发送控制条件
     */
    BigDecimal BATTERY_POWER_CTRL = new BigDecimal(25);

    /**
     * 电池电量阈值：告警电量，当低于该电量时，空闲状态下，自动返回充电  20
     */
    BigDecimal BATTERY_POWER_THRESHOLD_WARNING = new BigDecimal(20);

    /**
     * 电池电量阈值：强制充电电量，当低于该电量时，自动返回充电  15
     */
    BigDecimal BATTERY_POWER_THRESHOLD_FORCE_CHARGE = new BigDecimal(15);

    /**
     * 电池电量阈值：关机，当低于该电量时，自动关机
     */
    BigDecimal BATTERY_POWER_THRESHOLD_CLOSE = new BigDecimal(10);

    /**
     * 电池电量阈值：点阵屏，当等于该电量时，发布表情话题，满电
     */
    BigDecimal BATTERY_POWER_EXPRESSION = new BigDecimal(100);

    /**
     * 电池电量阈值：停止充电，当等于该电量时，自动停止充电
     */
    BigDecimal BATTERY_POWER_THRESHOLD_STOP_CHARING = new BigDecimal(100);

    /**
     * 最低工作电压，低于这个电压后，则返回充电
     */
    BigDecimal BATTERY_POWER_MINIMUM_VOLTAGE = new BigDecimal("2.8");

    /**
     * 电池温度阈值：温度超过55度主动停止充电
     */
    Double TEMPERATURE_STOP_CHARING = 55.0;

}
