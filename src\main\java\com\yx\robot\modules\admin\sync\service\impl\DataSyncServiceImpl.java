package com.yx.robot.modules.admin.sync.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.utils.*;
import com.yx.robot.modules.admin.dto.YxRobotDeviceDataDto;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.jike.constant.JikeConstants;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosWebService;
import com.yx.robot.modules.admin.sync.constant.DataSyncConstant;
import com.yx.robot.modules.admin.sync.service.DataSyncService;
import com.yx.robot.modules.admin.sync.util.SyncRedisUtil;
import com.yx.robot.modules.admin.vo.*;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.util.*;
import java.util.stream.Collectors;

import static com.yx.robot.common.constant.RosWebConstants.*;

/**
 * 数据同步接口实现
 *
 * <AUTHOR>
 * @date 2020/05/17
 */

@Slf4j
@Service
public class DataSyncServiceImpl implements DataSyncService {

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private IRobotTaskRecordService robotTaskRecordService;

    @Autowired
    private IRobotChargingRecordService iRobotChargingRecordService;

    @Autowired
    private IRobotDisinfectTaskDetailService robotDisinfectTaskDetailService;

    @Autowired
    private IRobotSwitchRecordService iRobotSwitchRecordService;

    @Autowired
    private IRobotMapService iRobotMapService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private IRobotWorldPositionService iRobotWorldPositionService;

    @Autowired
    private IRobotLocationService iRobotLocationService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotAreasService iRobotAreasService;

    @Autowired
    private IRobotTaskService iRobotTaskService;

    @Autowired
    private IRobotStatusService iRobotStatusService;

    @Value("${yx-yun.host}")
    private String yxYunHost;

    @Value("${yx-yun.port}")
    private Integer yxYunPort;

    @Value("${yx-yun.context-path}")
    private String yxYunContextPath;

    @Value("${yx-yun.url.syncRobotDeviceDataToYun}")
    private String syncRobotDeviceDataToYun;

    @Value("${yx-yun.url.syncRobotDeviceDataToLocal}")
    private String syncRobotDeviceDataToLocal;

    @Autowired
    private TaskExecutor taskExecutor;

    /**
     * 同步设备信息数据
     */
    @Override
    public void syncDeviceInfoData() {
        Jedis jedis = null;
        try {
            RobotDeviceInfoVo robotDeviceInfo = rosWebService.getRobotDeviceInfo();
            String serialNumber = RDes.getSerialNumber();
            if (StrUtil.isEmpty(serialNumber)) {
                return;
            }
            jedis = SyncRedisUtil.getJedis();
            if (jedis == null) {
                log.info("远程服务器redis 连接异常");
                return;
            }
            String key = DataSyncConstant.DEVICE_DATA_KEY + ":" + serialNumber + ":" + DataSyncConstant.DEVICE_INFO_KEY;
            jedis.hset(key, DataSyncConstant.SERIAL_NUMBER, serialNumber);
            jedis.hset(key, DataSyncConstant.CURRENT_MAP, robotDeviceInfo.getCurrentMap());
            jedis.hset(key, DataSyncConstant.DEVICE_HOST_KEY, robotDeviceInfo.getDeviceHost());
            jedis.hset(key, DataSyncConstant.DEVICE_STATUS_KEY, robotDeviceInfo.getDeviceStatus() + "");
            jedis.hset(key, DataSyncConstant.WORKING_INFO_KEY, robotDeviceInfo.getWorkingInfo());
            jedis.hset(key, DataSyncConstant.BATTERY_KEY, robotDeviceInfo.getBattery() + "");
            jedis.hset(key, DataSyncConstant.MILEAGE_KEY, robotDeviceInfo.getMileage() + "");
            jedis.hset(key, DataSyncConstant.RUN_TIME_KEY, robotDeviceInfo.getRunTime() + "");
            jedis.hset(key, DataSyncConstant.VOLUME_SIZE_KEY, robotDeviceInfo.getVolumeSize() + "");
            jedis.hset(key, DataSyncConstant.SPRAY_WARNING_KEY, robotDeviceInfo.getSprayWarning() + "");
            jedis.hset(key, DataSyncConstant.STOP_KEY, robotDeviceInfo.getStop() + "");
            jedis.hset(key, DataSyncConstant.COLLISION_KEY, robotDeviceInfo.getCollision() + "");
            jedis.hset(key, DataSyncConstant.IDLE_KEY, robotDeviceInfo.getIdle() + "");
            jedis.hset(key, DataSyncConstant.MAPPING_KEY, robotDeviceInfo.getMapping() + "");
            jedis.hset(key, DataSyncConstant.CHARGING_KEY, robotDeviceInfo.getCharging() + "");
            jedis.hset(key, DataSyncConstant.UPDATE_TIME_KEY, DateUtils.formatDate(robotDeviceInfo.getUpdateTime(), "yyyy-MM-dd HH:mm:ss"));
            jedis.hset(key, DataSyncConstant.WORKING_OPERATION_KEY, iRobotStatusService.getOperationStatus().getValue() + "");
        } catch (Exception e) {
            e.printStackTrace();
            log.error("同步数据信息异常");
        } finally {
            SyncRedisUtil.closeJedis(jedis);
        }

    }

    /**
     * 同步任务记录数据
     */
    @Override
    public void syncTaskRecordData() {
        Jedis jedis = null;
        try {
            String serialNumber = RDes.getSerialNumber();
            if (StrUtil.isEmpty(serialNumber)) {
                return;
            }
            jedis = SyncRedisUtil.getJedis();
            if (jedis == null) {
                return;
            }
            // 未上传的任务记录
            List<RobotTaskRecord> robotTaskRecords = robotTaskRecordService
                    .list(new LambdaQueryWrapper<RobotTaskRecord>()
                            .eq(RobotTaskRecord::getSyncStatus, "0")
                            .eq(RobotTaskRecord::getDataStatus, "1")); //未上传已更新
            String key = DataSyncConstant.DEVICE_DATA_KEY + ":" + serialNumber + ":" + DataSyncConstant.TASK_RECORD_KEY;
            // 获取一条数据
            if (CollectionUtil.isNotEmpty(robotTaskRecords)) {
                RobotTaskRecord robotTaskRecord = robotTaskRecords.get(0);
                RobotTaskRecordVo robotTaskRecordVo = new RobotTaskRecordVo(robotTaskRecord);
                //设置任务类型
                RobotTask robotTask = iRobotTaskService.getById(robotTaskRecord.getTaskId());
                if (null != robotTask) {
                    robotTaskRecordVo.setTaskType(robotTask.getType());
                }
                robotTaskRecordVo.setSerialNumber(serialNumber);
                Long lpush = jedis.lpush(key, JSON.toJSONString(robotTaskRecordVo));
                if (lpush > 0) {
                    robotTaskRecord.setSyncStatus("1");
                    robotTaskRecordService.updateById(robotTaskRecord);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            SyncRedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 同步充电记录数据
     */
    @Override
    public void syncChargingRecordData() {
        Jedis jedis = null;
        try {
            String serialNumber = RDes.getSerialNumber();
            if (StrUtil.isEmpty(serialNumber)) {
                return;
            }
            jedis = SyncRedisUtil.getJedis();
            if (jedis == null) {
                return;
            }
            // 未上传的充电记录
            List<RobotChargingRecord> robotChargingRecords = iRobotChargingRecordService
                    .list(new LambdaQueryWrapper<RobotChargingRecord>()
                            .eq(RobotChargingRecord::getSyncStatus, "0")
                            .eq(RobotChargingRecord::getDataStatus, "1")); //未上传已更新
            String key = DataSyncConstant.DEVICE_DATA_KEY + ":" + serialNumber + ":" + DataSyncConstant.CHARGING_RECORD_KEY;
            // 获取一条数据
            if (CollectionUtil.isNotEmpty(robotChargingRecords)) {
                RobotChargingRecord robotChargingRecord = robotChargingRecords.get(0);
                RobotChargingRecordVo robotChargingRecordVo = new RobotChargingRecordVo(robotChargingRecord);
                robotChargingRecordVo.setSerialNumber(serialNumber);
                /*
                Redis Lpush 命令将一个或多个值插入到列表头部。
                如果 key 不存在，一个空列表会被创建并执行 LPUSH 操作。
                当 key 存在但不是列表类型时，返回一个错误。
                 */
                Long lpush = jedis.lpush(key, JSON.toJSONString(robotChargingRecordVo));
                if (lpush > 0) {
                    robotChargingRecord.setSyncStatus("1");
                    iRobotChargingRecordService.updateById(robotChargingRecord);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            SyncRedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 同步开关机记录
     */
    @Override
    public void syncSwitchRecordData() {
        Jedis jedis = null;
        try {
            String serialNumber = RDes.getSerialNumber();
            if (StrUtil.isEmpty(serialNumber)) {
                return;
            }
            jedis = SyncRedisUtil.getJedis();
            if (jedis == null) {
                return;
            }
            //未上传的开关机记录详情
            List<RobotSwitchRecord> robotSwitchRecordList = iRobotSwitchRecordService
                    .list(new LambdaQueryWrapper<RobotSwitchRecord>().eq(RobotSwitchRecord::getSyncStatus, "0"));
            String key = DataSyncConstant.DEVICE_DATA_KEY + ":" + serialNumber + ":" + DataSyncConstant.SWITCH_RECORD_KEY;
            if (CollectionUtil.isNotEmpty(robotSwitchRecordList)) {
                RobotSwitchRecord robotSwitchRecord = robotSwitchRecordList.get(0);
                RobotSwitchRecordVo robotSwitchRecordVo = new RobotSwitchRecordVo(robotSwitchRecord);
                robotSwitchRecordVo.setSerialNumber(serialNumber);
                Long lpush = jedis.lpush(key, JSON.toJSONString(robotSwitchRecordVo));
                if (lpush > 0) {
                    robotSwitchRecord.setSyncStatus("1");
                    iRobotSwitchRecordService.updateById(robotSwitchRecord);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            SyncRedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 同步消毒任务详情
     */
    @Override
    public void syncDisinfectTaskDetailData() {
        Jedis jedis = null;
        try {
            String serialNumber = RDes.getSerialNumber();
            if (StrUtil.isEmpty(serialNumber)) {
                return;
            }
            jedis = SyncRedisUtil.getJedis();
            if (jedis == null) {
                return;
            }
            // 未上传的任务详情记录
            List<RobotDisinfectTaskDetail> robotDisinfectTaskDetails = robotDisinfectTaskDetailService
                    .list(new LambdaQueryWrapper<RobotDisinfectTaskDetail>()
                            .eq(RobotDisinfectTaskDetail::getSyncStatus, "0"));
            String key = DataSyncConstant.DEVICE_DATA_KEY + ":" + serialNumber + ":" + DataSyncConstant.DISINFECT_TASK_DETAIL;
            if (CollectionUtil.isNotEmpty(robotDisinfectTaskDetails)) {
                RobotDisinfectTaskDetail robotDisinfectTaskDetail = robotDisinfectTaskDetails.get(0);
                RobotDisinfectTaskDetailVo robotDisinfectTaskDetailVo = new RobotDisinfectTaskDetailVo(robotDisinfectTaskDetail);
                robotDisinfectTaskDetailVo.setSerialNumber(serialNumber);
                Long lpush = jedis.lpush(key, JSON.toJSONString(robotDisinfectTaskDetailVo));
                if (lpush > 0) {
                    robotDisinfectTaskDetail.setSyncStatus("1");
                    robotDisinfectTaskDetailService.updateById(robotDisinfectTaskDetail);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            SyncRedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 同步机器人设备数据到云端
     *
     * @param mapIds
     * @return
     */
    @Override
    public boolean syncRobotDeviceDataToYun(String mapIds) {
        List<String> mapIdArr = null;
        if (StrUtil.isBlank(mapIds)) {
            mapIdArr = iRobotMapService.list().stream().map(RobotMap::getId).collect(Collectors.toList());
        } else {
            mapIdArr = Arrays.asList(mapIds.split(","));
        }
        for (String mapId : mapIdArr) {
            taskExecutor.execute(() -> {
                try {
                    YxRobotDeviceDataDto yxRobotDeviceDataDto = new YxRobotDeviceDataDto();

                    // 序列号
                    String serialNumber = RDes.getSerialNumber();
                    if (StrUtil.isEmpty(serialNumber)) {
                        log.error("无法获取序列号");
                        return;
                    }

                    yxRobotDeviceDataDto.setSerialNumber(serialNumber);

                    // 地图id
                    yxRobotDeviceDataDto.setMapId(mapId);

                    // 地图名称
                    RobotMap robotMap = iRobotMapService.getById(mapId);
                    String robotMapName = robotMap.getName();
                    yxRobotDeviceDataDto.setMapName(robotMapName);

                    // 地图数据
                    yxRobotDeviceDataDto.setRobotMapData(JSON.toJSONString(robotMap));

                    // 标点数据
                    List<RobotPosition> robotPositionList = iRobotPositionService.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getMapId, mapId));
                    yxRobotDeviceDataDto.setRobotPositionData(JSON.toJSONString(robotPositionList));

                    // 世界坐标数据
                    List<RobotWorldPosition> robotWorldPositionList = new ArrayList<>();
                    // 位置区域映射数据
                    List<RobotLocation> robotLocationList = new ArrayList<>();
                    // 消毒数据
                    List<RobotDisinfect> robotDisinfectList = new ArrayList<>();
                    if (CollectionUtil.isNotEmpty(robotPositionList)) {
                        for (RobotPosition robotPosition : robotPositionList) {
                            RobotWorldPosition robotWorldPosition = iRobotWorldPositionService.getOne(new LambdaQueryWrapper<RobotWorldPosition>().eq(RobotWorldPosition::getId, robotPosition.getWorldPoseId()));
                            RobotLocation robotLocation = iRobotLocationService.getOne(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getPositionId, robotPosition.getId()));
                            if (null != robotLocation) {
                                RobotDisinfect robotDisinfect = iRobotDisinfectService.getOne(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotLocationId, robotLocation.getId()));
                                if (null != robotDisinfect) {
                                    robotDisinfectList.add(robotDisinfect);
                                }
                                robotLocationList.add(robotLocation);
                            }
                            robotWorldPositionList.add(robotWorldPosition);
                        }
                    }
                    yxRobotDeviceDataDto.setRobotWorldPositionData(JSON.toJSONString(robotWorldPositionList));
                    yxRobotDeviceDataDto.setRobotLocationData(JSON.toJSONString(robotLocationList));
                    yxRobotDeviceDataDto.setRobotDisinfectData(JSON.toJSONString(robotDisinfectList));

                    // 任务数据
                    List<RobotTask> robotTaskList = iRobotTaskService.list(new LambdaQueryWrapper<RobotTask>().eq(RobotTask::getMapId, mapId));
                    yxRobotDeviceDataDto.setRobotTaskData(JSON.toJSONString(robotTaskList));


                    // 区域数据
                    List<RobotAreas> robotSpeedList = iRobotAreasService.list(new LambdaQueryWrapper<RobotAreas>().eq(RobotAreas::getMapId, mapId));
                    yxRobotDeviceDataDto.setRobotAreasData(JSON.toJSONString(robotSpeedList));
                    // 调用云端api
                    String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + syncRobotDeviceDataToYun;
                    JSONObject params = JSON.parseObject(JSON.toJSONString(yxRobotDeviceDataDto));
                    JSONObject result = RestUtil.post(url, params);
                    if (result != null && null != result.get("result")) {
                        Object result1 = result.get("result");
                        if (result1.toString().equals("true")) {
                        }
                    }
                } catch (Exception e) {
                }
            });
        }
        return true;
    }

    /**
     * 同步机器人设备数据到本地
     *
     * @param mapIds
     * @return
     */
    @Override
    public boolean syncRobotDeviceDataToLocal(String mapIds) {
        String[] mapIdArr = mapIds.split(",");
        for (String mapId : mapIdArr) {
            try {
                // 序列号
                String serialNumber = RDes.getSerialNumber();
                if (StrUtil.isEmpty(serialNumber)) {
                    log.error("无法获取序列号");
                    continue;
                }
                // 调用云端api
                String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + syncRobotDeviceDataToLocal;
                JSONObject variables = new JSONObject();
                variables.put("mapId", mapId);
                variables.put("serialNumber", serialNumber);
                JSONObject result = RestUtil.get(url, variables);
                if (result != null && null != result.get("result")) {
                    Object result1 = result.get("result");
                    YxRobotDeviceDataDto yxRobotDeviceDataDto = JSON.parseObject(JSON.toJSONString(result1), YxRobotDeviceDataDto.class);
                    // 地图数据
                    String robotMapData = yxRobotDeviceDataDto.getRobotMapData();
                    RobotMap robotMap = JSON.parseObject(robotMapData, RobotMap.class);
                    if (robotMap != null) {
                        iRobotMapService.saveOrUpdate(robotMap);
                    }
                    // 标点位置
                    String robotPositionData = yxRobotDeviceDataDto.getRobotPositionData();
                    List<RobotPosition> robotPositionList = JSONObject.parseArray(robotPositionData, RobotPosition.class);
                    if (CollectionUtil.isNotEmpty(robotPositionList)) {
                        iRobotPositionService.saveOrUpdateBatch(robotPositionList);
                    }
                    // 世界坐标数据
                    String robotWorldPositionData = yxRobotDeviceDataDto.getRobotWorldPositionData();
                    List<RobotWorldPosition> robotWorldPositionList = JSONObject.parseArray(robotWorldPositionData, RobotWorldPosition.class);
                    if (CollectionUtil.isNotEmpty(robotWorldPositionList)) {
                        iRobotWorldPositionService.saveOrUpdateBatch(robotWorldPositionList);
                    }
                    // 位置区域数据
                    String robotLocationData = yxRobotDeviceDataDto.getRobotLocationData();
                    List<RobotLocation> robotLocationList = JSONObject.parseArray(robotLocationData, RobotLocation.class);
                    if (CollectionUtil.isNotEmpty(robotLocationList)) {
                        iRobotLocationService.saveOrUpdateBatch(robotLocationList);
                    }
                    // 消毒数据
                    String robotDisinfectData = yxRobotDeviceDataDto.getRobotDisinfectData();
                    List<RobotDisinfect> robotDisinfectList = JSONObject.parseArray(robotDisinfectData, RobotDisinfect.class);
                    if (CollectionUtil.isNotEmpty(robotDisinfectList)) {
                        iRobotDisinfectService.saveOrUpdateBatch(robotDisinfectList);
                    }
                    // 任务数据
                    String robotTaskData = yxRobotDeviceDataDto.getRobotTaskData();
                    List<RobotTask> robotTaskList = JSONObject.parseArray(robotTaskData, RobotTask.class);
                    if (CollectionUtil.isNotEmpty(robotTaskList)) {
                        iRobotTaskService.saveOrUpdateBatch(robotTaskList);
                    }
                    // 区域数据
                    String robotAreasData = yxRobotDeviceDataDto.getRobotAreasData();
                    List<RobotAreas> robotAreasList = JSONObject.parseArray(robotAreasData, RobotAreas.class);
                    if (CollectionUtil.isNotEmpty(robotAreasList)) {
                        iRobotAreasService.saveOrUpdateBatch(robotAreasList);
                    }
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return true;
    }


    /**
     * 同步订单状态
     */
    @Override
    public void syncOrdersStateDataToYun() {
        Jedis jedis1 = null;
        Jedis jedis2 = null;
        try {
            jedis1 = RedisUtil.getJedis();
            jedis2 = SyncRedisUtil.getJedis();
            // 序列号
            String serialNumber = RDes.getSerialNumber();
            if (jedis2 != null) {
                // 1:未开始 2:进行中 3：已完成 4：未完成 5：失败 6：暂停中
                String key = DataSyncConstant.DEVICE_DATA_KEY + ":" + serialNumber + ":" + DataSyncConstant.DEVICE_INFO_KEY;
                String orderId = jedis1.hget(JikeConstants.JIKE, JikeConstants.ORDER_ID);
                String orderState = jedis1.hget(JikeConstants.JIKE, JikeConstants.ORDER_STATE);
                if (StrUtil.isNotBlank(orderId) && StrUtil.isNotBlank(orderState)) {
                    jedis2.hset(key, orderId, orderState);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis1);
            SyncRedisUtil.closeJedis(jedis2);
        }
    }

    /**
     * 处理设备本体数据(保证和其它设备数据id不一样)
     */
    @Override
    public void handleDeviceSqlData(String param1, String param2) {
        try {
            String serialNumber = RDes.getSerialNumber();
            if (StrUtil.isBlank(serialNumber)) {
                log.info("序列号不能为空");
                return;
            }
            // param1 代表楼栋
            // param2 代表楼层
            serialNumber = param1.concat("-").concat(param2).concat("-").concat(serialNumber);

            // 地图
            List<RobotMap> robotMapList = iRobotMapService.list();
            List<RobotMap> changedRobotMapList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(robotMapList)) {
                for (RobotMap robotMap : robotMapList) {
                    if (StrUtil.isNotBlank(robotMap.getId()) && !robotMap.getId().contains(serialNumber)) {
                        String newId = serialNumber.concat("-").concat(robotMap.getId());
                        robotMap.setId(newId);
                    }
                    changedRobotMapList.add(robotMap);
                }
            }
            iRobotMapService.remove(new LambdaQueryWrapper<>());
            iRobotMapService.saveBatch(changedRobotMapList);

            // 标点数据
            List<RobotPosition> robotPositionList = iRobotPositionService.list();
            List<RobotPosition> changedRobotPositionList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(robotPositionList)) {
                for (RobotPosition robotPosition : robotPositionList) {
                    if (StrUtil.isNotBlank(robotPosition.getId()) && !robotPosition.getId().contains(serialNumber)) {
                        String newId = serialNumber.concat("-").concat(robotPosition.getId());
                        robotPosition.setId(newId);
                    }
                    if (StrUtil.isNotBlank(robotPosition.getMapId()) && !robotPosition.getMapId().contains(serialNumber)) {
                        String newMapId = serialNumber.concat("-").concat(robotPosition.getMapId());
                        robotPosition.setMapId(newMapId);
                    }
                    if (StrUtil.isNotBlank(robotPosition.getParentId()) && !robotPosition.getParentId().contains(serialNumber)) {
                        String newParentId = serialNumber.concat("-").concat(robotPosition.getParentId());
                        robotPosition.setParentId(newParentId);
                    }
                    if (StrUtil.isNotBlank(robotPosition.getWorldPoseId()) && !robotPosition.getWorldPoseId().contains(serialNumber)) {
                        String newWorldPoseId = serialNumber.concat("-").concat(robotPosition.getWorldPoseId());
                        robotPosition.setWorldPoseId(newWorldPoseId);
                    }
                    changedRobotPositionList.add(robotPosition);
                }
            }
            iRobotPositionService.remove(new LambdaQueryWrapper<>());
            iRobotPositionService.saveBatch(changedRobotPositionList);

            // 世界坐标数据
            List<RobotWorldPosition> robotWorldPositionList = iRobotWorldPositionService.list();
            List<RobotWorldPosition> changedRobotWorldPositionList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(robotWorldPositionList)) {
                for (RobotWorldPosition robotWorldPosition : robotWorldPositionList) {
                    if (StrUtil.isNotBlank(robotWorldPosition.getId()) && !robotWorldPosition.getId().contains(serialNumber)) {
                        String newId = serialNumber.concat("-").concat(robotWorldPosition.getId());
                        robotWorldPosition.setId(newId);
                    }
                    if (StrUtil.isNotBlank(robotWorldPosition.getUuId()) && !robotWorldPosition.getUuId().contains(serialNumber)) {
                        String newUuId = serialNumber.concat("-").concat(robotWorldPosition.getUuId());
                        robotWorldPosition.setUuId(newUuId);
                    }
                    changedRobotWorldPositionList.add(robotWorldPosition);
                }
            }
            iRobotWorldPositionService.remove(new LambdaQueryWrapper<>());
            iRobotWorldPositionService.saveBatch(changedRobotWorldPositionList);

            // 位置区域数据
            List<RobotLocation> robotLocationList = iRobotLocationService.list();
            List<RobotLocation> changedRobotLocationList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(robotLocationList)) {
                for (RobotLocation robotLocation : robotLocationList) {
                    if (StrUtil.isNotBlank(robotLocation.getId()) && !robotLocation.getId().contains(serialNumber)) {
                        String newId = serialNumber.concat("-").concat(robotLocation.getId());
                        robotLocation.setId(newId);
                    }
                    if (StrUtil.isNotBlank(robotLocation.getPositionId()) && !robotLocation.getPositionId().contains(serialNumber)) {
                        String newPositionId = serialNumber.concat("-").concat(robotLocation.getPositionId());
                        robotLocation.setPositionId(newPositionId);
                    }
                    if (StrUtil.isNotBlank(robotLocation.getParentId()) && !robotLocation.getParentId().contains(serialNumber)) {
                        String newParentId = serialNumber.concat("-").concat(robotLocation.getParentId());
                        robotLocation.setParentId(newParentId);
                    }
                    changedRobotLocationList.add(robotLocation);
                }
            }
            iRobotLocationService.remove(new LambdaQueryWrapper<>());
            iRobotLocationService.saveBatch(changedRobotLocationList);

            //消毒数据
            List<RobotDisinfect> robotDisinfectList = iRobotDisinfectService.list();
            List<RobotDisinfect> changedRobotDisinfectList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(robotDisinfectList)) {
                for (RobotDisinfect robotDisinfect : robotDisinfectList) {
                    if (StrUtil.isNotBlank(robotDisinfect.getId()) && !robotDisinfect.getId().contains(serialNumber)) {
                        String newId = serialNumber.concat("-").concat(robotDisinfect.getId());
                        robotDisinfect.setId(newId);
                    }
                    if (StrUtil.isNotBlank(robotDisinfect.getRobotLocationId()) && !robotDisinfect.getRobotLocationId().contains(serialNumber)) {
                        String newRobotLocationId = serialNumber.concat("-").concat(robotDisinfect.getRobotLocationId());
                        robotDisinfect.setRobotLocationId(newRobotLocationId);
                    }
                    if (StrUtil.isNotBlank(robotDisinfect.getRobotTaskId()) && !robotDisinfect.getRobotTaskId().contains(serialNumber)) {
                        String newRobotTaskId = serialNumber.concat("-").concat(robotDisinfect.getRobotTaskId());
                        robotDisinfect.setRobotTaskId(newRobotTaskId);
                    }
                    changedRobotDisinfectList.add(robotDisinfect);
                }
            }
            iRobotDisinfectService.remove(new LambdaQueryWrapper<>());
            iRobotDisinfectService.saveBatch(changedRobotDisinfectList);

            // 任务数据
            List<RobotTask> robotTaskList = iRobotTaskService.list();
            List<RobotTask> changedRobotTaskList = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(robotTaskList)) {
                for (RobotTask robotTask : robotTaskList) {
                    if (StrUtil.isNotBlank(robotTask.getId()) && !robotTask.getId().contains(serialNumber)) {
                        String newId = serialNumber.concat("-").concat(robotTask.getId());
                        robotTask.setId(newId);
                    }
                    if (StrUtil.isNotBlank(robotTask.getMapId()) && !robotTask.getMapId().contains(serialNumber)) {
                        String newMapId = serialNumber.concat("-").concat(robotTask.getMapId());
                        robotTask.setMapId(newMapId);
                    }
                    if (StrUtil.isNotBlank(robotTask.getUuId()) && !robotTask.getUuId().contains(serialNumber)) {
                        String newUUid = serialNumber.concat("-").concat(robotTask.getUuId());
                        robotTask.setUuId(newUUid);
                    }
                    changedRobotTaskList.add(robotTask);
                }
            }
            iRobotTaskService.remove(new LambdaQueryWrapper<>());
            iRobotTaskService.saveBatch(changedRobotTaskList);

            // 区域数据
            List<RobotAreas> robotAreasList = iRobotAreasService.list();
            List<RobotAreas> changedRobotAreas = new ArrayList<>();
            if (CollectionUtil.isNotEmpty(robotAreasList)) {
                for (RobotAreas robotAreas : robotAreasList) {
                    if (StrUtil.isNotBlank(robotAreas.getId()) && !robotAreas.getId().contains(serialNumber)) {
                        String newId = serialNumber.concat("-").concat(robotAreas.getId());
                        robotAreas.setId(newId);
                    }
                    if (StrUtil.isNotBlank(robotAreas.getMapId()) && !robotAreas.getMapId().contains(serialNumber)) {
                        String newMapId = serialNumber.concat("-").concat(robotAreas.getMapId());
                        robotAreas.setMapId(newMapId);
                    }
                    changedRobotAreas.add(robotAreas);
                }
            }
            iRobotAreasService.remove(new LambdaQueryWrapper<>());
            iRobotAreasService.saveBatch(changedRobotAreas);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
