package com.yx.robot.modules.admin.scheduler;

import com.yx.robot.common.hk.HkUtils;
import com.yx.robot.common.utils.YxFileUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

/**
 * 系统运维定时处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/9/4 13:46
 */
@Component
@Slf4j
public class SystemOperationHandleScheduler {

    @Scheduled(cron = "0 0 0-10 * * ? ")
    public void saveLastHkFile() {
        log.info("定时删除海康视频文件");
        YxFileUtils.saveLast(HkUtils.robotPath + HkUtils.videoPath);
    }
}
