package com.yx.robot.modules.admin.electrl.enums;

/**
 * 订单状态枚举类
 *
 * <AUTHOR>
 * @date 2021/12/16
 */
public enum OrderStateEnum {

    /**
     * 1,"未开始", 888
     */
    UN_START(1, "未开始", 888),

    /**
     * 2, "进行中", 999
     */
    DOING(2, "进行中", 999),

    /**
     * 3, "已完成", 444
     */
    FINISH(3, "已完成", 444),

    /**
     * 4, "未完成", 555
     */
    UNFINISH(4, "未完成", 555),

    /**
     * 5, "失败", 777
     */
    FAIL(5, "失败", 777),

    /**
     * 6, "暂停中", 666
     */
    STOP(6, "暂停中", 666);

    private Integer value;

    private String label;

    private Integer priority;

    OrderStateEnum(Integer value, String label, Integer priority) {
        this.value = value;
        this.label = label;
        this.priority = priority;
    }

    public Integer getValue() {
        return value;
    }

    public String getLabel() {
        return label;
    }

    public Integer getPriority() {
        return priority;
    }
}
