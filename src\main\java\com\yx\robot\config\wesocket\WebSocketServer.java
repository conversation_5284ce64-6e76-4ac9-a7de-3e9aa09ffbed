package com.yx.robot.config.wesocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CopyOnWriteArraySet;

/**
 * <AUTHOR>
 */
@ServerEndpoint("/yx/websocket/{sid}")
@Component
@Slf4j
public class WebSocketServer {

    //静态变量，用来记录当前在线连接数。应该把它设计成线程安全的。
    private static int onlineCount = 0;
    //concurrent包的线程安全Set，用来存放每个客户端对应的MyWebSocket对象。
    private static CopyOnWriteArraySet<WebSocketServer> webSocketSet = new CopyOnWriteArraySet<WebSocketServer>();

    //与某个客户端的连接会话，需要通过它来给客户端发送数据
    private Session session;
    private static Map<String, Session> sessions = new ConcurrentHashMap<>();

    //接收sid
    public static String sid = "";

    /**
     * 连接建立成功调用的方法
     *
     * @param session
     * @param sid
     */
    @OnOpen
    public void onOpen(Session session, @PathParam("sid") String sid) {
        try {
            this.session = session;
            WebSocketServer.sid = sid;
            sid = sid + "-" + UUID.randomUUID();
            sessions.put(sid, session);
            webSocketSet.add(this);     //加入set中
            addOnlineCount();           //在线数加1
        } catch (Exception e) {
        }
    }


    /**
     * 连接关闭调用的方法
     */
    @OnClose
    public void onClose() {
        webSocketSet.remove(this);  //从set中删除
        subOnlineCount();           //在线数减1
        sessions.remove(session);
    }

    /**
     * 收到客户端消息后调用的方法
     *
     * @param message 客户端发送过来的消息
     */
    @OnMessage
    public void onMessage(String message, Session session) {
        //群发消息
        for (WebSocketServer item : webSocketSet) {
            try {
                item.sendMessage(message);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 关闭连接
     *
     * @param session
     * @param error
     */
    @OnError
    public void onError(Session session, Throwable error) {
        log.error("发生错误:{}", error.getMessage());
        log.error("session.getRequestURI:{}", session.getRequestURI());
        error.printStackTrace();
    }

    /**
     * 实现服务器主动推送
     */
    public void sendMessage(String message) throws IOException {
        if (null != session) {
            synchronized (session) {
                this.session.getBasicRemote().sendText(message);
            }
        }
    }

    public synchronized void sendAllMessage(String message, String name) throws IOException {
//      此处加锁原因：java.lang.IllegalStateException: The remote endpoint was in state [TEXT_FULL_WRITING] which is an invalid state for called method
//      参考地址：https://www.codenong.com/cs109217177/
        synchronized (sessions) {
            for (String sid : sessions.keySet()) {
                Session session = sessions.get(sid);
                if (-1 != sid.indexOf(name)) {
                    synchronized (session) {
                        if (session.isOpen()) {
                            session.getBasicRemote().sendText(message);
                        }
                    }
                }
            }
        }
    }

    public static synchronized void addOnlineCount() {
        WebSocketServer.onlineCount++;
        log.info("当前在线人数：" + WebSocketServer.onlineCount);
    }

    public static synchronized void subOnlineCount() {
        WebSocketServer.onlineCount--;
        log.info("当前在线人数：" + WebSocketServer.onlineCount);
    }
}
