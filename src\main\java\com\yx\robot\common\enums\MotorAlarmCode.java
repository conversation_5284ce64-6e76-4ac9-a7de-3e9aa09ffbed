package com.yx.robot.common.enums;

/**
 * <AUTHOR>
 * @date 2022/9/5
 * description：电机故障码枚举类
 */
public enum MotorAlarmCode {

    /**
     * 此故障码为李宁自定义设定
     */
    ZERO(231,"无法解析故障码"),

    ONE(1,"编码器ABZ报警"),

    TWO(2,"编码器UVW报警"),

    THREE(3,"位置超差"),

    FOUR(4,"失速"),

    FIVE(5,"ADC零点异常"),

    SIX(6,"过载"),

    SEVEN(7,"功率电源欠压"),

    EIGHT(8,"功率电源过压"),

    NINE(9,"过流"),

    TEN(10,"顺时放电报警"),

    ELEVEN(11,"平均放电报警"),

    TWELVE(12,"参数读写异常"),

    THIRTEEN(13,"输入端口重复定义"),

    FOURTEEN(14,"断线保护"),

    FIFTEEN(15,"温度报警");

    private Integer type;

    private String value;

    MotorAlarmCode(Integer type, String value) {
        this.type = type;
        this.value = value;
    }

    public Integer getType() {
        return this.type;
    }

    public String getValue() {
        return value;
    }



}
