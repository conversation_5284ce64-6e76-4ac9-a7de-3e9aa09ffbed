package com.yx.robot.test;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.yx.robot.common.utils.PageUtil;
import com.yx.robot.common.vo.PageVo;
import net.sf.jsqlparser.JSQLParserException;
import net.sf.jsqlparser.parser.CCJSqlParser;
import net.sf.jsqlparser.parser.CCJSqlParserUtil;
import net.sf.jsqlparser.parser.StringProvider;
import net.sf.jsqlparser.schema.Column;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.select.OrderByElement;
import net.sf.jsqlparser.statement.select.PlainSelect;
import net.sf.jsqlparser.statement.select.Select;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/25
 * description：
 */
public class Testss {
    public static void main(String[] args) {
        String originalSql = "SELECT  id,task_record_id,type,start_time,voltage,current,percentage,try_times,condition_type,status,data_status,sync_status,create_by,create_time,update_by,update_time,del_flag  FROM t_robot_charging_record";
        PageVo pageVo = new PageVo();
        pageVo.setOrder("desc");
        pageVo.setSort("start_time");
        pageVo.setPageSize(10);
        IPage iPage = PageUtil.initMpPage(pageVo);
        concatOrderBy(originalSql,iPage);
    }

    public static String concatOrderBy(String originalSql, IPage<?> page) {
        if (CollectionUtils.isNotEmpty(page.orders())) {
            try {
                List<OrderItem> orderList = page.orders();
                System.out.println("orderList");
                Statement statement = parse(originalSql);
                System.out.println(JSON.toJSON(statement));
                Select selectStatement = (Select) CCJSqlParserUtil.parse(originalSql);

                System.out.println(".................");

                PlainSelect plainSelect = (PlainSelect) selectStatement.getSelectBody();
                List<OrderByElement> orderByElements = plainSelect.getOrderByElements();
                if (orderByElements == null || orderByElements.isEmpty()) {
                    orderByElements = new ArrayList<>(orderList.size());
                }
                for (OrderItem item : orderList) {
                    OrderByElement element = new OrderByElement();
                    element.setExpression(new Column(item.getColumn()));
                    element.setAsc(item.isAsc());
                    orderByElements.add(element);
                }
                plainSelect.setOrderByElements(orderByElements);
                return plainSelect.toString();
            } catch (JSQLParserException e) {
                System.out.println("failed to concat orderBy from IPage, exception=" + e.getMessage());
            }
        }
        return originalSql;
    }

    public static Statement parse(String sql) throws JSQLParserException {
        CCJSqlParser parser = new CCJSqlParser(new StringProvider(sql));
        try {
            if (ObjectUtil.isNull(parser)){
                System.out.println("parser");
            }
            return parser.Statement();
        } catch (Exception ex) {
            System.out.println(ex.getMessage());
            throw new JSQLParserException(ex);
        }
    }
}
