package com.yx.robot.modules.admin.jike.constant;

import com.yx.robot.modules.admin.jike.enums.ProfileEnum;

/**
 * API 接口
 * <AUTHOR>
 * @date 2021/12/13
 */
public interface ApiConstants {

    Integer profile = ProfileEnum.PROD.getValue();

    String GET_TOKEN_BY_TOKEN = "Basic c3VwZXJ1c2VyOjEyMzQ1Ng==";

    String TOKEN_HEADER = "token";

    String TOKEN_PREFIX = "Bearer ";

    /**
     * 科技化平台
     */
    String BASE_TOKEN_URL = "http://192.168.15.159:32096";

    /**
     * 科技化平台
     */
    String BASE_URL = "http://192.168.15.159:32096";

    /**
     * 消毒仓平台(赛特)
     */
    String UVR_BASE_URL_1 = "http://192.168.15.150:22000";

    /**
     * 消毒仓平台（优必选）
     */
    String UVR_BASE_URL_2 = "http://192.168.15.105:21120";


    String REFRESH_TOKEN =  (profile.equals(ProfileEnum.PROD.getValue()) ? "/ifaas-authority" : "")  + "/oauth/token";

    String UVR_REFRESH_TOKEN = "/v1/uvr/auth";

    String ROBOT_WORK_STATUS_REPORT = (profile.equals(ProfileEnum.PROD.getValue()) ? "/ifaas-hotel-robot-platform" : "")  + "/api/hotel/tech/robot/work/status/report";

    String ROBOT_STATUS_REPORT = (profile.equals(ProfileEnum.PROD.getValue()) ? "/ifaas-hotel-robot-platform" : "")  + "/api/hotel/tech/robot/status/report";

    String GET_DISINFECT_BOX = (profile.equals(ProfileEnum.PROD.getValue()) ? "/ifaas-hotel-robot-platform" : "")  + "/api/hotel/tech/robot/disinfect/request/create";

    String OPEN_DISINFECT_BOX_DOOR = "/v1/uvr/unclean-door/open";

    String CLOSE_DISINFECT_BOX_DOOR = "/v1/uvr/unclean-door/close";

    String START_DISINFECT_BOX_TASK = "/v1/uvr/disinfect/start";

    String STOP_DISINFECT_BOX_TASK = "/v1/uvr/disinfect/stop";

    String OPEN_DISINFECT_CLEAN_DOOR = "/v1/uvr/clean-door/open";

    String CLOSE_DISINFECT_CLEAN_DOOR = "/v1/uvr/clean-door/close";

    String NOTIFY_DISINFECT_BOX_FINISH = (profile.equals(ProfileEnum.PROD.getValue()) ? "/ifaas-hotel-robot-platform" : "")  + "/api/hotel/tech/robot/disinfect/robot/notify";

}
