package com.yx.robot.config.mqtt;

import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.electrl.constant.ElevatorTopicConstants;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.*;
import redis.clients.jedis.Jedis;

/**
 * mqtt消息处理类
 */
@Slf4j
public class TopicMsgCallback implements MqttCallback {

    private MqttClient client;
    private MqttConnectOptions options;
    private String[] topic;
    private int[] qos;

    public TopicMsgCallback() {}

    public TopicMsgCallback(MqttClient client, MqttConnectOptions options, String[] topic, int[] qos) {
        this.client = client;
        this.options = options;
        this.topic = topic;
        this.qos = qos;
    }

    /**
     * 断开重连
     * @param throwable
     */
    @Override
    public void connectionLost(Throwable throwable) {
        log.info("MQTT连接断开，发起重连");
        while(true) {
            try {
                Thread.sleep(30000);
                client.connect(options);
                //订阅消息
                client.subscribe(topic,qos);
                log.info("MQTT重新连接成功:"+client);
                break;
            } catch (Exception e) {
                e.printStackTrace();
                continue;
            }
        }
    }

    /**
     * 消息处理
     * @param s
     * @param mqttMessage
     * @throws Exception
     */
    @Override
    public void messageArrived(String s, MqttMessage mqttMessage) throws Exception {
        log.info("topic:{},qos:{},msg:{}",s, mqttMessage.getQos(), new String(mqttMessage.getPayload()));
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {

    }
}
