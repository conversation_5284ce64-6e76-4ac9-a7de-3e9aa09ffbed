package com.yx.robot.modules.admin.serviceimpl.core;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yx.robot.common.constant.SecurityConstant;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.*;
import com.yx.robot.common.utils.*;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.InitService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.core.task.TaskExecutor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.List;
import java.util.Random;
import java.util.concurrent.*;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.SecurityConstant.CODE_VAL;
import static com.yx.robot.common.constant.TopicConstants.*;

/**
 * 初始化相关接口
 *
 * <AUTHOR>
 * @date 2020/09/19
 */
@Transactional(rollbackFor = RuntimeException.class)
@Slf4j
@Service
public class InitServiceImpl implements InitService {

    @Autowired
    private StringRedisTemplate redisTemplate;
    @Autowired
    private IRobotMapService iRobotMapService;
    @Autowired
    private TaskExecutor taskExecutor;
    @Autowired
    private RosWebService rosWebService;
    @Autowired
    private IRobotPositionService robotPositionService;
    @Autowired
    private IRobotWorldPositionService iRobotWorldPositionService;
    @Autowired
    private RosBridgeService rosBridgeService;
    @Autowired
    private IRobotAreasService iRobotAreasService;
    @Autowired
    private IRobotDefineService iRobotDefineService;

    @Value("${robot.file.map-path}")
    private String mapPath;

    @Value("${robot.file.map-back-suffix}")
    private String mapBackSuffix;

    @Value("${yx-yun.host}")
    private String yxYunHost;

    @Value("${yx-yun.port}")
    private Integer yxYunPort;

    @Value("${yx-yun.context-path}")
    private String yxYunContextPath;

    @Value("${yx-yun.url.sceneSpeech}")
    private String yxYunSceneSpeechUrl;

    @Value("${yx-yun.url.serverId}")
    private String yxYunServerId;

    @Autowired
    private IpInfoUtil ipInfoUtil;

    @Value("${yx.speechType}")
    private String speechType;

    private static final ExecutorService executorService = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors() * 2);

    /**
     * 初始化服务地点id
     */
    @Override
    public void initServerId() {
//        String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + yxYunServerId;
        try {
            JSONObject variables = new JSONObject();
            String serialNumber = RDes.getSerialNumber();
            variables.put("serialNumber", serialNumber);
//            JSONObject result = RestUtil.get(url, variables);
//            if(result != null && null != result.get("result")){
//                Object result1 = result.get("result");
//                if(null != result1) {
//                    String data = result1.toString();
//                    redisTemplate.opsForHash().put(ROBOT_SYS_INFO, SERVER_ID,data);
//                }
//            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void initRos() {
        try {
            String host = ipInfoUtil.getIp();
            boolean b = rosBridgeService.rosOnline();
            if (b) {
                log.info(String.format("成功连接到主机地址为：%s的ROS MASTER", host));
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, HOST, host);
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, ROS_STATUS, SUCCESS);
            } else {
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, ROS_STATUS, FAIL);
            }
            //电机的矢能和释能
//            rosWebService.motorControl(true);
//            Thread.sleep(3000);
//            rosWebService.motorControl(false);
            iRobotAreasService.initRobotAreas(null);
            // 打开广告屏
            rosWebService.adControl(true);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Override
    public void initMap() {
        try {
            //列出所有地图文件和数据库中做比较
            List<String> fileNames = FileUtil.listFileNames(mapPath);
            if (CollectionUtil.isNotEmpty(fileNames)) {
                for (String fileName : fileNames) {
                    if (fileName.endsWith(".png") && fileName.indexOf(mapBackSuffix) == -1) {
                        RobotMap one = iRobotMapService.getOne(new LambdaQueryWrapper<RobotMap>().eq(RobotMap::getName, fileName));
                        RobotMap target = new RobotMap();
                        if (one == null) {
                            target.setFileName(fileName);
                            target.setFilePath(mapPath);
                            target.setName(fileName);
                            target.setFloor(1); // 默认1楼
                            //是否默认
                            target.setIsDefault(FAIL);
                            target.setCreateTime(new Date());
                            iRobotMapService.save(target);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.error("获取默认异常");
        }
    }

    @Override
    public void initTopic() {
        rosBridgeService.subscribe(TopicConstants.SYSTEM_CHECK, Message.getMessageType(_SystemCheck.class));
        
        //机器人的实时位置,设置5秒过期，如果没有数据则表示定位程序异常。
        rosBridgeService.subscribe(TopicConstants.ROBOT_POSE_STAMPED, Message.getMessageType(_PoseStamped.class),5);

        rosBridgeService.subscribe(TopicConstants.ROBOT_POSE, Message.getMessageType(_Pose.class),5);
        //机器人雷达数据
        rosBridgeService.subscribe(TopicConstants.SCAN, Message.getMessageType(_LaserScan.class));
        //机器人路径
//        rosBridgeService.subscribe(TopicConstants.PATH_RECORD, Message.getMessageType(_Path.class), 2);
        //防撞条
        rosBridgeService.subscribe(TopicConstants.COLLISION, Message.getMessageType(_Bool.class));
        //导航状态
        rosBridgeService.subscribe(TopicConstants.NAV_STATUS, Message.getMessageType(_RobotNavStatus.class));
        //电源管理
        rosBridgeService.subscribe(TopicConstants.BATTERY_STATE, Message.getMessageType(_BatteryState.class));
        //电源健康状态管理
        //rosBridgeService.subscribe(TopicConstants.BATTERY_HEALTH_STATE, Message.getMessageType(_BatteryHealthState.class));
        //Point Marker
        rosBridgeService.subscribe(TopicConstants.POINT_MARKER, Message.getMessageType(_Marker.class));
        //急停开关
        rosBridgeService.subscribe(TopicConstants.EMERGENCY_BUTTON, Message.getMessageType(_Bool.class));
        //关机状态
        rosBridgeService.subscribe(TopicConstants.SYSTEM_CHARGE, Message.getMessageType(_Uint8.class));
        //充电头状态
        rosBridgeService.subscribe(TopicConstants.INFRARED_STATUS, Message.getMessageType(_Infrared.class));
        //自动充电状态
        rosBridgeService.subscribe(TopicConstants.AUTO_DOCK_STATE, Message.getMessageType(_AutoDockState.class));
        // 路径记录
        rosBridgeService.subscribe(TopicConstants.ORIGINAL_PATH_VISUAL, Message.getMessageType(_Path.class));
        // 充电桩检测
        rosBridgeService.subscribe(TopicConstants.GET_DOCK, Message.getMessageType(_Bool.class));
        // 梯控任务反馈
//        rosBridgeService.subscribe(TopicConstants.ELEVATOR_TASK_FEEDBACK,Message.getMessageType(_ElevatorTaskFeedback.class));
        //消毒液的量
        rosBridgeService.subscribe(TopicConstants.DISINFECTANT, Message.getMessageType(_Disinfectant.class));
        //喷雾液位状态
        rosBridgeService.subscribe(TopicConstants.SPRAY_WIQUID_STATUS, Message.getMessageType(_Bool.class));
        //消毒按键状态
        rosBridgeService.subscribe(TopicConstants.DOCTOR_KEY_STATUS, Message.getMessageType(_Bool.class));
        //活物检测
        rosBridgeService.subscribe(TopicConstants.FIND_PEOPLE, Message.getMessageType(_Bool.class));
        //风扇状态
        rosBridgeService.subscribe(TopicConstants.FAN_STATUS, Message.getMessageType(_Bool.class));
        //紫外线保护状态
        rosBridgeService.subscribe(TopicConstants.SHIELDING_STATUS, Message.getMessageType(_Bool.class));
        //紫外线状态
        rosBridgeService.subscribe(TopicConstants.ULRAY_STATUS, Message.getMessageType(_Bool.class));
        //脉冲灯状态
        rosBridgeService.subscribe(TopicConstants.PULSE_STATUS, Message.getMessageType(_Bool.class));
        //喷雾状态
        rosBridgeService.subscribe(TopicConstants.SPRAY_STATUS, Message.getMessageType(_Bool.class));
        //消毒液进水控制状态
        rosBridgeService.subscribe(TopicConstants.WATER_IN_STATUS, Message.getMessageType(_Uint8.class));
        //消毒液出水控制状态
        rosBridgeService.subscribe(TopicConstants.WATER_OUT_STATUS, Message.getMessageType(_Uint8.class));
        //消毒模式状态
        rosBridgeService.subscribe(TopicConstants.DOCTOR_MODE_STATE, Message.getMessageType(_DoctorModeStatus.class));

        //梯控返回结果
        rosBridgeService.subscribe(TopicConstants.ELEVATOR_TASK_FEEDBACK, Message.getMessageType(_ElevatorTaskFeedback.class));

        // 是否是高液位
        rosBridgeService.subscribe(TopicConstants.SPRAY_LIQUID_OVERFLOW, Message.getMessageType(_Bool.class));

        // 监听方向控制话题
        rosBridgeService.subscribe(DIRECTIONAL_CONTROL, Message.getMessageType(_DirectionalControl.class));

        // 地图相关
        rosBridgeService.subscribe(MAP_METADATA, Message.getMessageType(_MapMetadata.class));

        //语音交流
        rosBridgeService.subscribeTalkTopic(TopicConstants.TALK_SCENE, Message.getMessageType(_Talk.class));

        //点阵表情话题订阅
        rosBridgeService.subscribeExpressionTopic(TopicConstants.EXPRESSION_CONTROL,Message.getMessageType(_Uint8.class));

        //对接充电桩实时状态
        rosBridgeService.subscribe(UDRIVE_AUTO_DOCK_STATE, Message.getMessageType(_UdriveAutoDockState.class));

        // 电机数据话题订阅
        rosBridgeService.subscribe(MOTOR_DATA,Message.getMessageType(_MotorData.class));

        //防跌落话题订阅
        rosBridgeService.subscribe(MOTOR_LOCK, Message.getMessageType(_MotorLock.class));

        // 电池健康状态话题订阅
        rosBridgeService.subscribe(WHOLE_BATTERY_STATE,Message.getMessageType(_BatteryHealthState.class));

        // 充电片接触状态话题
        rosBridgeService.subscribe(SYSTEM_CHARGE_STATE,Message.getMessageType(_Bool.class));

        // 防跌落开启状态话题
        rosBridgeService.subscribe(ROBOT_FALL_STATE,Message.getMessageType(_Bool.class));

        // 底层板低功耗状态话题
        rosBridgeService.subscribe(LOW_POWER_STATE,Message.getMessageType(_Bool.class));

        // 路径信息话题订阅
//        rosBridgeService.subscribe(PATH_RECORD,Message.getMessageType(_Path.class));

        // 液位传感器状态话题订阅
        rosBridgeService.subscribe(WATER_LEVEL_STATE,Message.getMessageType(_Bool.class));

        // 语音对话功能状态话题订阅
        rosBridgeService.subscribe(VOICE_MODEL_STATE,Message.getMessageType(_Bool.class));

        log.info("初始化话题完成");
    }

    /**
     * 计算运行时间
     */
    @Override
    public void sumRunTime() {
        // 周期1分钟计算一次
        final int period = 1000 * 60;
        taskExecutor.execute(() -> {
            double result = 0.00;
            while (true) {
                Jedis jedis = null;
                try {
                    jedis = RedisUtil.getJedis();
                    ThreadUtil.sleep(period);
                    String runtime = jedis.hget(ROBOT_SYS_INFO, RUNTIME);
                    if (runtime == null) {
                        result += 1;
                    } else {
                        result = Double.parseDouble(runtime) * 60 + 1;
                    }
                    String resultStr = String.format("%.2f", result / 60);
                    jedis.hset(ROBOT_SYS_INFO, RUNTIME, resultStr);
                } catch (Exception e) {
                    e.printStackTrace();
                    log.error("计算运行时间异常");
                } finally {
                    RedisUtil.closeJedis(jedis);
                }
            }
        });
        log.info("开始计算运行时间");
    }

    @Override
    public void initTask() {
        redisTemplate.opsForHash().put(TASK_INFO, NEXT_LOCATION_INFO, "");
        redisTemplate.opsForHash().put(TASK_INFO, NEXT_LOCATION_CODE, "");
        redisTemplate.opsForHash().put(TASK_INFO, IS_RUNNING, "false");
        redisTemplate.opsForHash().put(TASK_INFO, IS_STOPPING, "false");
        redisTemplate.opsForHash().put(TASK_INFO, TASK_TYPE, "");
        redisTemplate.opsForHash().put(TASK_INFO, NEXT_TARGET, "");
        redisTemplate.opsForHash().put(TASK_INFO, IS_STOPPING, "false");

        //消毒相关目标点
        redisTemplate.opsForHash().put(TASK_INFO, IS_ULRAY, "false");
        redisTemplate.opsForHash().put(TASK_INFO, IS_SPRAY, "false");
        redisTemplate.opsForHash().put(TASK_INFO, IS_XT, "false");
        redisTemplate.opsForHash().put(TASK_INFO, DISINFECT_TIME, "");
        redisTemplate.delete(TO_DO_LIST);
        redisTemplate.delete(ROUTE_TO_DO_LIST);
        redisTemplate.delete(ENTRANCE_GUARD_ID);
        redisTemplate.delete(ENTRANCE_POSITION_ID);
        redisTemplate.delete(MONITOR_INFO);
        redisTemplate.opsForHash().delete(ROBOT_SYS_INFO, CURRENT_TASK_ID);
        redisTemplate.opsForValue().set(SecurityConstant.OPERATION_CODE, CODE_VAL);
        redisTemplate.opsForValue().set(SecurityConstant.FETCH_CODE, CODE_VAL);
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, PRE_SHUTDOWN, "false");
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, END_CHARGING, "false");
        redisTemplate.opsForHash().put(ROBOT_SYS_INFO, AUTO_CHARGING_STATUS, AutoChargingStatus.UNDO.getValue().toString());
        redisTemplate.opsForHash().delete(ROBOT_SYS_INFO, MANUAL_DISINFECT);
        redisTemplate.opsForHash().delete(ROBOT_SYS_INFO, LINE_DISINFECT);
        redisTemplate.opsForHash().delete(TASK_INFO, ROUTE_DISINFECT_START_TIME);
        redisTemplate.opsForHash().delete(ROBOT_SYS_INFO, RING_LIGHT_STATE);
        redisTemplate.opsForHash().delete(ROBOT_SYS_INFO, POSITION_STATUS);
        redisTemplate.opsForHash().delete(ROBOT_SYS_INFO, LOW_POWER_STATUS);
        log.info("初始化任务完成");
    }

    /**
     * 重定位
     *
     * @param firstInit
     * @return
     */
    @Override
    public boolean initPose(boolean firstInit, _Pose pose) {
        try {
            List<RobotPosition> robotPositionList = null;
            if (pose == null) {
                Object currentMap = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP);
                if (null == currentMap || currentMap.toString().isEmpty()) {
                    log.error("未获取当前加载地图，无法进行重定位");
                    return false;
                }
                robotPositionList = robotPositionService.list(new LambdaQueryWrapper<RobotPosition>()
                        .eq(RobotPosition::getMapId, currentMap.toString())
                        .eq(RobotPosition::getType, RobotPositionType.START_POSITION.getType()));
                if (CollectionUtil.isEmpty(robotPositionList)) {
                    log.error("未查询到定位点，无法进行重定位");
                    return false;
                }
            }
            // 开启位置控制
            RobotWorldPosition robotWorldPosition = null;
            rosWebService.positionCtrl(true);
            if (CollectionUtil.isNotEmpty(robotPositionList)) {
                RobotPosition robotPosition = robotPositionList.get(robotPositionList.size() - 1);
                robotWorldPosition = iRobotWorldPositionService.getById(robotPosition.getWorldPoseId());
            }
            //发送世界坐标位置
            _PoseWithCovarianceStamped poseWithCovarianceStamped = new _PoseWithCovarianceStamped();
            poseWithCovarianceStamped.header = new Header();
            poseWithCovarianceStamped.header.frame_id = "map";
            poseWithCovarianceStamped.header.seq = new Random().nextInt(10);
            poseWithCovarianceStamped.header.stamp = new TimePrimitive();
            poseWithCovarianceStamped.header.stamp.secs = 0;
            poseWithCovarianceStamped.header.stamp.nsecs = 0;
            poseWithCovarianceStamped.pose = new _PoseWithCovariance();
            poseWithCovarianceStamped.pose.pose = new _Pose();
            poseWithCovarianceStamped.pose.pose.position = new _Point();
            if (pose != null) {
                poseWithCovarianceStamped.pose.pose.position.x = pose.position.x;
                poseWithCovarianceStamped.pose.pose.position.y = pose.position.y;
                poseWithCovarianceStamped.pose.pose.position.z = pose.position.z;
                poseWithCovarianceStamped.pose.pose.orientation = new _Quaternion();
                poseWithCovarianceStamped.pose.pose.orientation.w = pose.orientation.w;
                poseWithCovarianceStamped.pose.pose.orientation.x = pose.orientation.x;
                poseWithCovarianceStamped.pose.pose.orientation.y = pose.orientation.y;
                poseWithCovarianceStamped.pose.pose.orientation.z = pose.orientation.z;
            } else {
                if (robotWorldPosition != null) {
                    poseWithCovarianceStamped.pose.pose.position.x = robotWorldPosition.getPositionX();
                    poseWithCovarianceStamped.pose.pose.position.y = robotWorldPosition.getPositionY();
                    poseWithCovarianceStamped.pose.pose.position.z = robotWorldPosition.getPositionZ();
                    poseWithCovarianceStamped.pose.pose.orientation = new _Quaternion();
                    poseWithCovarianceStamped.pose.pose.orientation.w = robotWorldPosition.getOrientationW();
                    poseWithCovarianceStamped.pose.pose.orientation.x = robotWorldPosition.getOrientationX();
                    poseWithCovarianceStamped.pose.pose.orientation.y = robotWorldPosition.getOrientationY();
                    poseWithCovarianceStamped.pose.pose.orientation.z = robotWorldPosition.getOrientationZ();
                }
            }
            double[] covariance = new double[]{0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0};
            if (firstInit) {
                covariance = new double[]{0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.25, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.04};
            }
            poseWithCovarianceStamped.pose.covariance = covariance;
            log.info("重定位命令发送成功");
//            RedisUtil.hset(ROBOT_SYS_INFO, POSITION_STATUS, FAIL);
            rosBridgeService.publish(INIT_POSE, Message.getMessageType(_PoseWithCovarianceStamped.class), JSON.toJSONString(poseWithCovarianceStamped));
            rosBridgeService.publish(INIT_ANGLE, Message.getMessageType(_PoseWithCovarianceStamped.class), JSON.toJSONString(poseWithCovarianceStamped));
//            long startTime = System.currentTimeMillis();
//            new Thread(() -> {
//                while (true) {
//                    RedisUtil.delTopicValue(ROBOT_POSE);
//                    ThreadUtil.sleep(2000);
//                    if (StringUtils.isNotBlank(RedisUtil.getTopicValue(ROBOT_POSE))) {
//                        RedisUtil.hset(ROBOT_SYS_INFO, POSITION_STATUS, SUCCESS);
//                        break;
//                    }
//                    if (System.currentTimeMillis() - startTime > 60 * 2 * 1000) {
//                        break;
//                    }
//                }
//            }).start();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            log.error("重定位异常");
            return false;
        } finally {
            rosWebService.positionCtrl(false);
        }
    }

    /**
     * 初始化场景语音列表
     */
    @Override
    public void initSceneSpeech() {
        String url = "http://" + yxYunHost + ":" + yxYunPort + yxYunContextPath + yxYunSceneSpeechUrl;
        JSONObject variables = new JSONObject();
        String serialNumber = RDes.getSerialNumber();
        variables.put("serialNumber", serialNumber);
        JSONObject result = null;
        Future<JSONObject> future = null;
        try {
            future = executorService.submit(() -> {
                try {
                    return RestUtil.get(url, variables);
                } catch (Exception e) {
                    e.printStackTrace();
                }
                return null;
            });
            result = future.get(10000, TimeUnit.MILLISECONDS);
            if (result != null && null != result.get("result")) {
                Object result1 = result.get("result");
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, SPEECH_LIST, JSON.toJSONString(result1));
            }
            Object o = redisTemplate.opsForHash().get(ROBOT_SYS_INFO, SPEECH_LIST);
            if (o == null || StrUtil.isBlank(o.toString()) || o.toString().equals("[]")) {
                ClassPathResource classPathResource = new ClassPathResource(speechType);
                InputStream inputStream = classPathResource.getInputStream();
                String readJson = IOUtils.toString(inputStream, StandardCharsets.UTF_8);
                redisTemplate.opsForHash().put(ROBOT_SYS_INFO, SPEECH_LIST, readJson);
            }
        } catch (Exception e) {
            log.error("初始化场景语音列表异常");
            if (future != null) {
                future.cancel(true);
            }
        }
    }

    /**
     * 初始化设备信息
     */
    @Override
    public void initDeviceInfo() {
        iRobotDefineService.initRobotInfo();
    }


}
