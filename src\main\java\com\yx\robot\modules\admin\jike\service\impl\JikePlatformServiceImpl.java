package com.yx.robot.modules.admin.jike.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.utils.RDes;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.RestUtil;
import com.yx.robot.modules.admin.entity.RobotMap;
import com.yx.robot.modules.admin.jike.enums.RobotStatusEnum;
import com.yx.robot.modules.admin.jike.enums.RobotTypeEnum;
import com.yx.robot.modules.admin.jike.enums.UvrFactoryEnum;
import com.yx.robot.modules.admin.jike.constant.ApiConstants;
import com.yx.robot.modules.admin.jike.constant.JikeConstants;
import com.yx.robot.modules.admin.jike.dto.*;
import com.yx.robot.modules.admin.jike.response.GetDisinfectBoxRep;
import com.yx.robot.modules.admin.jike.enums.RespCodeEnum;
import com.yx.robot.modules.admin.jike.response.*;
import com.yx.robot.modules.admin.jike.service.JikePlatformService;
import com.yx.robot.modules.admin.service.IRobotMapService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.concurrent.ListenableFuture;
import org.springframework.util.concurrent.ListenableFutureCallback;
import redis.clients.jedis.Jedis;
import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.SYSTEM_CHECK;


/**
 * 机科平台服务实现类
 * <AUTHOR>
 * @date 2021/12/13
 */
@Service
@Slf4j
public class JikePlatformServiceImpl implements JikePlatformService {

    @Autowired
    private IRobotMapService iRobotMapService;

    @Autowired
    private RosWebService rosWebService;


    /**
     * 获取token
     *
     * @param loginInfoDto
     * @return
     */
    @Override
    public JSONObject refreshToken(LoginInfoDto loginInfoDto) {
        String url = ApiConstants.BASE_TOKEN_URL + ApiConstants.REFRESH_TOKEN;
        LinkedMultiValueMap<String, String> from = new LinkedMultiValueMap<>();
        from.add("username", loginInfoDto.getUsername());
        from.add("password", loginInfoDto.getPassword());
        from.add("grant_type", loginInfoDto.getGrantType());
        from.add("scope", loginInfoDto.getScope());
        JSONObject result = RestUtil.doPostForForm(url, from);
        if(result != null) {
            return result;
        }
        return null;
    }

    /**
     * 获取消毒仓平台token
     *
     * @param uvrLoginDto
     */
    @Override
    public void uvrRefreshToken(UvrLoginDto uvrLoginDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String url1 = ApiConstants.UVR_BASE_URL_1 + ApiConstants.UVR_REFRESH_TOKEN;
            String url2 = ApiConstants.UVR_BASE_URL_2 + ApiConstants.UVR_REFRESH_TOKEN;
            JSONObject result1 = RestUtil.post(url1,JSONObject.parseObject(JSONObject.toJSONString(uvrLoginDto)));
            JSONObject result2 = RestUtil.post(url2, JSONObject.parseObject(JSONObject.toJSONString(uvrLoginDto)));
            if(result1 != null){
                UvrLoginResponse uvrLoginResponse = JSONObject.parseObject(result1.toJSONString(), UvrLoginResponse.class);
                if(RespCodeEnum.SUCCESS.getCode().equals(uvrLoginResponse.getRespCode())) {
                    jedis.set(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN,uvrLoginResponse.getData().getToken());
                    // 平台24小时过期，自定义12小时过期
                    jedis.expire(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN, 60 * 60 * 12);
                }
            }
            if(result2 != null) {
                UvrLoginResponse uvrLoginResponse = JSONObject.parseObject(result2.toJSONString(), UvrLoginResponse.class);
                if(RespCodeEnum.SUCCESS.getCode().equals(uvrLoginResponse.getRespCode())) {
                    jedis.set(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN,uvrLoginResponse.getData().getToken());
                    // 平台24小时过期，自定义12小时过期
                    jedis.expire(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN, 60 * 60 * 12);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }


    /**
     * 上报机器人任务状态
     *
     * @param robotWorkStatusDto
     */
    @Override
    public void reportRobotWorkStatus(RobotWorkStatusDto robotWorkStatusDto) {
        String url = ApiConstants.BASE_URL + ApiConstants.ROBOT_WORK_STATUS_REPORT + "/" + JikeConstants.API_VERSION;
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String token = jedis.get(JikeConstants.JIKE + "::" + JikeConstants.TOKEN);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(ApiConstants.TOKEN_HEADER,ApiConstants.TOKEN_PREFIX + token);
            ListenableFuture<ResponseEntity<JSONObject>> responseEntityListenableFuture = RestUtil.asyncPost(url, httpHeaders, null, JSONObject.parseObject(JSONObject.toJSONString(robotWorkStatusDto)));
            responseEntityListenableFuture.addCallback(new ListenableFutureCallback<ResponseEntity<JSONObject>>() {
                @Override
                public void onFailure(Throwable throwable) {
                    log.info("机器人任务状态上报失败");
                }
                @Override
                public void onSuccess(ResponseEntity<JSONObject> objectResponseEntity) {
                    log.info("机器人任务状态上报成功");
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 处理机器人任务状态
     */
    @Override
    public void handleRobotWorkStatus(RobotWorkStatusDto robotWorkStatusDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String target = jedis.hget(TASK_INFO, NEXT_LOCATION_CODE);
            if(StrUtil.isNotBlank(target) && target.indexOf("-") != -1) {
                if(target.endsWith("-1")
                || target.endsWith("-2")
                || target.endsWith("-3")
                || target.endsWith("-4")
                || target.endsWith("-5")){
                    target = target.substring(0,target.lastIndexOf("-"));
                }
                robotWorkStatusDto.setDestinationId(target);
                String[] split = target.split("-");
                if(split.length >  0) {
                    robotWorkStatusDto.setRoomNo(split[2]);
                }
            }
            // TODO 任务信息需要重新处理
            String orderId = jedis.hget(JikeConstants.JIKE, JikeConstants.ORDER_ID);
            if(StrUtil.isNotBlank(orderId)) {
                robotWorkStatusDto.setOrderId(orderId);
            }
            String taskId = jedis.hget(ROBOT_SYS_INFO, CURRENT_TASK_ID);
            if(StrUtil.isNotBlank(taskId)) {
                robotWorkStatusDto.setTaskId(taskId);
            }
            robotWorkStatusDto.setTaskId(taskId);
            String currentMapId = jedis.hget(ROBOT_SYS_INFO, CURRENT_MAP);
            if(StrUtil.isNotBlank(currentMapId)) {
                RobotMap currentMap = iRobotMapService.getById(currentMapId);
                Integer floor = currentMap.getFloor();
                robotWorkStatusDto.setFloorNo(floor.toString());
            }
            // TODO 备注信息需要重新处理
            robotWorkStatusDto.setRemark("");
            this.reportRobotWorkStatus(robotWorkStatusDto);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 上报机器人自身状态
     *
     * @param robotStatusDto
     */
    @Override
    public void reportRobotStatus(RobotStatusDto robotStatusDto) {
        String url = ApiConstants.BASE_URL + ApiConstants.ROBOT_STATUS_REPORT + "/" + JikeConstants.API_VERSION;
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String token = jedis.get(JikeConstants.JIKE + "::" + JikeConstants.TOKEN);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(ApiConstants.TOKEN_HEADER,ApiConstants.TOKEN_PREFIX + token);
            ListenableFuture<ResponseEntity<JSONObject>> responseEntityListenableFuture = RestUtil.asyncPost(url, httpHeaders, null, JSONObject.parseObject(JSONObject.toJSONString(robotStatusDto)));
            responseEntityListenableFuture.addCallback(new ListenableFutureCallback<ResponseEntity<JSONObject>>() {
                @Override
                public void onFailure(Throwable throwable) {
                    log.info("机器人自身状态上报失败");
                }
                @Override
                public void onSuccess(ResponseEntity<JSONObject> objectResponseEntity) {
                    log.info("机器人自身状态上报成功");
                }
            });
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 处理机器人自身状态
     */
    @Override
    public void handleRobotStatus() {
        RobotStatusDto robotStatusDto = new RobotStatusDto();
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String robotLocation = rosWebService.getRobotLocation();
            robotStatusDto.setPositionId(robotLocation);
            String taskId = jedis.hget(ROBOT_SYS_INFO, CURRENT_TASK_ID);
            if(StrUtil.isNotBlank(taskId)) {
                robotStatusDto.setTaskId(taskId);
            }
            boolean idle = rosWebService.isIdle();
            boolean isCharging = rosWebService.isCharging();
            boolean isOnline = rosWebService.isOnline();
//            String systemCheckStr = jedis.get(TOPIC + "::" + SYSTEM_CHECK);
            String systemCheckStr = RedisUtil.getTopicValue(SYSTEM_CHECK);
            if(!idle) {
                robotStatusDto.setRobotStatus(RobotStatusEnum.WORKING.getValue());
            }else{
                robotStatusDto.setRobotStatus(RobotStatusEnum.WAIT.getValue());
            }
            if(isCharging) {
                robotStatusDto.setRobotStatus(RobotStatusEnum.CHARGING.getValue());
            }
            if (StrUtil.isNotBlank(systemCheckStr)) {
                boolean systemCheck = rosWebService.systemCheck(systemCheckStr);
                if(!systemCheck) {
                    robotStatusDto.setRobotStatus(RobotStatusEnum.ERROR.getValue());
                }
            }
            if(!isOnline) {
                robotStatusDto.setRobotStatus(RobotStatusEnum.OFFLINE.getValue());
            }
            this.reportRobotStatus(robotStatusDto);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 获取消毒仓
     *
     * @param getDisinfectBoxDto
     */
    @Override
    public GetDisinfectBoxRep getDisinfectBox(GetDisinfectBoxDto getDisinfectBoxDto) {
        String url = ApiConstants.BASE_URL + ApiConstants.GET_DISINFECT_BOX + "/" + JikeConstants.API_VERSION;
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String token = jedis.get(JikeConstants.JIKE + "::" + JikeConstants.TOKEN);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(ApiConstants.TOKEN_HEADER,ApiConstants.TOKEN_PREFIX + token);
            JSONObject result = RestUtil.post(url,httpHeaders,null,JSONObject.parseObject(JSONObject.toJSONString(getDisinfectBoxDto)));
            if(result != null) {
                GetDisinfectBoxRep getDisinfectBoxRep = JSONObject.parseObject(result.toJSONString(), GetDisinfectBoxRep.class);
                if(getDisinfectBoxRep != null && getDisinfectBoxRep.getData() != null && StrUtil.isNotBlank(getDisinfectBoxRep.getData().toString())) {
                    String vendor = getDisinfectBoxRep.getData().getVendor();
                    if(UvrFactoryEnum.SAITE.getValue().equals(vendor)) {
                        jedis.set(JikeConstants.UVR_HOST,ApiConstants.UVR_BASE_URL_1);
                    } else if(UvrFactoryEnum.UBTECH.getValue().equals(vendor)) {
                        jedis.set(JikeConstants.UVR_HOST,ApiConstants.UVR_BASE_URL_2);
                    } else {
                        // 如果两者都不行则设置优必选消毒仓地址
                        jedis.set(JikeConstants.UVR_HOST,ApiConstants.UVR_BASE_URL_2);
                    }
                }
                return getDisinfectBoxRep;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * 控制污染区开门
     *
     * @param openDisinfectBoxDoorDto
     */
    @Override
    public OpenDisinfectBoxDoorResponse openDisinfectBoxDoor(OpenDisinfectBoxDoorDto openDisinfectBoxDoorDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String uvrHost = jedis.get(JikeConstants.UVR_HOST);
            String url = uvrHost + ApiConstants.OPEN_DISINFECT_BOX_DOOR;
            String token = "";
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_1)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            }
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_2)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JikeConstants.TOKEN, token);
            OpenDisinfectBoxDoorResponse openDisinfectBoxDoorResponse = null;
            for(int index = 0; index < 3; index++) {
                JSONObject result = RestUtil.post(url,httpHeaders,null,JSONObject.parseObject(JSONObject.toJSONString(openDisinfectBoxDoorDto)));
                if(result != null) {
                    openDisinfectBoxDoorResponse = JSONObject.parseObject(result.toJSONString(), OpenDisinfectBoxDoorResponse.class);
                    if(openDisinfectBoxDoorResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                        return openDisinfectBoxDoorResponse;
                    }
                }
                Thread.sleep(3000);
            }
            return openDisinfectBoxDoorResponse;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * 控制污染区关门
     *
     * @param closeDisinfectBoxDoorDto
     */
    @Override
    public CloseDisinfectBoxDoorResponse closeDisinfectBoxDoor(CloseDisinfectBoxDoorDto closeDisinfectBoxDoorDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String uvrHost = jedis.get(JikeConstants.UVR_HOST);
            String url = uvrHost + ApiConstants.CLOSE_DISINFECT_BOX_DOOR;
            String token = "";
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_1)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            }
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_2)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JikeConstants.TOKEN,token);
            CloseDisinfectBoxDoorResponse closeDisinfectBoxDoorResponse = null;
            for(int index = 0; index < 3; index++) {
                JSONObject result = RestUtil.post(url,httpHeaders, null,JSONObject.parseObject(JSONObject.toJSONString(closeDisinfectBoxDoorDto)));
                if(result != null) {
                    closeDisinfectBoxDoorResponse = JSONObject.parseObject(result.toJSONString(), CloseDisinfectBoxDoorResponse.class);
                    if(closeDisinfectBoxDoorResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                        return closeDisinfectBoxDoorResponse;
                    }
                }
                Thread.sleep(3000);
            }
            return closeDisinfectBoxDoorResponse;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * UC灯开启请求
     *
     * @param startDisinfectBoxTaskDto
     */
    @Override
    public StartDisinfectBoxTaskResponse startDisinfectBoxTask(StartDisinfectBoxTaskDto startDisinfectBoxTaskDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String uvrHost = jedis.get(JikeConstants.UVR_HOST);
            String url = uvrHost + ApiConstants.START_DISINFECT_BOX_TASK;
            String token = "";
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_1)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            }
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_2)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JikeConstants.TOKEN,token);
            StartDisinfectBoxTaskResponse startDisinfectBoxTaskResponse = null;
            for(int index = 0; index < 3; index++) {
                JSONObject result = RestUtil.post(url,httpHeaders,null,JSONObject.parseObject(JSONObject.toJSONString(startDisinfectBoxTaskDto)));
                if(result != null) {
                    startDisinfectBoxTaskResponse = JSONObject.parseObject(result.toJSONString(), StartDisinfectBoxTaskResponse.class);
                    if(startDisinfectBoxTaskResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                        return startDisinfectBoxTaskResponse;
                    }
                }
                Thread.sleep(3000);
            }
            return startDisinfectBoxTaskResponse;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * UC灯关闭请求
     *
     * @param stopDisinfectBoxTaskDto
     */
    @Override
    public StopDisinfectBoxTaskResponse stopDisinfectBoxTask(StopDisinfectBoxTaskDto stopDisinfectBoxTaskDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String uvrHost = jedis.get(JikeConstants.UVR_HOST);
            String url = uvrHost + ApiConstants.STOP_DISINFECT_BOX_TASK;
            String token = "";
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_1)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            }
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_2)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JikeConstants.TOKEN,token);
            StopDisinfectBoxTaskResponse stopDisinfectBoxTaskResponse = null;
            for(int index = 0; index < 3; index++) {
                JSONObject result = RestUtil.post(url,httpHeaders,null,JSONObject.parseObject(JSONObject.toJSONString(stopDisinfectBoxTaskDto)));
                if(result != null) {
                    stopDisinfectBoxTaskResponse = JSONObject.parseObject(result.toJSONString(), StopDisinfectBoxTaskResponse.class);
                    if(stopDisinfectBoxTaskResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                        return stopDisinfectBoxTaskResponse;
                    }
                }
                Thread.sleep(3000);
            }
            return stopDisinfectBoxTaskResponse;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * 控制清洁区开门
     *
     * @param openDisinfectCleanDoorDto
     */
    @Override
    public OpenDisinfectCleanDoorResponse openDisinfectCleanDoor(OpenDisinfectCleanDoorDto openDisinfectCleanDoorDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String uvrHost = jedis.get(JikeConstants.UVR_HOST);
            String url = uvrHost + ApiConstants.OPEN_DISINFECT_CLEAN_DOOR;
            String token = "";
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_1)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            }
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_2)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JikeConstants.TOKEN,token);
            OpenDisinfectCleanDoorResponse openDisinfectCleanDoorResponse = null;
            for(int index = 0; index < 3; index++) {
                JSONObject result = RestUtil.post(url,httpHeaders,null,JSONObject.parseObject(JSONObject.toJSONString(openDisinfectCleanDoorDto)));
                if(result != null) {
                    openDisinfectCleanDoorResponse = JSONObject.parseObject(result.toJSONString(), OpenDisinfectCleanDoorResponse.class);
                    if(openDisinfectCleanDoorResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                        return openDisinfectCleanDoorResponse;
                    }
                }
                Thread.sleep(3000);
            }
            return openDisinfectCleanDoorResponse;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * 控制清洁区关门
     *
     * @param closeDisinfectCleanDoorDto
     */
    @Override
    public CloseDisinfectCleanDoorResponse closeDisinfectCleanDoor(CloseDisinfectCleanDoorDto closeDisinfectCleanDoorDto) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String uvrHost = jedis.get(JikeConstants.UVR_HOST);
            String url = uvrHost + ApiConstants.CLOSE_DISINFECT_CLEAN_DOOR;
            String token = "";
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_1)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            }
            if(uvrHost.equals(ApiConstants.UVR_BASE_URL_2)) {
                token = jedis.get(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
            }
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JikeConstants.TOKEN,token);
            CloseDisinfectCleanDoorResponse closeDisinfectCleanDoorResponse = null;
            for(int index = 0; index < 3; index++) {
                JSONObject result = RestUtil.post(url,httpHeaders, null,JSONObject.parseObject(JSONObject.toJSONString(closeDisinfectCleanDoorDto)));
                if(result != null) {
                    closeDisinfectCleanDoorResponse = JSONObject.parseObject(result.toJSONString(), CloseDisinfectCleanDoorResponse.class);
                    if(closeDisinfectCleanDoorResponse.getRespCode().equals(RespCodeEnum.SUCCESS.getCode())) {
                        return closeDisinfectCleanDoorResponse;
                    }
                }
                Thread.sleep(3000);
            }
            return closeDisinfectCleanDoorResponse;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * 消毒仓完成通知
     *
     * @param notifyDisinfectBoxFinishDto
     */
    @Override
    public NotifyDisinfectBoxFinishResponse notifyDisinfectBoxFinishResponse(NotifyDisinfectBoxFinishDto notifyDisinfectBoxFinishDto) {
        String url = ApiConstants.BASE_URL + ApiConstants.NOTIFY_DISINFECT_BOX_FINISH + "/" + JikeConstants.API_VERSION;
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String token = jedis.get(JikeConstants.JIKE + "::" + JikeConstants.TOKEN);
            HttpHeaders httpHeaders = new HttpHeaders();
            httpHeaders.add(JikeConstants.TOKEN,ApiConstants.TOKEN_PREFIX + token);
            JSONObject result = RestUtil.post(url,httpHeaders,null,JSONObject.parseObject(JSONObject.toJSONString(notifyDisinfectBoxFinishDto)));
            if(result != null) {
                NotifyDisinfectBoxFinishResponse notifyDisinfectBoxFinishResponse = JSONObject.parseObject(result.toJSONString(), NotifyDisinfectBoxFinishResponse.class);
                return notifyDisinfectBoxFinishResponse;
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return null;
    }

    /**
     * 判断房间消杀是否到达或完成
     */
    @Override
    public boolean roomDisinfectIsStartOrFinish() {
        return true;
    }
}
