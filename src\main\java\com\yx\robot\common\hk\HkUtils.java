package com.yx.robot.common.hk;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.thread.ThreadUtil;
import cn.hutool.core.util.ObjectUtil;
import com.sun.jna.Native;
import com.sun.jna.Pointer;
import com.sun.jna.ptr.ByteByReference;
import com.sun.jna.ptr.IntByReference;
import com.yx.robot.common.utils.OsSelect;
import com.yx.robot.common.utils.YmlUtils;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.Date;
import java.util.Objects;

/**
 * 海康威视摄像头工具类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/8/29 13:17
 */
@Slf4j
public class HkUtils {

    static int userHandler;

    static int realHandler;

    static HCNetSDK hCNetSDK = null;

    static PlayCtrl playControl;

    /**
     * 视频通道
     */
    static int videoChannel = 2;

    /**
     * 图片通道
     */
    static int picChannel = 1;

    /**
     * 根目录
     */
    public static String robotPath;

    static String hkLibPath;

    public static String picPath;

    public static String videoPath;

    static String ip;

    static int port;

    static String username;

    static String password;

    static {
        robotPath = ObjectUtil.toString(YmlUtils.getValue("hk.robotPath"));
        hkLibPath = ObjectUtil.toString(YmlUtils.getValue("hk.config"));
        picPath = ObjectUtil.toString(YmlUtils.getValue("hk.picPath"));
        videoPath = ObjectUtil.toString(YmlUtils.getValue("hk.videoPath"));
        ip = ObjectUtil.toString(YmlUtils.getValue("hk.host"));
        port = ObjectUtil.isNull(YmlUtils.getValue("hk.port")) ? 8000
                : Integer.parseInt(Objects.requireNonNull(YmlUtils.getValue("hk.port")).toString());
        username = ObjectUtil.toString(YmlUtils.getValue("hk.username"));
        password = ObjectUtil.toString(YmlUtils.getValue("hk.password"));
    }

    private static boolean setHcNetSdk() {
        if (hCNetSDK == null) {
            if (!createSdkInstance()) {
                log.error("Load SDK fail");
                return false;
            }
        }
        //linux系统建议调用以下接口加载组件库
        if (OsSelect.isLinux()) {
            HCNetSDK.BYTE_ARRAY ptrByteArray1 = new HCNetSDK.BYTE_ARRAY(256);
            HCNetSDK.BYTE_ARRAY ptrByteArray2 = new HCNetSDK.BYTE_ARRAY(256);
            //这里是库的绝对路径，请根据实际情况修改，注意改路径必须有访问权限
            String strPath1 = hkLibPath + "/libcrypto.so.1.1";
            String strPath2 = hkLibPath + "/libssl.so.1.1";

            System.arraycopy(strPath1.getBytes(), 0, ptrByteArray1.byValue, 0, strPath1.length());
            ptrByteArray1.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(3, ptrByteArray1.getPointer());

            System.arraycopy(strPath2.getBytes(), 0, ptrByteArray2.byValue, 0, strPath2.length());
            ptrByteArray2.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(4, ptrByteArray2.getPointer());

            String strPathCom = hkLibPath;
            HCNetSDK.NET_DVR_LOCAL_SDK_PATH struComPath = new HCNetSDK.NET_DVR_LOCAL_SDK_PATH();
            System.arraycopy(strPathCom.getBytes(), 0, struComPath.sPath, 0, strPathCom.length());
            struComPath.write();
            hCNetSDK.NET_DVR_SetSDKInitCfg(2, struComPath.getPointer());
        }
        //SDK初始化，一个程序只需要调用一次
        boolean initSuc = hCNetSDK.NET_DVR_Init();

        if (!initSuc) {
            log.info("初始化失败");
            return false;
        }
        return true;
    }

    /**
     * 动态库加载
     *
     * @return true/false
     */
    private static boolean createSdkInstance() {
        if (hCNetSDK == null) {
            synchronized (HCNetSDK.class) {
                String strDllPath = "";
                try {
                    if (OsSelect.isWindows()) {
                        //win系统加载库路径
                        strDllPath = hkLibPath + "/HCNetSDK.dll";
                        hCNetSDK = (HCNetSDK) Native.loadLibrary(strDllPath, HCNetSDK.class);
//                        strDllPath = System.getProperty("user.dir") + "\\lib\\HCNetSDK.dll";
                    } else if (OsSelect.isLinux()) {
                        //Linux系统加载库路径
                        strDllPath = hkLibPath + "/libhcnetsdk.so";
                        if (!FileUtil.exist(strDllPath)) {
                            log.error("文件不存在：" + strDllPath);
                        }
                        hCNetSDK = (HCNetSDK) Native.loadLibrary(strDllPath, HCNetSDK.class);
                    }
                } catch (Exception ex) {
                    log.error("loadLibrary: " + strDllPath + " Error: " + ex.getMessage());
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 设备登录V40 与V30功能一致
     *
     * @param ip   设备IP
     * @param port SDK端口，默认设备的8000端口
     * @param user 设备用户名
     * @param psw  设备密码
     * @return 用户句柄 实现对设备登录
     */
    public static int loginV40(String ip, short port, String user, String psw) {
        //注册
        //设备登录信息
        HCNetSDK.NET_DVR_USER_LOGIN_INFO strLoginInfo = new HCNetSDK.NET_DVR_USER_LOGIN_INFO();
        //设备信息
        HCNetSDK.NET_DVR_DEVICEINFO_V40 strDeviceInfo = new HCNetSDK.NET_DVR_DEVICEINFO_V40();

        //设备ip地址
        strLoginInfo.sDeviceAddress = new byte[HCNetSDK.NET_DVR_DEV_ADDRESS_MAX_LEN];
        System.arraycopy(ip.getBytes(), 0, strLoginInfo.sDeviceAddress, 0, ip.length());

        //设备用户名
        strLoginInfo.sUserName = new byte[HCNetSDK.NET_DVR_LOGIN_USERNAME_MAX_LEN];
        System.arraycopy(user.getBytes(), 0, strLoginInfo.sUserName, 0, user.length());

        //设备密码
        strLoginInfo.sPassword = new byte[HCNetSDK.NET_DVR_LOGIN_PASSWD_MAX_LEN];
        System.arraycopy(psw.getBytes(), 0, strLoginInfo.sPassword, 0, psw.length());

        strLoginInfo.wPort = port;
        //是否异步登录：0- 否，1- 是
        strLoginInfo.bUseAsynLogin = false;

        strLoginInfo.write();

        int lUserHandler = hCNetSDK.NET_DVR_Login_V40(strLoginInfo, strDeviceInfo);
        if (lUserHandler == -1) {
            log.info("登录失败，错误码为" + hCNetSDK.NET_DVR_GetLastError());
        } else {
            log.info(ip + ":设备登录成功！");
        }
        return lUserHandler;
    }

    /**
     * 设备注销
     *
     * @param loginHandler 登录句柄
     */
    public static void logout(int loginHandler) {
        if (hCNetSDK.NET_DVR_Logout(loginHandler)) {
            log.info("注销成功");
        }
        hCNetSDK.NET_DVR_Cleanup();
    }

    public static String captureJpegPicture(int loginHandler, String fileName) {
        HCNetSDK.NET_DVR_JPEGPARA netDvrJpegpara = new HCNetSDK.NET_DVR_JPEGPARA();
        netDvrJpegpara.wPicSize = 4;
        netDvrJpegpara.wPicQuality = 1;
        String path = robotPath + picPath + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + "/";
        if (!FileUtil.exist(path)) {
            FileUtil.mkdir(path);
        }
        String fullPath = path + fileName + ".jpeg";
        boolean b = hCNetSDK.NET_DVR_CaptureJPEGPicture(loginHandler, picChannel, netDvrJpegpara, fullPath);
        if (!b) {
            if (FileUtil.exist(fullPath)) {
                FileUtil.del(fullPath);
            }
            log.info("抓图错误码:{}", hCNetSDK.NET_DVR_GetLastError());
            return null;
        }
        setAuth(fullPath);
        return "/" + fullPath.replace(robotPath, "");
    }

    public static void setAuth(String path) {
        try {
            Runtime.getRuntime().exec("chmod 777 " + path);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 播放库加载
     *
     * @return true/false
     */
    private static boolean createPlayInstance() {
        if (playControl == null) {
            synchronized (PlayCtrl.class) {
                String strPlayPath = "";
                try {
                    if (OsSelect.isWindows()) {
                        //win系统加载库路径
                        strPlayPath = hkLibPath + "/PlayCtrl.dll";
                    } else if (OsSelect.isLinux()) {
                        //Linux系统加载库路径
                        strPlayPath = hkLibPath + "/libPlayCtrl.so";
                    }
                    playControl = (PlayCtrl) Native.loadLibrary(strPlayPath, PlayCtrl.class);

                } catch (Exception ex) {
                    log.info("loadLibrary: " + strPlayPath + " Error: " + ex.getMessage());
                    return false;
                }
            }
        }
        return true;
    }

    public static int startRealPlayV40(int loginHandler) {
        HCNetSDK.NET_DVR_PREVIEWINFO lpPreviewInfo = new HCNetSDK.NET_DVR_PREVIEWINFO();
        lpPreviewInfo.byPreviewMode = 0;
//        lpPreviewInfo.bBlocked = 0;
        //通道号
        lpPreviewInfo.lChannel = videoChannel;
        // 码流类型，0-主码流，1-子码流，2-码流3，3-码流4, 4-码流5,5-码流6,7-码流7,8-码流8,9-码流9,10-码流10
        lpPreviewInfo.dwStreamType = 0;
        // 0：TCP方式,1：UDP方式,2：多播方式,3 - RTP方式，4-RTP/RTSP,5-RSTP/HTTP ,6- HRUDP（可靠传输） ,7-RTSP/HTTPS
        lpPreviewInfo.dwLinkMode = 5;
        //应用层取流协议，0-私有协议，1-RTSP协议
        lpPreviewInfo.byProtoType = 1;
        lpPreviewInfo.bBlocked = 1;
        //播放库播放缓冲区最大缓冲帧数，范围1-50，置0时默认为1
//        lpPreviewInfo.dwDisplayBufNum = 10;
        //NPQ是直连模式，还是过流媒体 0-直连 1-过流媒体
//        lpPreviewInfo.byNPQMode = 0;

        Pointer pUser = Pointer.createConstant(loginHandler);
        RealDataCallBackV30 realDataCallBackV30 = new RealDataCallBackV30(playControl, new IntByReference(3));
        return hCNetSDK.NET_DVR_RealPlay_V40(loginHandler, lpPreviewInfo,
                realDataCallBackV30, pUser);
    }

    public static String saveRealDataV30(int lRealHandle, String fileName) {
        String path = robotPath + videoPath + DateUtil.format(new Date(), DatePattern.NORM_DATE_PATTERN) + "/";
        if (!FileUtil.exist(path)) {
            FileUtil.mkdir(path);
        }
        String fullPath = path + fileName + ".mp4";
        boolean result = hCNetSDK.NET_DVR_SaveRealData_V30(lRealHandle, 2, fullPath);
        if (result) {
            setAuth(fullPath);
            return "/" + fullPath.replace(robotPath, "");
        } else {
            if (FileUtil.exist(fullPath)) {
                FileUtil.del(fullPath);
            }
            log.warn("视频录制失败:{}", hCNetSDK.NET_DVR_GetLastError());
            return null;
        }
    }

    public static boolean stopRealPlayV40(int lRealHandler) {
        return hCNetSDK.NET_DVR_StopRealPlay(lRealHandler);
    }

    public static String getPic(String fileName) {
        boolean initSdkSuccess = setHcNetSdk();
        if (!initSdkSuccess) {
            return null;
        }
        int loginHandler = loginV40(ip, (short) port, username, password);
        String path = captureJpegPicture(loginHandler, fileName);
        ThreadUtil.sleep(100);
        logout(loginHandler);
        return path;
    }

    public static int preRecord() {
        boolean initSdkSuccess = setHcNetSdk();
        if (!initSdkSuccess) {
            return -1;
        }
        if (!createPlayInstance()) {
            return -1;
        }
        userHandler = loginV40(ip, (short) port, username, password);
        return userHandler;
    }

    public static String getStartRecord(String fileName, int loginHandler) {
        realHandler = startRealPlayV40(loginHandler);
        return saveRealDataV30(realHandler, fileName);
    }

    public static boolean endRecord() {
        if (ObjectUtil.isNull(hCNetSDK)) {
            log.info("hcNetSdk为空");
            return false;
        }
        boolean result = stopRealPlayV40(realHandler);
        logout(userHandler);
        return result;
    }

    public static void main(String[] args) {
//        System.out.println(HkUtils.ip);
//        for (int i = 1000; i < 1001; i++) {
//            System.out.println(getPic("p" + i));
//            System.out.println("=================start==============================");
//            int loginHandler = preRecord();
//            System.out.println(getStartRecord("vv" + i, loginHandler));
//            ThreadUtil.sleep(1000 * 5);
//            System.out.println("=================end==============================");
//            System.out.println(endRecord());
//        }
//        realDate();
//        System.out.println(JSON.toJSONString(YmlUtils.getValue("robot")));
//        Object profiles = YmlUtils.getValue("robot.factory-info");
//        System.out.println(profiles);
    }

    public static void realDate() {
        int loginHandler = preRecord();
        int realHandler = startRealPlayV40(loginHandler);
        hCNetSDK.NET_DVR_SetStandardDataCallBack(realHandler, new HCNetSDK.FStdDataCallBack() {
            @Override
            public void invoke(int lRealHandle, int dwDataType, ByteByReference pBuffer, int dwBufSize, int dwUser) {
                log.info("dwDataType:{},dwBufSize:{}", dwDataType, dwBufSize);
//                if (NET_DVR_STD_VIDEODATA == dwDataType) {
                log.info(pBuffer.getValue()+"");
                log.info("end");
//                    FileUtil.writeBytes(pBuffer.getPointer().getByteArray(0, dwBufSize), "E:/test1.mp4");
//                }
            }
        }, loginHandler);
        ThreadUtil.sleep(10 * 1000);
        stopRealPlayV40(realHandler);
        logout(loginHandler);
    }
}
