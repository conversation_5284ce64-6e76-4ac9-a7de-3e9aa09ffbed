package com.yx.robot.modules.admin.vo;

import com.yx.robot.modules.admin.entity.RobotSwitchRecord;
import lombok.Data;

import java.util.Date;

/**
 * 开关机记录
 * <AUTHOR>
 * @date 2020/01/08
 */
@Data
public class RobotSwitchRecordVo {

    /**
     * id
     */
    private String id;

    /**
     * 序列号
     */
    private String serialNumber;

    /**
     * 开始时间
     */
    private Date startTime;

    /**
     * 动作（0：关机 1：开机）
     */
    private Integer action;

    /**
     * 类型（0：低电量 1：手动 2：自动 3：远程）
     */
    private Integer type;

    /**
     * 电量百分比
     */
    private Double percentage;


    public RobotSwitchRecordVo(RobotSwitchRecord robotSwitchRecord) {
        this.id = robotSwitchRecord.getId();
        this.startTime = robotSwitchRecord.getStartTime();
        this.action = robotSwitchRecord.getAction();
        this.type = robotSwitchRecord.getType();
        this.percentage = robotSwitchRecord.getPercentage();
    }
}
