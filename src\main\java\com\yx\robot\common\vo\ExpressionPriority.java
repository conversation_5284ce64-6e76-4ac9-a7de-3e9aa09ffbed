package com.yx.robot.common.vo;

/**
 * <AUTHOR>
 * @date 2022/7/18
 * description：点阵表情优先级，有的没用到，此类只起辅助作用；
 */
public class ExpressionPriority {

    /**
     * 危险  13
     */
    public boolean danger;

    /**
     * 低电量   12
     */
    public boolean lowBattery;

    /**
     * 急停    4
     */
    public boolean emergencyStop;

    /**
     * 迷路    14
     */
    public boolean getLost;

    /**
     * 工作被打扰   8
     */
    public boolean disturbed=false;

    /**
     * 工作中   6
     */
    public boolean working=false;

    /**
     * 寻找充电桩   9
     */
    public boolean gotoCharing=false;

    /**
     * 充电中     10
     */
    public boolean charing;

    /**
     * 满电    11
     */
    public boolean fullPower;

    /**
     * 微笑    微笑1：7   ；   微笑2：5
     */
    public boolean smile;

}
