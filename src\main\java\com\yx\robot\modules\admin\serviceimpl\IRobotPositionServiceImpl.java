package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.constant.ServiceConstants;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.enums.RobotPositionType;
import com.yx.robot.common.exception.AppException;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.common.utils.ResultUtil;
import com.yx.robot.common.utils.SecurityUtil;
import com.yx.robot.common.vo.Result;
import com.yx.robot.modules.admin.dao.mapper.RobotPositionMapper;
import com.yx.robot.modules.admin.entity.*;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.*;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.vo.RobotPositionVo;
import com.yx.robot.modules.admin.vo.RobotSubPositionVo;
import com.yx.robot.modules.base.entity.User;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.ServiceConstants.POSE_CHECK_VALID;

/**
 * 机器人列表接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional(rollbackFor = Exception.class)
public class IRobotPositionServiceImpl extends ServiceImpl<RobotPositionMapper, RobotPosition> implements IRobotPositionService {

    @Autowired
    private IRobotWorldPositionService iRobotWorldPositionService;

    @Autowired
    private SecurityUtil securityUtil;

    @Autowired
    private IRobotTaskItemService iRobotTaskItemService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IRobotLocationService iRobotLocationService;

    @Autowired
    private RosBridgeService rosBridgeService;

    @Autowired
    private IRobotDisinfectService iRobotDisinfectService;

    @Autowired
    private IRobotEntranceGuardService iRobotEntranceGuardService;

    @Autowired
    private IRobotDisinfectBoxService iRobotDisinfectBoxService;

    @Autowired
    private IRobotChargingService iRobotChargingService;

    @Autowired
    private IRobotPositionService iRobotPositionService;

    @Autowired
    private IRobotPositionDevRelationService robotPositionDevRelationService;

    /**
     * 插入点位数据
     *
     * @param robotPositionVo
     * @return
     */
    @Override
    public Result<Boolean> add(RobotPositionVo robotPositionVo) {
        if (RobotPositionType.START_POSITION.getType().equals(robotPositionVo.getType())
                || RobotPositionType.CHARGING_POSITION.getType().equals(robotPositionVo.getType())) {
            Result<Boolean> result = new Result<>();
            Boolean res = markStartAndChargingPose(robotPositionVo.getMapId());
            result.setSuccess(res);
            result.setResult(res);
            result.setMessage(res ? "操作成功" : "操作失败");
            return result;
        }
        return add(robotPositionVo, null);
    }

    /**
     * 插入点位数据
     *
     * @param robotPositionVo
     * @return
     */
    private Result<Boolean> add(RobotPositionVo robotPositionVo, RobotWorldPosition autoRobotWorldPosition) {
        log.info("准备进入标点模式");
        RobotPosition robotPosition = new RobotPosition();
        robotPosition.setParentId(robotPositionVo.getRobotPositionParentId());
        robotPosition.setName(robotPositionVo.getName());
        robotPosition.setType(robotPositionVo.getType());
        robotPosition.setMapId(robotPositionVo.getMapId());
        robotPosition.setCreateTime(new Date());
        robotPosition.setUpdateTime(new Date());
        User user = securityUtil.getCurrUser();
        if (null != user) {
            robotPosition.setCreateBy(user.getUsername());
        }
        //先插入世界坐标系
        RobotWorldPosition robotWorldPosition = getRobotPoseFromRos();
        if (robotPositionVo.getType().equals(RobotPositionType.START_POSITION.getType())) {
            if (robotPositionVo.getMapId().equals(TEMP_MAP_ID)) {
                robotWorldPosition = getStartPosition();
            }
            if (ObjectUtil.isNotNull(autoRobotWorldPosition)) {
                robotWorldPosition = autoRobotWorldPosition;
            }
        }
        if (robotPositionVo.getType().equals(RobotPositionType.INIT_POSITION.getType())) {
            if (robotPositionVo.getMapId().equals(TEMP_MAP_ID)) {
                robotWorldPosition = getStartPosition();
            }
        }
        if (robotPositionVo.getType().equals(RobotPositionType.CHARGING_POSITION.getType())) {
            if (robotPositionVo.getMapId().equals(TEMP_MAP_ID)) {
                robotWorldPosition = getChargingPosition();
            }
            if (ObjectUtil.isNotNull(autoRobotWorldPosition)) {
                robotWorldPosition = autoRobotWorldPosition;
            }
        }
        log.info("世界坐标系：：：：：：" + JSONObject.toJSONString(robotWorldPosition));
        if (null != robotWorldPosition) {
            String uuId = UUID.randomUUID().toString();
            robotWorldPosition.setUuId(uuId);
            // 如果是构建地图中的标点则不进行点位检验
            if (!robotPositionVo.getMapId().equals(TEMP_MAP_ID)) {
                boolean b = checkPointValue(robotWorldPosition);
                if (!b) {
                    throw new AppException("当前点位无法通过检验，标点失败");
                }
            }
            boolean res = iRobotWorldPositionService.save(robotWorldPosition);
            if (res) {
                QueryWrapper<RobotWorldPosition> queryWrapper = new QueryWrapper<>();
                queryWrapper.eq("uu_id", uuId);
                RobotWorldPosition robotWorldPosition1 = iRobotWorldPositionService.getOne(queryWrapper);
                if (null != robotWorldPosition1) {
                    robotPosition.setWorldPoseId(robotWorldPosition1.getId());
                }
            }
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("获取机器人位置信息超时");
        }
        if (RobotPositionType.START_POSITION.getType().equals(robotPositionVo.getType())) {
            deleteByTypeNameAndMapId(RobotPositionType.START_POSITION.getValue(), robotPositionVo.getMapId());
        }
        if (RobotPositionType.CHARGING_POSITION.getType().equals(robotPositionVo.getType())) {
            deleteByTypeNameAndMapId(RobotPositionType.CHARGING_POSITION.getValue(), robotPositionVo.getMapId());
        }
        //判断同一张地图下名称是否重复
        boolean isRepeat = isRepeat(robotPositionVo.getMapId(), robotPositionVo.getName());
        if (isRepeat) {
            return new ResultUtil<Boolean>().setErrorMsg("点位名称重复");
        }
        boolean res = this.save(robotPosition);
        if (RobotPositionType.INSIDE_ENTRANCE_GUARD.getType().equals(robotPositionVo.getType())
                || RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType().equals(robotPositionVo.getType())) {
            robotPositionDevRelationService.addRelation(robotPositionVo.getDevId(), robotPosition.getId());
        }
        //判断是否设置标注位置
        String locationInfo = robotPositionVo.getLocationInfo();
        String locationCode = robotPositionVo.getLocationCode();
        if (StrUtil.isNotEmpty(locationInfo) && StrUtil.isNotEmpty(locationCode)) {
            RobotLocation robotLocation = new RobotLocation();
            QueryWrapper<RobotPosition> queryWrapper = new QueryWrapper<>();
            queryWrapper.eq("name", robotPositionVo.getName());
            queryWrapper.eq("map_id", robotPositionVo.getMapId());
            queryWrapper.eq("type", robotPositionVo.getType());
            RobotPosition robotPosition1 = this.getOne(queryWrapper);
            robotLocation.setParentId(robotPositionVo.getRobotLocationParentId());
            robotLocation.setPositionId(robotPosition1.getId());
            robotLocation.setLocationInfo(locationInfo);
            robotLocation.setLocationCode(locationCode);
            if (null != user) {
                robotLocation.setCreateBy(user.getUsername());
            }
            robotLocation.setCreateTime(new Date());
            res = iRobotLocationService.save(robotLocation);
            log.info("设备标注位置插入成功");
        }
        iRobotEntranceGuardService.handleMarkEntranceGuardPosition(robotPosition);
        iRobotDisinfectBoxService.handleMarkDisinfectBoxPosition(robotPosition);
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "添加成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("添加失败");
        }
    }

    /**
     * robot实体转vo
     *
     * @param robotPosition
     * @return
     */
    @Override
    public RobotPositionVo robotPositionToRobotPositionVo(RobotPosition robotPosition) {
        //获取世界坐标点位id
        String worldPoseId = robotPosition.getWorldPoseId();
        //根据id获取世界坐标点位
        RobotWorldPosition robotWorldPosition = iRobotWorldPositionService.getById(worldPoseId);
        RobotPositionVo robotPositionVo = new RobotPositionVo();
        robotPositionVo.setId(robotPosition.getId());
        robotPositionVo.setCreateBy(robotPosition.getCreateBy());
        robotPositionVo.setCreateTime(robotPosition.getCreateTime());
        robotPositionVo.setUpdateBy(robotPosition.getUpdateBy());
        robotPositionVo.setUpdateTime(robotPosition.getUpdateTime());
        robotPositionVo.setMapId(robotPosition.getMapId());
        robotPositionVo.setName(robotPosition.getName());
        robotPositionVo.setType(robotPosition.getType());
        //将世界坐标点位放入vo返回
        robotPositionVo.setRobotWorldPosition(robotWorldPosition);
        List<RobotLocation> robotLocationList = iRobotLocationService.list(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getPositionId, robotPosition.getId()));
        if (CollectionUtil.isNotEmpty(robotLocationList)) {
            robotPositionVo.setRobotLocationId(robotLocationList.get(robotLocationList.size() - 1).getId());
            robotPositionVo.setLocationInfo(robotLocationList.get(robotLocationList.size() - 1).getLocationInfo());
            robotPositionVo.setLocationCode(robotLocationList.get(robotLocationList.size() - 1).getLocationCode());
        }
        return robotPositionVo;
    }

    /**
     * robot实体转subPositionVo
     *
     * @param robotPosition
     * @return
     */
    @Override
    public RobotSubPositionVo robotPositionToRobotSubPositionVo(RobotPosition robotPosition) {
        RobotSubPositionVo robotSubPositionVo = new RobotSubPositionVo();
        String robotPositionId = robotPosition.getId();
        robotSubPositionVo.setId(robotPositionId);
        robotSubPositionVo.setCreateBy(robotPosition.getCreateBy());
        robotSubPositionVo.setCreateTime(robotPosition.getCreateTime());
        robotSubPositionVo.setUpdateBy(robotPosition.getUpdateBy());
        robotSubPositionVo.setUpdateTime(robotPosition.getUpdateTime());
        robotSubPositionVo.setParentRobotPositionId(robotPosition.getParentId());
        robotSubPositionVo.setMapId(robotPosition.getMapId());
        robotSubPositionVo.setType(robotPosition.getType());
        List<RobotLocation> robotLocationList = iRobotLocationService.list(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getPositionId, robotPositionId));
        if (CollectionUtil.isNotEmpty(robotLocationList)) {
            robotSubPositionVo.setParentRobotLocationId(robotLocationList.get(robotLocationList.size() - 1).getParentId());
            robotSubPositionVo.setLocationInfo(robotLocationList.get(robotLocationList.size() - 1).getLocationInfo());
            robotSubPositionVo.setLocationCode(robotLocationList.get(robotLocationList.size() - 1).getLocationCode());
        }
        return robotSubPositionVo;
    }

    @Override
    public boolean sort() {
        List<RobotLocation> robotLocationList = iRobotLocationService.list();
        String regEx = "[^0-9]";
        Pattern p = Pattern.compile(regEx);
        for (int i = 0; i < robotLocationList.size(); i++) {
            RobotLocation robotLocation = robotLocationList.get(i);
            BigDecimal order = null;
            try {
                String robotLocationStr = robotLocation.getLocationInfo() + robotLocation.getLocationCode();
                Matcher m = p.matcher(robotLocationStr);
                String result = m.replaceAll("").trim();
                if (StringUtils.isNumeric(result)) {
                    order = BigDecimal.valueOf(Integer.parseInt(result));
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            robotLocation.setSortOrder(order);
            iRobotLocationService.updateById(robotLocation);
            RobotPosition robotPosition = this.getById(robotLocation.getPositionId());
            if (null != robotPosition) {
                robotPosition.setSortOrder(order);
                this.updateById(robotPosition);
            }
        }
        return true;
    }

    /**
     * 更新点位数据
     *
     * @param robotPositionVo
     * @return
     */
    @Override
    public Result<Boolean> update(RobotPositionVo robotPositionVo) {
        if (RobotPositionType.START_POSITION.getType().equals(robotPositionVo.getType())
                || RobotPositionType.CHARGING_POSITION.getType().equals(robotPositionVo.getType())) {
            Result<Boolean> result = new Result<>();
            deleteByTypeNameAndMapId(RobotPositionType.START_POSITION.getValue(), robotPositionVo.getMapId());
            deleteByTypeNameAndMapId(RobotPositionType.CHARGING_POSITION.getValue(), robotPositionVo.getMapId());
            result.setResult(markStartAndChargingPose(robotPositionVo.getMapId()));
            return result;
        }
        RobotPosition robotPosition = this.getById(robotPositionVo.getId());
        User user = securityUtil.getCurrUser();
        if (null != robotPosition) {
            robotPosition.setParentId(robotPositionVo.getRobotPositionParentId());
            robotPosition.setType(robotPositionVo.getType());
            robotPosition.setMapId(robotPositionVo.getMapId());
            if (null != user) {
                robotPosition.setUpdateBy(user.getUsername());
            }
            robotPosition.setUpdateTime(new Date());
            boolean recalibration = robotPositionVo.isRecalibration();
            if (recalibration) {
                RobotWorldPosition robotWorldPosition = getRobotPoseFromRos();
                // 如果是充电点
//                if (robotPositionVo.getType().equals(RobotPositionType.CHARGING_POSITION.getType())) {
//                    robotWorldPosition = null;
//                    _RobotDockReq robotDockReq = new _RobotDockReq();
//                    robotDockReq.cmd = 1;
//                    String s = rosBridgeService.callService(ServiceConstants.AUTO_DOCK, Message.getMessageType(_RobotDockReq.class), JSON.toJSONString(robotDockReq));
//                    if (StrUtil.isNotEmpty(s)) {
//                        _RobotDockRep robotDockRep = JSON.parseObject(s, _RobotDockRep.class);
//                        if (robotDockRep.ret == 0) {
//                            log.info("设置充电桩成功");
//                            _Pose pose = robotDockRep.pose;
//                            robotWorldPosition = new RobotWorldPosition(pose);
//                        }
//                    }
//                    if (robotWorldPosition == null) {
//                        throw new AppException("标点充电桩失败，无法获取充点电位置信息");
//                    }
//                }
                if (null != robotWorldPosition) {
                    robotWorldPosition.setId(robotPosition.getWorldPoseId());
                    boolean b = checkPointValue(robotWorldPosition);
                    if (!b) {
                        throw new AppException("当前点位无法通过检验，标点失败");
                    }
                    iRobotWorldPositionService.saveOrUpdate(robotWorldPosition);
                } else {
                    return new ResultUtil<Boolean>().setErrorMsg("获取机器人位置信息超时");
                }
            }
            //判断同一张地图下名称是否重复
            if (!robotPosition.getName().equals(robotPositionVo.getName())) {
                boolean isRepeat = isRepeat(robotPositionVo.getMapId(), robotPositionVo.getName());
                if (isRepeat) {
                    return new ResultUtil<Boolean>().setErrorMsg("点位名称重复");
                }
            }
            //修改robot_position表---解决：修改点位名称，消毒日志未更改BUG;---已测试
            if (robotPosition.getType().equals(5)) {
                robotPosition.setName(robotPositionVo.getLocationInfo() + robotPositionVo.getLocationCode());
            } else {
                robotPosition.setName(robotPositionVo.getName());
            }
        }
        boolean res = this.updateById(robotPosition);
        QueryWrapper<RobotLocation> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("position_id", robotPositionVo.getId());
        List<RobotLocation> robotLocationList = iRobotLocationService.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(robotLocationList)) {
            for (RobotLocation robotLocation : robotLocationList) {
                robotLocation.setParentId(robotPositionVo.getRobotLocationParentId());
                robotLocation.setLocationInfo(robotPositionVo.getLocationInfo());
                robotLocation.setLocationCode(robotPositionVo.getLocationCode());
                if (null != user) {
                    robotLocation.setUpdateBy(user.getUsername());
                }
                robotLocation.setUpdateTime(new Date());
                res = iRobotLocationService.updateById(robotLocation);
                if (!res) {
                    break;
                }
            }
        } else {
            String locationInfo = robotPositionVo.getLocationInfo();
            String locationCode = robotPositionVo.getLocationCode();
            if (StrUtil.isNotEmpty(locationInfo) && StrUtil.isNotEmpty(locationCode)) {
                RobotLocation robotLocation = new RobotLocation();
                robotLocation.setParentId(robotPositionVo.getRobotLocationParentId());
                robotLocation.setPositionId(robotPositionVo.getId());
                robotLocation.setLocationInfo(robotPositionVo.getLocationInfo());
                robotLocation.setLocationCode(robotPositionVo.getLocationCode());
                if (null != user) {
                    robotLocation.setCreateBy(user.getUsername());
                }
                robotLocation.setCreateTime(new Date());
                res = iRobotLocationService.save(robotLocation);
            }
        }
        if (res) {
            return new ResultUtil<Boolean>().setData(true, "更新成功");
        } else {
            return new ResultUtil<Boolean>().setErrorMsg("更新失败");
        }
    }

    /**
     * 删除点位数据
     *
     * @param id
     * @return
     */
    @Override
    public boolean delete(String id) {
        RobotPosition robotPosition = this.getById(id);
        if (null != robotPosition) {
            String worldPoseId = robotPosition.getWorldPoseId();
            //删除世界坐标数据
            iRobotWorldPositionService.removeById(worldPoseId);
            //删除点位数据
            this.removeById(id);
            //删除点位的子点数据
            this.remove(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getParentId, id));
            //删除标定点对应的设备标注位置
            iRobotLocationService.remove(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getPositionId, id));
            //删除位置区域的子数据
            RobotLocation robotLocation = iRobotLocationService.getOne(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getPositionId, id));
            if (null != robotLocation) {
                iRobotLocationService.remove(new LambdaQueryWrapper<RobotLocation>().eq(RobotLocation::getParentId, robotLocation.getId()));
                iRobotDisinfectService.remove(new LambdaQueryWrapper<RobotDisinfect>().eq(RobotDisinfect::getRobotLocationId, robotLocation.getId()));
            }
            if (robotPositionDevRelationService.deleteRelation(id)) {
                log.info("关系删除成功");
            }
            //删除导航点
            iRobotTaskItemService.remove(new LambdaQueryWrapper<RobotTaskItem>().eq(RobotTaskItem::getPositionId, id));
            //删除门禁区域
            iRobotEntranceGuardService.remove(new LambdaQueryWrapper<RobotEntranceGuard>().eq(RobotEntranceGuard::getPositionAId, id).or().eq(RobotEntranceGuard::getPositionBId, id));
        }
        return true;
    }

    /**
     * 获取起始原点
     *
     * @return
     */
    private RobotWorldPosition getStartPosition() {
        RobotWorldPosition robotWorldPosition = new RobotWorldPosition();
        robotWorldPosition.setPositionX(0.0);
        robotWorldPosition.setPositionY(0.0);
        robotWorldPosition.setPositionZ(0.0);
        robotWorldPosition.setOrientationW(1.0);
        robotWorldPosition.setOrientationX(0.0);
        robotWorldPosition.setOrientationY(0.0);
        robotWorldPosition.setOrientationZ(0.0);
        robotWorldPosition.setCreateTime(new Date());
        robotWorldPosition.setUpdateTime(new Date());
        return robotWorldPosition;
    }

    /**
     * 获取起始充电点
     */
    private RobotWorldPosition getChargingPosition() {
        RobotWorldPosition robotWorldPosition = new RobotWorldPosition();
        robotWorldPosition.setPositionX(0.0 + 0.6);
        robotWorldPosition.setPositionY(0.0);
        robotWorldPosition.setPositionZ(0.0);
        robotWorldPosition.setOrientationW(0.0);
        robotWorldPosition.setOrientationX(0.0);
        robotWorldPosition.setOrientationY(0.0);
        robotWorldPosition.setOrientationZ(1.0);
        robotWorldPosition.setCreateTime(new Date());
        robotWorldPosition.setUpdateTime(new Date());
        return robotWorldPosition;
    }

    /**
     * 将从ROS中获取的数据转换成世界坐标系
     *
     * @return
     */
    @Override
    public RobotWorldPosition getRobotPoseFromRos() {
        RobotWorldPosition robotWorldPosition = new RobotWorldPosition();
        _Pose pose = null;
        String topicResult = stringRedisTemplate.opsForValue().get(TOPIC + "::" + TopicConstants.ROBOT_POSE_STAMPED);

        if (StrUtil.isNotBlank(topicResult)) {
            _PoseStamped poseStamped = JSON.parseObject(topicResult, _PoseStamped.class);
            pose = poseStamped.pose;
            long poseTime = poseStamped.header.stamp.secs;
            long current = System.currentTimeMillis() / 1000;
            long timeout = Math.abs(current - poseTime);
            if (timeout > 5) {
                log.error("机器人位置信息超时");
                return null;
            }
        }
        if (null != pose) {
            robotWorldPosition.setPositionX(pose.position.x);
            robotWorldPosition.setPositionY(pose.position.y);
            robotWorldPosition.setPositionZ(pose.position.z);
            robotWorldPosition.setOrientationW(pose.orientation.w);
            robotWorldPosition.setOrientationX(pose.orientation.x);
            robotWorldPosition.setOrientationY(pose.orientation.y);
            robotWorldPosition.setOrientationZ(pose.orientation.z);
            robotWorldPosition.setCreateTime(new Date());
            robotWorldPosition.setUpdateTime(new Date());
            return robotWorldPosition;
        } else {
            log.error("无法获取机器人位置信息");
        }
        return null;
    }

    @Override
    public boolean isRepeat(String mapId, String name) {
        List<RobotPosition> robotPositionList = this.list(new LambdaQueryWrapper<RobotPosition>()
                .eq(RobotPosition::getName, name)
                .eq(RobotPosition::getMapId, mapId)
                .notIn(RobotPosition::getType, RobotPositionType.INSIDE_ENTRANCE_GUARD.getType()
                        , RobotPositionType.OUTSIDE_ENTRANCE_GUARD.getType())
                .notIn(RobotPosition::getType, RobotPositionType.INSIDE_ELEVATOR_POSITION.getType()
                        , RobotPositionType.OUTSIDE_ELEVATOR_POSITION.getType())
                .notIn(RobotPosition::getType, RobotPositionType.INSIDE_DISINFECT_BOX_POSITION.getType()
                        , RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_1.getType(),
                        RobotPositionType.OUTSIDE_DISINFECT_BOX_POSITION_2.getType()));
        if (robotPositionList.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 检测标点是否可用
     *
     * @param robotWorldPosition
     * @return
     */
    @Override
    public boolean checkPointValue(RobotWorldPosition robotWorldPosition) {
        _Pose pose = new _Pose();
        _PoseCheckValidReq pointCheckValueReq = new _PoseCheckValidReq();
        pose.position = new _Point();
        pose.orientation = new _Quaternion();
        pose.position.x = robotWorldPosition.getPositionX();
        pose.position.y = robotWorldPosition.getPositionY();
        pose.position.z = robotWorldPosition.getPositionZ();
        pose.orientation.w = robotWorldPosition.getOrientationW();
        pose.orientation.x = robotWorldPosition.getOrientationX();
        pose.orientation.y = robotWorldPosition.getOrientationY();
        pose.orientation.z = robotWorldPosition.getOrientationZ();
        pointCheckValueReq.pose = pose;
        String s = rosBridgeService.callService(POSE_CHECK_VALID, Message.getMessageType(_PoseCheckValidReq.class), JSON.toJSONString(pointCheckValueReq));
        if (StrUtil.isNotEmpty(s)) {
            _PoseCheckValidRep poseCheckValidRep = JSON.parseObject(s, _PoseCheckValidRep.class);
            if (poseCheckValidRep.ret == 0) {
                log.info("点位数据校验通过");
                return true;
            }
        }
        return false;
    }

    /**
     * 标注起始点和充电点
     *
     * @return
     */
    @Override
    public boolean markStartAndChargingPose() {
        // 添加定位点
        return markStartAndChargingPose(null);
    }

    /**
     * 标注起始点和充电点
     *
     * @return
     */
    @Override
    public boolean markStartAndChargingPose(String mapId) {
        // 删除旧数据
        List<RobotPosition> robotPositionList = this.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getMapId, TEMP_MAP_ID));
        if (CollectionUtil.isNotEmpty(robotPositionList)) {
            robotPositionList.forEach(item -> {
                this.delete(item.getId());
            });
        }
        SaveDockPositionMsg saveDockPositionMsg = iRobotChargingService.saveDockPosition();
        if (ObjectUtil.isNull(saveDockPositionMsg)) {
            log.warn("更新充电桩失败，请重新更新");
            return false;
        }
        RobotWorldPosition startRobotWorldPosition = new RobotWorldPosition(saveDockPositionMsg.getDockPose());
        RobotWorldPosition chargingRobotWorldPosition = new RobotWorldPosition(saveDockPositionMsg.getDockNavPose());
        // 添加起始点
        RobotPositionVo robotPositionVo = new RobotPositionVo();
        robotPositionVo.setType(RobotPositionType.START_POSITION.getType());
        robotPositionVo.setName(RobotPositionType.START_POSITION.getValue());
        if (StringUtils.isNotBlank(mapId)) {
            robotPositionVo.setMapId(mapId);
        } else {
            robotPositionVo.setMapId(TEMP_MAP_ID);
        }
        Result<Boolean> resultStart = add(robotPositionVo, startRobotWorldPosition);
        log.info("resultStart:{}", JSON.toJSONString(resultStart));
        // 添加充电点
        robotPositionVo.setId(null);
        robotPositionVo.setType(RobotPositionType.CHARGING_POSITION.getType());
        robotPositionVo.setName(RobotPositionType.CHARGING_POSITION.getValue());
        Result<Boolean> resultCharging = add(robotPositionVo, chargingRobotWorldPosition);
        log.info("resultStart:{}", JSON.toJSONString(resultCharging));
        // 添加定位点
        return true;
    }

    /**
     * 更新充电桩位置
     *
     * @param mapId
     */
    @Override
    public boolean updateDockCheck(String mapId) {
        List<RobotPosition> robotPositionList = this.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getMapId, mapId).eq(RobotPosition::getType, RobotPositionType.CHARGING_POSITION.getType()));
        if (CollectionUtil.isNotEmpty(robotPositionList)) {
            RobotPosition robotPosition = robotPositionList.get(0);
            RobotWorldPosition robotWorldPosition = iRobotWorldPositionService.getById(robotPosition.getWorldPoseId());
            if (robotWorldPosition != null) {
                _PoseCheckValidReq poseCheckValidReq = new _PoseCheckValidReq();
                poseCheckValidReq.pose = new _Pose();
                poseCheckValidReq.pose.position = new _Point();
                poseCheckValidReq.pose.position.x = robotWorldPosition.getPositionX();
                poseCheckValidReq.pose.position.y = robotWorldPosition.getPositionY();
                poseCheckValidReq.pose.position.z = robotWorldPosition.getPositionZ();
                poseCheckValidReq.pose.orientation = new _Quaternion();
                poseCheckValidReq.pose.orientation.x = robotWorldPosition.getOrientationX();
                poseCheckValidReq.pose.orientation.y = robotWorldPosition.getOrientationY();
                poseCheckValidReq.pose.orientation.z = robotWorldPosition.getOrientationZ();
                poseCheckValidReq.pose.orientation.w = robotWorldPosition.getOrientationW();
                String s = rosBridgeService.callService(ServiceConstants.UPDATE_DOCK_POSE, Message.getMessageType(_PoseCheckValidReq.class), JSONObject.toJSONString(poseCheckValidReq));
                if (StrUtil.isNotBlank(s)) {
                    _PoseCheckValidRep poseCheckValidRep = JSONObject.parseObject(s, _PoseCheckValidRep.class);
                    if (poseCheckValidRep.ret == 0) {
                        log.info("更新充电桩位置成功");
                        return true;
                    }
                }
            }
        }
        return false;
    }

    /**
     * 保存地图完成时，更新充电桩位置和充电点
     *
     * @return true/false
     */
    @Override
    public boolean updateDockPointAndOriginBySaveMap() {
        String mapId = RedisUtil.getHash(ROBOT_SYS_INFO, CURRENT_MAP);
        if (StringUtils.isBlank(mapId)) {
            log.info("当前地图为空");
            return false;
        }
        // 更新起始点和充电点地图id
        List<RobotPosition> list = this.list(new LambdaQueryWrapper<RobotPosition>().eq(RobotPosition::getMapId, TEMP_MAP_ID));
        if (CollectionUtil.isNotEmpty(list)) {
            for (RobotPosition robotPosition : list) {
                robotPosition.setMapId(mapId);
                this.updateById(robotPosition);
            }
        }
        return true;
    }

    /**
     * 获取深度相机图片路劲
     *
     * @return 图片路劲
     */
    @Override
    public String getImagePath() {
        _ImageReq imageReq = new _ImageReq();
        imageReq.id = 1;
        String s = rosBridgeService.callService("/capture_picture",
                Message.getMessageType(_ImageReq.class),
                JSONObject.toJSONString(imageReq));
        String picturePath = null;
        if (StrUtil.isNotBlank(s)) {
            _ImageRep imageRep = JSON.parseObject(s, _ImageRep.class);
            String path = imageRep.picture_path;
            //获取图片名称，包括后缀名。
            String[] split = path.split("/");
            String name = split[split.length - 1];
            picturePath = name;
            //将图片压缩一下
            compressImage(picturePath);
        }
        return picturePath;
    }

    /**
     * 获取机器人位置信息
     *
     * @return json格式
     */
    @Override
    public String getRobotPoseJson() {
        return RedisUtil.getTopicValue(TopicConstants.ROBOT_POSE);
    }

    /**
     * 获取机器人位置信息
     *
     * @return 对象格式
     */
    @Override
    public _Pose getRobotPose() {
        _Pose pose = null;
        String robotPose = getRobotPoseJson();
        if (StringUtils.isNotBlank(robotPose)) {
            pose = JSONObject.parseObject(robotPose, _Pose.class);
        }
        return pose;
    }

    /**
     * 获取机器人位置信息
     *
     * @return json格式
     */
    @Override
    public String getRobotPoseStampedJson() {
        return RedisUtil.getTopicValue(TopicConstants.ROBOT_POSE_STAMPED);
    }

    /**
     * 获取机器人位置信息
     *
     * @return 对象格式
     */
    @Override
    public _PoseStamped getRobotPoseStamped() {
        _PoseStamped pose = null;
        String robotPose = getRobotPoseStampedJson();
        if (StringUtils.isNotBlank(robotPose)) {
            pose = JSONObject.parseObject(robotPose, _PoseStamped.class);
        }
        return pose;
    }

    /**
     * 压缩图片大小
     *
     * @param pictureName 图片路径加名称
     */
    public static void compressImage(String pictureName) {
        try {
            //图片所在路径
            BufferedImage templateImage = ImageIO.read(new File("/home/<USER>/udrive_v1_1_1/ws/image/" + pictureName));
            //原始图片的长度和宽度
            int height = templateImage.getHeight();
            int width = templateImage.getWidth();
            //通过比例压缩
            float scale = 0.5f;
            //通过固定长度压缩
            /*int doWithHeight = 100;
            int dowithWidth = 300;*/
            //压缩之后的长度和宽度
            int doWithHeight = (int) (scale * height);
            int dowithWidth = (int) (scale * width);
            BufferedImage finalImage = new BufferedImage(dowithWidth, doWithHeight, BufferedImage.TYPE_INT_RGB);
            finalImage.getGraphics().drawImage(templateImage.getScaledInstance(dowithWidth, doWithHeight, java.awt.Image.SCALE_SMOOTH), 0, 0, null);
            //创建存放压缩后的深度相机照片的文件夹
            createFolder("/home/<USER>/udrive_v1_1_1/ws/compressImage/");
            saveImage(finalImage, "/home/<USER>/udrive_v1_1_1/ws/compressImage/" + pictureName);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 解决：程序包com.sun.image.codec.jpeg不存在 ；问题的方法
     *
     * @param dstImage
     * @param dstName
     * @throws IOException
     */
    public static void saveImage(BufferedImage dstImage, String dstName) throws IOException {
        String formatName = dstName.substring(dstName.lastIndexOf(".") + 1);
        ImageIO.write(dstImage, formatName, new File(dstName));
    }

    /**
     * 创建文件夹
     *
     * @param destDirName 文件夹路径
     * @return true/false
     */
    public static boolean createFolder(String destDirName) {
        File dir = new File(destDirName);
        if (dir.exists()) {
            return false;
        }
        //创建单个目录
        return dir.mkdirs();
    }

    /**
     * 获取两个点路径上的点位个数
     *
     * @param start 起始点
     * @param end   结束点
     * @return 两点之间路线上的数量
     */
    @Override
    public Integer getRoutePointSum(_PoseStamped start, _PoseStamped end) {

        String currentMap = Objects.requireNonNull(stringRedisTemplate.opsForHash().get(ROBOT_SYS_INFO, CURRENT_MAP)).toString();
        List<RobotEntranceGuard> robotEntranceGuardList = iRobotEntranceGuardService.list(new LambdaQueryWrapper<RobotEntranceGuard>()
                .eq(RobotEntranceGuard::getMapId, currentMap));

        if (CollectionUtil.isEmpty(robotEntranceGuardList)) {
            robotEntranceGuardList = new ArrayList<>();

        }
        _Point[] entranceGuardArr = new _Point[robotEntranceGuardList.size()];
        for (int i = 0; i < robotEntranceGuardList.size(); i++) {
            _Point point = new _Point();
            RobotEntranceGuard robotEntranceGuard = robotEntranceGuardList.get(i);
            point.x = robotEntranceGuard.getCenterX();
            point.y = robotEntranceGuard.getCenterY();
            point.z = robotEntranceGuard.getRadius();
            entranceGuardArr[i] = point;
        }

        _OriginalPathReq originalPathReq = new _OriginalPathReq();
        originalPathReq.cmd = 0;
        originalPathReq.door_type = 0;
        originalPathReq.robot_pose_info = start.pose;
        originalPathReq.goal_pose_info = end.pose;
        originalPathReq.door_access = entranceGuardArr;
        String s = rosBridgeService.callService(ServiceConstants.ORIGINAL_PATH, Message.getMessageType(_OriginalPathReq.class), JSONObject.toJSONString(originalPathReq));
        if (StrUtil.isNotEmpty(s)) {
            _OriginalPathRep originalPathRep = JSON.parseObject(s, _OriginalPathRep.class);
            if (originalPathRep.ret >= 0) {
                log.info("目标路径服务调用成功,获取路径数据");
                return originalPathRep.ret;
            }
        }
        return 0;
    }

    /**
     * 获取两个点路径上的点位个数
     *
     * @param startId 起始点
     * @param endId   结束点
     * @return 路径上点的数量
     */
    @Override
    public Integer getRoutePointSum(String startId, String endId) {
        _PoseStamped start = iRobotWorldPositionService.getPoseStamped(startId);
        _PoseStamped end = iRobotWorldPositionService.getPoseStamped(endId);
        return getRoutePointSum(start, end);
    }

    /**
     * 获取停靠点在地图的最短路径
     * 当点位小于3个时，无需排序
     *
     * @param positionIdList 停靠点列表
     * @return 排序后停靠点列表
     */
    @Override
    public List<String> shortestPaths(List<String> positionIdList) {
        int sortedSize = 3;
        if (CollectionUtil.size(positionIdList) < sortedSize) {
            return positionIdList;
        }
        // 机器人当前位置
        _PoseStamped start = getRobotPoseStamped();
        return shortestPaths(start, positionIdList);
    }

    /**
     * 获取停靠点在地图的最短路径
     *
     * @param start          开始点
     * @param positionIdList 停靠点列表
     * @return 排序后停靠点列表
     */
    @Override
    public List<String> shortestPaths(_PoseStamped start, List<String> positionIdList) {
        // id -> sum
        Map<String, Integer> idToSumMap = new HashMap<>(16);
        positionIdList.forEach(item -> {
            _PoseStamped end = iRobotWorldPositionService.getPoseStamped(item);
            idToSumMap.put(item, -1);
            if (ObjectUtil.isNotNull(end)) {
                Integer sum = getRoutePointSum(start, end);
                if (ObjectUtil.isNotNull(sum)) {
                    idToSumMap.put(item, sum);
                }
            }
        });
        return idToSumMap.entrySet().stream()
                .sorted(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }

    /**
     * 下一个点是否位消毒
     *
     * @return true/false
     */
    @Override
    public boolean isNextDisinfectPoint() {
        long toDoListSize = RedisUtil.getListLength(TO_DO_LIST);
        String toDoContent = RedisUtil.getIndexList(TO_DO_LIST, toDoListSize - 2);
        if (StringUtils.isBlank(toDoContent)) {
            return false;
        }
        String[] toDoContentArr = toDoContent.split("#");
        String positionId = toDoContentArr[1];
        RobotPosition robotPosition = getById(positionId);
        if (ObjectUtil.isNull(robotPosition) || toDoListSize < 2) {
            return false;
        }
        return RobotPositionType.ROOM_POSITION.getType().equals(robotPosition.getType());
    }

    /**
     * 设置定位状态
     *
     * @param status 定位状态
     */
    @Override
    public void setPositionStatus(String status) {
        RedisUtil.hset(ROBOT_SYS_INFO, POSITION_STATUS, status);
    }

    /**
     * 根据点位类型和地图ID 获取点位ID
     *
     * @param typeName 类型名称
     * @param mapId    地图ID
     * @return 点位列表
     */
    @Override
    public List<String> getPositionIdByTypeAndMapId(String typeName, String mapId) {
        QueryWrapper<RobotPosition> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", typeName)
                .eq("map_id", mapId);
        List<RobotPosition> positionList = this.list(queryWrapper);
        if (CollectionUtil.isNotEmpty(positionList)) {
            return positionList.stream().map(RobotPosition::getId).collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * 删除点位 根据类型名称和地图ID
     *
     * @param typeName
     * @param mapId
     */
    @Override
    public void deleteByTypeNameAndMapId(String typeName, String mapId) {
        List<String> positionList = getPositionIdByTypeAndMapId(typeName, mapId);
        if (CollectionUtil.isNotEmpty(positionList)) {
            for (String id : positionList) {
                log.info("positionId:" + id);
                delete(id);
            }
        }
    }
}