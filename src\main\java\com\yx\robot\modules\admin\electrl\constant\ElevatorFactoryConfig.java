package com.yx.robot.modules.admin.electrl.constant;

/**
 * 电梯控制配置
 *
 * <AUTHOR>
 * @date 2021/12/11
 */
public interface ElevatorFactoryConfig {

//    String APP_ID = "1470334221091737600"; // 深圳

//    String APP_SECRET = "22711C75AD1B5E92"; // 深圳

//    String PROJECT_ID = "00005530"; // 深圳

//    String ROBOT_ID = "000055303499"; // 深圳

//    String DEVICE_UNIQUE = "0000553050005"; //A2

//    String DEVICE_UNIQUE = "0000553050021"; //A6

//    String DEVICE_UNIQUE = "0000553050013"; //A4

    String APP_ID = "1499218945721634816"; // 测试

    String APP_SECRET = "CCA2966B7E620014"; // 测试

    String PROJECT_ID = "00005671"; // 测试

    String ROBOT_ID = "000056717285"; // 测试

    String DEVICE_UNIQUE = "0000567110001"; // 测试

    String OPEN_TIME = "99";
}
