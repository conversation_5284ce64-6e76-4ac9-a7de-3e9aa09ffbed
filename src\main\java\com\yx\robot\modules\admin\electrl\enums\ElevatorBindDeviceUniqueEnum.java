package com.yx.robot.modules.admin.electrl.enums;

/**
 * <AUTHOR>
 * @date 2021/12/18
 */
public enum ElevatorBindDeviceUniqueEnum {

    /**
     * A客梯
     */
//    A("A","0000553010005"), // A2
//    A("A","0000553010021"), // A6
    // A4
//    A("A", "0000553010013"),
    A("A", "0000567110001"),
    /**
     * B客梯
     */
//    B("B","0000553010006"), // A2
//    B("B","0000553010022"), // A6
    // A4
    B("B", "0000553010014"),
    /**
     * 外部
     */
    AB("AB", "");


    final String name;

    final String deviceUnique;

    ElevatorBindDeviceUniqueEnum(String name, String deviceUnique) {
        this.name = name;
        this.deviceUnique = deviceUnique;
    }

    public String getName() {
        return name;
    }

    public String getDeviceUnique() {
        return deviceUnique;
    }
}
