# 配置文件加密key 生产环境中可通过环境变量、命令行等形式进行设置
jasypt:
  encryptor:
    password: yx

server:
  port: 6780
  servlet:
    context-path: /
  tomcat:
    uri-encoding: UTF-8
    max-threads: 1000
    min-spare-threads: 30

robot:
  webVersion:
    number: WEB_2.3.8.20230726.U3.beta
    publishDate: 2023-07-26

# 忽略鉴权url
ignored:
  urls:
    - /yx/actuator/**
    - /yx/admin/**
    - /yx/dictData/getByType/**
    - /yx/user/regist
    - /yx/common/**
    - /yx/websocket/** #websocket开放连接
    - /yx/ws/ros/** #websocket开放连接
    - /yx/api-v1/robotMap/downloadMap/** #下载地图开放连接
    - /druid/**
    - /swagger-ui.html
    - /swagger-resources/**
    - /swagger/**
    - /**/v2/api-docs
    - /**/*.js
    - /**/*.css
    - /**/*.png
    - /**/*.ico
    - /**/*.pgm
    - /**/*.yaml
    - /**/*.mp4
    # 提供给云端调用的接口
    - /yx/api-v1/robotDefine/**
    - /yx/api-v1/robotLocation/**
    - /yx/api-v1/robotTask/**
    - /yx/api-v1/robotMap/**
    - /yx/api-v1/robotPosition/**
    - /yx/api-v1/robotRoute/**
    - /yx/api-v1/robotAreas/**
    - /yx/api-v1/robotCheckHistoryRecord/**
    - /yx/api-v1/robotEntranceGuard/**
    - /yx/api-v1/robotTaskRecord/**
    - /yx/api-v1/robotChargingRecord/**
    - /yx/api-v1/robotSwitchRecord/**
    - /yx/api-v1/**

# Actuator
management:
  health:
    rabbit:
      enabled: false
    status:
      http-mapping:
        DOWN: 200
        OUT_OF_SERVICE: 200
        FATAL: 200
        UNKNOWN: 200
    # 暂未用到ES 关闭其健康检查
    elasticsearch:
      enabled: false
  endpoint:
    health:
      show-details: always
  endpoints:
    web:
      base-path: /yx/actuator/
      exposure:
        include: '*'

# Mybatis-plus
mybatis-plus:
  mapper-locations: classpath*:mapper/*.xml
#  #打印SQL语句配置
#  configuration:
#    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
logging:
  config: classpath:logback-spring.xml
#  level:
#    com.yx.robot.modules.admin.dao: debug


