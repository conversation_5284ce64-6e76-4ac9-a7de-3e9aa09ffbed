package com.yx.robot.modules.admin.jike.service;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.electrl.constant.ElevatorTopicConstants;
import com.yx.robot.modules.admin.jike.constant.JikeConstants;
import com.yx.robot.modules.admin.jike.dto.LoginInfoDto;
import com.yx.robot.modules.admin.jike.dto.UvrLoginDto;
import com.yx.robot.modules.admin.jike.enums.UvrFactoryEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;

/**
 * jike平台数据上传
 */
@Component
@Slf4j
public class JikePlatformCore {

    @Autowired
    private JikePlatformService jikePlatformService;

    /**
     * 启动时先清空token
     */

    public void handleCleanToken() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            jedis.del(ElevatorTopicConstants.ELEVATOR_TOPIC + "::" + ElevatorTopicConstants.TOKEN);
            jedis.del(JikeConstants.JIKE + "::" + JikeConstants.TOKEN);
            jedis.del(JikeConstants.UVR + "::" + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            jedis.del(JikeConstants.UVR + "::" + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    /**
     * 刷新机科化平台token
     */
    public void handleRefreshJikeToken() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String token = jedis.get(JikeConstants.JIKE + "::" + JikeConstants.TOKEN);
            if(StrUtil.isBlank(token)) {
                LoginInfoDto loginInfoDto = new LoginInfoDto();
                JSONObject result = jikePlatformService.refreshToken(loginInfoDto);
                if(result != null && result.get("access_token") != null) {
                    String accessToken = result.get("access_token").toString();
                    jedis.set(JikeConstants.JIKE + "::" + JikeConstants.TOKEN,accessToken);
                    // 平台24小时过期，自定义12小时过期
                    jedis.expire(JikeConstants.JIKE + "::" + JikeConstants.TOKEN, 60 * 60 * 12);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }

    public void handleRefreshUvrToken() {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            String token1 = jedis.get(JikeConstants.UVR + "::"  + UvrFactoryEnum.UBTECH.getValue() + "::" + JikeConstants.TOKEN);
            String token2 = jedis.get(JikeConstants.UVR + "::"  + UvrFactoryEnum.SAITE.getValue() + "::" + JikeConstants.TOKEN);
            if(StrUtil.isBlank(token1) && StrUtil.isBlank(token2)) {
                UvrLoginDto uvrLoginDto = new UvrLoginDto();
                jikePlatformService.uvrRefreshToken(uvrLoginDto);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
    }
}
