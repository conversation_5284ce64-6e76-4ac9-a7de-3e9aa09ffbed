package com.yx.robot.modules.admin.scheduler;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.config.wesocket.WebSocketRosServer;
import com.yx.robot.modules.admin.dto.MetaDataDto;
import com.yx.robot.modules.admin.dto.ScanDto;
import com.yx.robot.modules.admin.message.*;
import com.yx.robot.modules.admin.service.IRobotMapService;
import com.yx.robot.modules.admin.service.IRobotPositionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.yx.robot.common.constant.TopicConstants.PATH_RECORD;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022/7/5 17:08
 */
@Component
@Order(2)
@Slf4j
public class WebsocketRosScheduler {

    private final IRobotPositionService iRobotPositionService;

    private final IRobotMapService iRobotMapService;

    private final WebSocketRosServer webSocketRosServer;

    private ScheduledExecutorService executorService = Executors.newSingleThreadScheduledExecutor();

    @Autowired
    public WebsocketRosScheduler(IRobotPositionService iRobotPositionService,
                                 IRobotMapService iRobotMapService,
                                 WebSocketRosServer webSocketRosServer) {
        this.iRobotPositionService = iRobotPositionService;
        this.iRobotMapService = iRobotMapService;
        this.webSocketRosServer = webSocketRosServer;
    }

    public void schedulerInfo() {
        log.info("schedulerInfo");
        executorService.scheduleAtFixedRate(() -> {
            try {
                sendPoseInfoScheduler();
                sendMetadata();
//                sendRoutedata();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }, 0, 2, TimeUnit.SECONDS);
    }

    //    @Scheduled(fixedRate = 2000)
    public void sendPoseInfoScheduler() {
        if (webSocketRosServer.getOnlineCount() < 1) {
            return;
        }
        try {
            Map<String, Object> jsonObject = new HashMap<>(16);
            String pose = iRobotPositionService.getRobotPoseJson();
            if (StringUtils.isBlank(pose)) {
                return;
            }
            jsonObject.put("pose", pose);
            webSocketRosServer.sendAllMessage(JSON.toJSONString(jsonObject), null);
        } catch (IOException e) {
            log.warn("机器人位置返回失败");
            e.printStackTrace();
        }
    }

    public void sendScanInfo() {
        if (webSocketRosServer.getOnlineCount() < 1) {
            return;
        }
        try {
            Map<String, Object> jsonObject = new HashMap<>(16);
            _LaserScan laserScan = iRobotMapService.getRobotScan();
            if (ObjectUtil.isNotNull(laserScan)) {
                ScanDto scan = new ScanDto(laserScan);
                jsonObject.put("scan", scan);
            }
            webSocketRosServer.sendAllMessage(JSON.toJSONString(jsonObject), null);
        } catch (IOException e) {
            log.warn("机器人雷达信息返回失败");
            e.printStackTrace();
        }
    }

    //    @Scheduled(fixedRate = 2000)
    public void sendMetadata() {
        if (webSocketRosServer.getOnlineCount() < 1) {
            return;
        }
        try {
            Map<String, Object> jsonObject = new HashMap<>(16);
            _MapMetadata mapMetadata = iRobotMapService.getMapMetaData();
            if (ObjectUtil.isNotNull(mapMetadata)) {
                MetaDataDto metaDataDto = new MetaDataDto(mapMetadata);
                jsonObject.put("metadata", JSON.toJSONString(metaDataDto));
            }
            webSocketRosServer.sendAllMessage(JSON.toJSONString(jsonObject), null);
        } catch (IOException e) {
            log.warn("机器人雷达信息返回失败");
            e.printStackTrace();
        }
    }

    public void sendRoutedata() {
        if (webSocketRosServer.getOnlineCount() < 1) {
            return;
        }
        try {
            Map<String, Object> jsonObject = new HashMap<>(16);
            String pathStr = RedisUtil.getTopicValue(PATH_RECORD);
            if (StrUtil.isNotBlank(pathStr)) {
                _Path path = JSONObject.parseObject(pathStr, _Path.class);
                _PoseStamped[] routePoseList = Objects.requireNonNull(path).poses;
                ArrayList<_Pose> poses = new ArrayList<>();
                for (_PoseStamped p : routePoseList) {
                    poses.add(p.pose);
                }
                jsonObject.put("routedata", JSON.toJSONString(poses));
            }
            webSocketRosServer.sendAllMessage(JSON.toJSONString(jsonObject), null);
        } catch (IOException e) {
            log.warn("机器人路径信息返回失败");
            e.printStackTrace();
        }
    }
}
