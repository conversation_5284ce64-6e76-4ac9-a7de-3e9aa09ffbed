package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yx.robot.common.constant.TopicConstants;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dao.mapper.RobotDisinfectantRecordMapper;
import com.yx.robot.modules.admin.entity.RobotDisinfectantRecord;
import com.yx.robot.modules.admin.message.Message;
import com.yx.robot.modules.admin.message._Disinfectant;
import com.yx.robot.modules.admin.message._Uint8;
import com.yx.robot.modules.admin.service.IRobotDisinfectantRecordService;
import com.yx.robot.modules.admin.service.core.RosBridgeService;
import com.yx.robot.modules.admin.service.core.RosWebService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.util.Date;

import static com.yx.robot.common.constant.RosWebConstants.*;
import static com.yx.robot.common.constant.TopicConstants.WATER_CTRL;

/**
 * 机器人消毒液变化记录接口实现
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Transactional
public class IRobotDisinfectantRecordServiceImpl extends ServiceImpl<RobotDisinfectantRecordMapper, RobotDisinfectantRecord> implements IRobotDisinfectantRecordService {

    @Autowired
    private RosWebService rosWebService;

    @Autowired
    private RosBridgeService rosBridgeService;

//    @Autowired
//    public IRobotDisinfectantRecordServiceImpl(RosWebService rosWebService) {
//        this.rosWebService = rosWebService;
//    }

    /**
     * 处理消毒水控制
     *
     * @param action    开始(1) 停止(0)
     * @param operation 进水(1) 出水(-1)
     * @return true / false
     */
    @Override
    public boolean handleRobotDisinfectant(String operation, String action) {
        Jedis jedis = null;
        try {
            jedis = RedisUtil.getJedis();
            if (jedis != null) {
                String waterInStatusStr = jedis.get(TOPIC + "::" + TopicConstants.WATER_IN_STATUS);
                String waterOutStatusStr = jedis.get(TOPIC + "::" + TopicConstants.WATER_OUT_STATUS);
                boolean waterInStatus = !StrUtil.isNotEmpty(waterInStatusStr)
                        || JSON.parseObject(waterInStatusStr, _Uint8.class).data != 0;
                boolean waterOutStatus = !StrUtil.isNotEmpty(waterOutStatusStr)
                        || JSON.parseObject(waterOutStatusStr, _Uint8.class).data != 0;
                if (operation.equals(INLET)) {
                    if (!waterInStatus && START.equals(action)) {
                        rosWebService.waterCtrl(operation, action);
                        RobotDisinfectantRecord robotDisinfectantRecord = new RobotDisinfectantRecord();
                        robotDisinfectantRecord.setBeginChangeTime(new Date());
                        String disinfectant = jedis.get(TOPIC + "::" + TopicConstants.DISINFECTANT);
                        robotDisinfectantRecord.setBeginQuantity(StrUtil.isEmpty(disinfectant) ? new BigDecimal("0.00") :
                                NumberUtil.round(JSONObject.parseObject(disinfectant, _Disinfectant.class).data * 100, 2));
                        robotDisinfectantRecord.setOperationType(Integer.valueOf(operation));
                        this.save(robotDisinfectantRecord);
                        jedis.hset(ROBOT_SYS_INFO, LAST_DISINFECTANT_RECORD_ID_1, robotDisinfectantRecord.getId());
                    } else if (waterInStatus && END.equals(action)) {
                        rosWebService.waterCtrl(operation, action);
                        String lastDisinfectantRecordId = jedis.hget(ROBOT_SYS_INFO, LAST_DISINFECTANT_RECORD_ID_1);
                        RobotDisinfectantRecord robotDisinfectantRecord = this.getById(lastDisinfectantRecordId);
                        if (robotDisinfectantRecord != null) {
                            robotDisinfectantRecord.setEndChangeTime(new Date());
                            Date beginChangeTime = robotDisinfectantRecord.getBeginChangeTime();
                            Date endChangeTime = robotDisinfectantRecord.getEndChangeTime();
                            long consume = (endChangeTime.getTime() - beginChangeTime.getTime()) / 1000;
                            robotDisinfectantRecord.setConsumeTime(consume);
                            String disinfectant = jedis.get(TOPIC + "::" + TopicConstants.DISINFECTANT);
                            robotDisinfectantRecord.setEndQuantity(StrUtil.isEmpty(disinfectant) ? new BigDecimal("0.00") :
                                    NumberUtil.round(JSONObject.parseObject(disinfectant, _Disinfectant.class).data * 100, 2));
                            robotDisinfectantRecord.setChangeQuantity(NumberUtil.round(Math.abs((robotDisinfectantRecord.getEndQuantity().doubleValue()
                                    - robotDisinfectantRecord.getBeginQuantity().doubleValue())), 2));
                            this.updateById(robotDisinfectantRecord);
                        }
                        return true;
                    } else {
                        return false;
                    }
                } else if (operation.equals(EFFLUENT)) {
                    if (!waterOutStatus && action.equals(START)) {
                        rosWebService.waterCtrl(operation, action);
                        RobotDisinfectantRecord robotDisinfectantRecord = new RobotDisinfectantRecord();
                        robotDisinfectantRecord.setBeginChangeTime(new Date());
                        String disinfectant = jedis.get(TOPIC + "::" + TopicConstants.DISINFECTANT);
                        robotDisinfectantRecord.setBeginQuantity(StrUtil.isEmpty(disinfectant) ? new BigDecimal("0.00") :
                                NumberUtil.round(JSONObject.parseObject(disinfectant, _Disinfectant.class).data * 100, 2));
                        robotDisinfectantRecord.setOperationType(Integer.valueOf(operation));
                        this.save(robotDisinfectantRecord);
                        jedis.hset(ROBOT_SYS_INFO, LAST_DISINFECTANT_RECORD_ID_2, robotDisinfectantRecord.getId());
                    } else if (waterOutStatus && action.equals(END)) {
                        rosWebService.waterCtrl(operation, action);
                        String lastDisinfectantRecordId = jedis.hget(ROBOT_SYS_INFO, LAST_DISINFECTANT_RECORD_ID_2);
                        RobotDisinfectantRecord robotDisinfectantRecord = this.getById(lastDisinfectantRecordId);
                        if (robotDisinfectantRecord != null) {
                            robotDisinfectantRecord.setEndChangeTime(new Date());
                            Date beginChangeTime = robotDisinfectantRecord.getBeginChangeTime();
                            Date endChangeTime = robotDisinfectantRecord.getEndChangeTime();
                            long consume = (endChangeTime.getTime() - beginChangeTime.getTime()) / 1000;
                            robotDisinfectantRecord.setConsumeTime(consume);
                            String disinfectant = jedis.get(TOPIC + "::" + TopicConstants.DISINFECTANT);
                            robotDisinfectantRecord.setEndQuantity(StrUtil.isEmpty(disinfectant) ? new BigDecimal("0.00") :
                                    NumberUtil.round(JSONObject.parseObject(disinfectant, _Disinfectant.class).data * 100, 2));
                            robotDisinfectantRecord.setChangeQuantity(NumberUtil.round(Math.abs((robotDisinfectantRecord.getEndQuantity().doubleValue()
                                    - robotDisinfectantRecord.getBeginQuantity().doubleValue())), 2));
                            this.updateById(robotDisinfectantRecord);
                        }
                        return true;
                    } else {
                        return false;
                    }
                }
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            RedisUtil.closeJedis(jedis);
        }
        return false;
    }

    @Override
    public boolean outWaterControl(String action) {
        _Uint8 uint8 = new _Uint8();
        uint8.data = Short.valueOf(action);
        rosBridgeService.publish(WATER_CTRL, Message.getMessageType(_Uint8.class), JSON.toJSONString(uint8));
        return true;
    }

}