package com.yx.robot.common.enums;

/**
 * 机器人检测项目
 *
 * <AUTHOR>
 * @date 2020/09/18
 */
public enum RobotCheckProject {

    /**
     * 0,1,"节点状态检测"
     */
    NODES_CHECK(0, 1, "节点状态检测"),

    /**
     * 1,1,"雷达状态检测"
     */
    LIDAR_CHECK(1, 1, "雷达状态检测"),

    /**
     * 1,1,"深度状态检测"
     */
    DEPTH_CHECK(1, 1, "深度状态检测"),

    /**
     * 1,1,"imu状态检测"
     */
    IMU_CHECK(1, 1, "imu状态检测"),

    /**
     * 1,1,"电机状态检测"
     */
    MOTOR_CHECK(1, 1, "电机状态检测"),

    /**
     * 1,1,"电池状态检测"
     */
    BMS_CHECK(1, 1, "电池状态检测"),

    /**
     * 1,1,"下层板状态检测"
     */
    SINGLECHIP_BOTTOM_CHECK(1, 1, "下层板状态检测"),

    /**
     * 1,1,"防撞条状态检测"
     */
    COLLISION_CHECK(1, 1, "防撞条状态检测"),

    /**
     * 1,1,"急停开关状态检测"
     */
    STOP_BTN_CHECK(1, 1, "急停开关状态检测"),

    /**
     * 1, 1, "充电接头状态检测"
     */
    AUTO_CHARGE_CHECK(1, 1, "充电接头状态检测"),

    /**
     * 1, 1, "电源管理板状态检测"
     */
    SYSTEM_CHARGE_CHECK(1, 1, "电源管理板状态检测"),

    /**
     * 1, 1, "转向灯状态检测"
     */
    TURN_LIGHT_CHECK(1, 1, "转向灯状态检测"),

    /**
     * 1, 1, "电源按键状态检测"
     */
    POWER_KEY_CHECK(1, 1, "电源按键状态检测"),

    /**
     * 2, 1, "上层板状态检测"
     */
    SINGLECHIP_TOP_CHECK(2, 1, "上层板状态检测"),

    /**
     * 2, 1, "眼睛状态检测"
     */
    EYE_CHECK(2, 1, "眼睛状态检测"),

    /**
     * 2, 1, "广告牌状态检测"
     */
    AD_CHECK(2, 1, "广告牌状态检测"),

    /**
     * 2, 1, "触摸板状态检测"
     */
    TOUCH_CHECK(2, 1, "触摸板状态检测"),

    /**
     * 2, 1, "门的状态检测"
     */
    DOOR_CHECK(2, 1, "门的状态检测"),

    /**
     * 2, 1, "led灯状态检测"
     */
    LED_LIGHT_CHECK(2, 1, "led灯状态检测"),

    /**
     * 2, 1, "红外摄像头状态检测"
     */
    INFRA_LIGHT_CHECK(2, 1, "红外摄像头状态检测"),

    /**
     * 3, 1, "消毒板状态检测"
     */
    DOCTOR_CHECK(3, 1, "消毒板状态检测"),

    /**
     * 3, 1, "脉冲灯状态检测"
     */
    PULSE_CHECK(3, 1, "脉冲灯状态检测"),

    /**
     * 3, 1, "紫外灯状态检测"
     */
    ULRAY_CHECK(3, 1, "紫外灯状态检测"),

    /**
     * 3, 1, "喷雾状态检测"
     */
    SPRAY_CHECK(3, 1, "喷雾状态检测"),

    /**
     * 3, 1, "风扇状态检测"
     */
    FAN_CHECK(3, 1, "风扇状态检测"),

    /**
     * 3, 1, "进出水状态检测"
     */
    PUMP_CHECK(3, 1, "进出水状态检测"),

    /**
     * 3, 1, "水箱水位状态检测"
     */
    WIQUID_CHECK(3, 1, "水箱水位状态检测"),

    /**
     * 3, 1, "环形灯状态检测"
     */
    ROLL_LED(3, 1, "环形灯状态检测");

    /**
     *  0、节点状态检测
     *  1、下层板
     *  2、消毒板
     */
    private final Integer type;

    private Integer step;

    private final String title;

    RobotCheckProject(Integer type, Integer step, String title) {
        this.type = type;
        this.title = title;
    }

    public Integer getType() {
        return this.type;
    }

    public Integer getStep() {
        return this.step;
    }

    public String getTitle() {
        return this.title;
    }
}
