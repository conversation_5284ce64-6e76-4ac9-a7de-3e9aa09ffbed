package com.yx.robot.modules.admin.entity;

import com.yx.robot.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@TableName("t_robot_route")
@ApiModel(value = "机器人路线")
public class RobotRoute extends BaseEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "序号")
    private String orderNumber;

    @ApiModelProperty(value = "地图id")
    private String mapId;

    @ApiModelProperty(value = "位置区域")
    private String locationInfo;

    @ApiModelProperty(value = "位置编号")
    private String locationCode;

    @ApiModelProperty(value = "点位数据")
    private String positions;

    @ApiModelProperty(value = "点位个数")
    private Integer positionNumber;

    @ApiModelProperty(value = "是否默认")
    private String isDefault;

    @ApiModelProperty(value = "排序值")
    private BigDecimal sortOrder;
}