package com.yx.robot.modules.admin.serviceimpl;

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.yx.robot.common.constant.RobotBaseInfoConstant;
import com.yx.robot.common.utils.RedisUtil;
import com.yx.robot.modules.admin.dto.RobotBaseInfoVo;
import com.yx.robot.modules.admin.service.IRobotBaseInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.charset.StandardCharsets;

import static com.yx.robot.common.constant.RobotRedisConstants.*;

/**
 * 机器人基本信息处理类
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/4/13 14:21
 */
@Service
@Slf4j
public class IRobotBaseInfoServiceImpl implements IRobotBaseInfoService {

    @Value("${robot.baseInfo}")
    private String robotBaseInfoPath;

    @Value("${robot.webVersion.number}")
    private String robotWebVersion;

    @Value("${robot.webVersion.publishDate}")
    private String robotWebPublishDate;

    /**
     * 从系统文件（只会在生产的时候修改）中获取机器人基础信息
     * 初始化机器人信息
     * <p>
     * 将这些信息保存到RobotBaseInfoConstant
     */
    @Override
    public RobotBaseInfoVo getBaseInfoFromRobot() {
        RobotBaseInfoVo baseInfoVo = new RobotBaseInfoVo();
        try {
            JSON json = JSONUtil.readJSON(new File(robotBaseInfoPath), StandardCharsets.UTF_8);
            baseInfoVo = JSONObject.parseObject(json.toString(), RobotBaseInfoVo.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.error("初始化机器人基础信息失败,json格式转化异常：{}", e.getMessage());
        }
        baseInfoVo.setWebVersion(robotWebVersion);
        baseInfoVo.setPublishDate(robotWebPublishDate);
        RobotBaseInfoConstant.type = baseInfoVo.getType();
        RobotBaseInfoConstant.name = baseInfoVo.getName();
        RobotBaseInfoConstant.robotNumber = baseInfoVo.getRobotNumber();
        RobotBaseInfoConstant.version = baseInfoVo.getVersion();
        RobotBaseInfoConstant.productDate = baseInfoVo.getProductDate();
        RobotBaseInfoConstant.serialNumber = baseInfoVo.getSerialNumber();
        RobotBaseInfoConstant.webVersion = baseInfoVo.getWebVersion();
        RobotBaseInfoConstant.rosVersion = baseInfoVo.getRosVersion();
        RobotBaseInfoConstant.robotWebPublishDate = robotWebPublishDate;
        return baseInfoVo;
    }

    /**
     * 保存机器人基本信息到redis中
     * 只有在系统重启时，执行该操作
     *
     * @return true:缓存成功 false:缓存失败
     */
    @Override
    public boolean catchBaseInfoToRedis() {
        try {
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_TYPE, RobotBaseInfoConstant.type);
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_NAME, RobotBaseInfoConstant.name);
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_ROBOT_NUMBER, RobotBaseInfoConstant.robotNumber);
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_VERSION, RobotBaseInfoConstant.version);
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_PRODUCT_DATE, RobotBaseInfoConstant.productDate);
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_SERIAL_NUMBER, RobotBaseInfoConstant.serialNumber);
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_WEB_VERSION, RobotBaseInfoConstant.webVersion);
            RedisUtil.hset(ROBOT_INFO_MAP, ROBOT_INFO_MAP_WEB_PUBLISH, RobotBaseInfoConstant.robotWebPublishDate);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 初始化机器人基础信息
     * 只有在系统重启时，执行该操作
     */
    @Override
    public void initRobotBaseInfo() {
        getBaseInfoFromRobot();
        catchBaseInfoToRedis();
    }
}
